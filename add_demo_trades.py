#!/usr/bin/env python3
"""
Add Demo Trades to Paper Trading Portfolio
This script adds some sample trades so you can see how the portfolio tracking works
"""

import sys
import os
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from trading.paper_trading import PaperTradingEngine
    from trading.robinhood_client import OrderSide
    from data.market_data import MarketDataProvider
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the AWOT directory")
    sys.exit(1)

def add_demo_trades():
    """Add some demo trades to the paper trading portfolio"""
    print("🎯 Adding Demo Trades to Paper Trading Portfolio")
    print("=" * 60)
    
    try:
        # Initialize paper trading engine
        paper_engine = PaperTradingEngine()
        
        print("📊 Initial Portfolio Status:")
        initial_portfolio = paper_engine.get_portfolio_summary()
        print(f"  Cash: ${initial_portfolio['cash']:,.2f}")
        print(f"  Positions: {initial_portfolio['open_positions']}")
        
        # Add some demo stock trades
        demo_trades = [
            {
                'symbol': 'AAPL',
                'action': 'buy',
                'quantity': 10,
                'price': 211.50,
                'position_type': 'stock'
            },
            {
                'symbol': 'TSLA', 
                'action': 'buy',
                'quantity': 5,
                'price': 330.25,
                'position_type': 'stock'
            },
            {
                'symbol': 'MSFT',
                'action': 'buy', 
                'quantity': 8,
                'price': 410.75,
                'position_type': 'stock'
            }
        ]
        
        print(f"\n🎯 Adding {len(demo_trades)} demo trades...")
        
        for i, trade in enumerate(demo_trades, 1):
            try:
                # Execute the trade using place_order
                result = paper_engine.place_order(
                    symbol=trade['symbol'],
                    quantity=trade['quantity'],
                    side=OrderSide.BUY,  # All demo trades are buys
                    price=trade['price'],
                    order_type=trade['position_type']
                )

                if result['success']:
                    print(f"  ✅ Trade {i}: BUY {trade['quantity']} {trade['symbol']} @ ${trade['price']:.2f}")
                else:
                    print(f"  ❌ Trade {i} failed: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"  ❌ Trade {i} error: {e}")
        
        # Update portfolio with some price changes to show P&L
        print(f"\n📈 Simulating price changes...")
        
        price_updates = {
            'AAPL': 213.25,  # +$1.75 gain
            'TSLA': 328.50,  # -$1.75 loss  
            'MSFT': 412.00   # +$1.25 gain
        }
        
        for symbol, new_price in price_updates.items():
            try:
                # Update position prices (this simulates market movement)
                for position in paper_engine.positions.values():
                    if position.symbol == symbol and position.is_open:
                        position.current_price = new_price
                        print(f"  📊 {symbol}: ${position.entry_price:.2f} → ${new_price:.2f}")
            except Exception as e:
                print(f"  ⚠️ Error updating {symbol}: {e}")
        
        # Update performance metrics
        paper_engine._update_performance_metrics()
        
        # Show final portfolio status
        print(f"\n📊 Final Portfolio Status:")
        final_portfolio = paper_engine.get_portfolio_summary()
        
        print(f"  Initial Capital: ${final_portfolio['initial_capital']:,.2f}")
        print(f"  Current Value:   ${final_portfolio['portfolio_value']:,.2f}")
        print(f"  Available Cash:  ${final_portfolio['cash']:,.2f}")
        print(f"  Total P&L:       ${final_portfolio['total_pnl']:,.2f}")
        print(f"  Unrealized P&L:  ${final_portfolio['unrealized_pnl']:,.2f}")
        print(f"  Open Positions:  {final_portfolio['open_positions']}")
        
        # Show positions
        positions = paper_engine.get_positions()
        open_positions = [p for p in positions if p['is_open']]
        
        if open_positions:
            print(f"\n📈 Current Positions:")
            print("  Symbol | Qty | Entry Price | Current Price | P&L")
            print("  " + "-" * 50)
            
            for pos in open_positions:
                pnl = pos['unrealized_pnl']
                pnl_sign = "+" if pnl >= 0 else ""
                print(f"  {pos['symbol']:6} | {pos['quantity']:3} | ${pos['entry_price']:>9.2f} | ${pos['current_price']:>12.2f} | {pnl_sign}${pnl:.2f}")
        
        print(f"\n🎉 Demo trades added successfully!")
        print(f"\n📱 Next Steps:")
        print("  1. Open your dashboard: http://localhost:8502")
        print("  2. Check the Portfolio tab - you should now see real data!")
        print("  3. View positions and P&L in real-time")
        print("  4. Check the Trade Log tab for trade history")
        
        # Save the state
        try:
            filename = f"demo_portfolio_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            paper_engine.save_state(filename)
            print(f"  5. Portfolio saved to: {filename}")
        except Exception as e:
            print(f"  ⚠️ Could not save portfolio: {e}")
        
    except Exception as e:
        print(f"❌ Error adding demo trades: {e}")
        print("\n🔧 Troubleshooting:")
        print("  1. Make sure you're in the AWOT directory")
        print("  2. Activate virtual environment: source venv/bin/activate")
        print("  3. Check if paper trading engine is working")

def main():
    """Main function"""
    print("🎯 AWOT Paper Trading Demo Setup")
    print("This will add some demo trades to your paper portfolio")
    print("so you can see how the portfolio tracking works.\n")
    
    response = input("Do you want to add demo trades? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        add_demo_trades()
    else:
        print("❌ Demo trades not added")
        print("\n💡 To see portfolio data in your dashboard:")
        print("  1. You can manually place trades through the Live Trading tab")
        print("  2. Or run this script again to add demo data")

if __name__ == "__main__":
    main()
