# Enhanced Algorithm Configuration
# Adjust these settings to fine-tune performance

[signal_generation]
# Minimum confidence threshold (0.5-0.9)
min_confidence_threshold = 0.7

# Volume threshold multiplier (1.0-3.0)
volume_threshold = 1.5

# Risk/reward ratio minimum (1.0-3.0)
min_risk_reward_ratio = 1.5

# Timeframe confirmations required (1-3)
required_timeframe_confirmations = 2

[trading_hours]
# Optimal trading windows (hour, minute, hour, minute)
morning_breakout = 9, 30, 10, 30
midday_momentum = 11, 0, 14, 0
power_hour = 15, 0, 16, 0

[risk_management]
# Dynamic stop loss multipliers
low_volatility_stop = 1.5
medium_volatility_stop = 2.0
high_volatility_stop = 2.5

# Dynamic take profit multipliers
low_volatility_target = 3.0
medium_volatility_target = 3.5
high_volatility_target = 4.0

[performance_tracking]
# Enable signal performance learning
track_signal_performance = true

# Minimum signals before performance adjustment
min_signals_for_adjustment = 10

# Performance adjustment range
max_positive_adjustment = 0.15
max_negative_adjustment = -0.10
