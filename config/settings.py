import os
from dotenv import load_dotenv

load_dotenv()

# Trading Configuration
INITIAL_CAPITAL = 50000
RISK_PER_TRADE = 0.10  # 10% of account per trade
MAX_POSITIONS = 3  # Maximum concurrent positions

# Robinhood Configuration
ROBINHOOD_USERNAME = os.getenv('ROBINHOOD_USERNAME')
ROBINHOOD_PASSWORD = os.getenv('ROBINHOOD_PASSWORD')

# Data Sources
ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY')
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

# Strategy Parameters
BREAKOUT_VOLUME_THRESHOLD = 2.0  # 2x average volume
RSI_OVERSOLD = 30
RSI_OVERBOUGHT = 70
PROFIT_TARGET = 1.0  # 100% gain
STOP_LOSS = 0.5  # 50% loss