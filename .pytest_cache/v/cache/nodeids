["tests/test_technical_indicators.py::TestTechnicalIndicators::test_calculate_all_indicators_empty_data", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_calculate_all_indicators_missing_columns", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_calculate_all_indicators_success", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_detect_patterns", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_detect_patterns_insufficient_data", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_double_bottom_detection", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_get_signal_strength", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_get_signal_strength_empty_data", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_indicator_consistency", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_indicator_values_realistic", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_momentum_indicators", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_moving_averages", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_options_indicators", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_volatility_indicators", "tests/test_technical_indicators.py::TestTechnicalIndicators::test_volume_indicators"]