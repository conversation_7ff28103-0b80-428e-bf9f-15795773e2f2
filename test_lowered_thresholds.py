#!/usr/bin/env python3
'''
Test Lowered Signal Thresholds (Option B)
This tests if lowering thresholds generates real signals
'''

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Set environment variable to force signal generation
os.environ['FORCE_SIGNAL_GENERATION'] = 'true'

from signals.signal_generator import SignalGenerator
from signals.enhanced_signal_generator import EnhancedSignalGenerator
from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/lowered_thresholds_test.log'),
        logging.StreamHandler()
    ]
)

class LoweredThresholdTester:
    """Test signal generation with lowered thresholds"""
    
    def __init__(self):
        self.basic_generator = SignalGenerator()
        self.enhanced_generator = EnhancedSignalGenerator()
        self.paper_engine = PaperTradingEngine()
        self.symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL']
        
        # Load existing portfolio
        if os.path.exists('current_portfolio_state.json'):
            try:
                self.paper_engine.load_state('current_portfolio_state.json')
                portfolio = self.paper_engine.get_portfolio_summary()
                logging.info(f"✅ Loaded portfolio: ${portfolio['portfolio_value']:,.2f}")
            except Exception as e:
                logging.error(f"Error loading portfolio: {e}")
    
    def test_basic_signals_with_lowered_thresholds(self):
        """Test basic signal generation with lowered thresholds"""
        logging.info("\n🔍 TESTING BASIC SIGNALS WITH LOWERED THRESHOLDS")
        logging.info("=" * 60)
        
        total_signals = 0
        
        for symbol in self.symbols:
            try:
                logging.info(f"\n📊 Testing {symbol}...")
                signals = self.basic_generator.generate_signals(symbol)
                
                if signals:
                    logging.info(f"✅ Generated {len(signals)} signals for {symbol}")
                    total_signals += len(signals)
                    
                    for signal in signals:
                        logging.info(f"   📈 {signal.symbol}: {signal.signal_type} - {signal.confidence:.2%}")
                else:
                    logging.info(f"❌ No signals for {symbol}")
                    
            except Exception as e:
                logging.error(f"❌ Error generating signals for {symbol}: {e}")
        
        logging.info(f"\n📊 Total basic signals with lowered thresholds: {total_signals}")
        return total_signals
    
    def test_enhanced_signals_with_lowered_thresholds(self):
        """Test enhanced signal generation with lowered thresholds"""
        logging.info("\n🔍 TESTING ENHANCED SIGNALS WITH LOWERED THRESHOLDS")
        logging.info("=" * 60)
        
        total_signals = 0
        
        for symbol in self.symbols:
            try:
                logging.info(f"\n📊 Testing enhanced signals for {symbol}...")
                signals = self.enhanced_generator.generate_signals(symbol)
                
                if signals:
                    logging.info(f"✅ Generated {len(signals)} enhanced signals for {symbol}")
                    total_signals += len(signals)
                    
                    for signal in signals:
                        logging.info(f"   📈 {signal.symbol}: {signal.signal_type} - {signal.confidence:.2%}")
                else:
                    logging.info(f"❌ No enhanced signals for {symbol}")
                    
            except Exception as e:
                logging.error(f"❌ Error generating enhanced signals for {symbol}: {e}")
        
        logging.info(f"\n📊 Total enhanced signals with lowered thresholds: {total_signals}")
        return total_signals
    
    def test_signal_execution(self, signals):
        """Test executing signals with lowered thresholds"""
        if not signals:
            logging.info("❌ No signals to execute")
            return 0
        
        logging.info(f"\n🎯 TESTING SIGNAL EXECUTION WITH {len(signals)} SIGNALS")
        logging.info("=" * 60)
        
        trades_executed = 0
        confidence_threshold = float(os.getenv('SIGNAL_CONFIDENCE_THRESHOLD', '0.5'))
        
        for signal in signals:
            logging.info(f"\n📊 Processing signal: {signal.symbol}")
            logging.info(f"   Confidence: {signal.confidence:.2%}")
            logging.info(f"   Threshold: {confidence_threshold:.0%}")
            
            if signal.confidence >= confidence_threshold:
                logging.info(f"   ✅ Signal meets lowered threshold")
                
                # Execute a small test trade
                try:
                    # Determine action
                    signal_type_str = str(signal.signal_type.value).lower()
                    
                    if 'bullish' in signal_type_str:
                        side = OrderSide.BUY
                        action = 'BUY'
                    elif 'bearish' in signal_type_str:
                        side = OrderSide.SELL
                        action = 'SELL'
                    else:
                        logging.info(f"   📊 Neutral signal, skipping")
                        continue
                    
                    # Use small test quantities
                    quantity = 2
                    price = getattr(signal, 'entry_price', 200.0)
                    
                    # For SELL, check if we have position
                    if action == 'SELL':
                        positions = self.paper_engine.get_positions()
                        has_position = any(pos['symbol'] == signal.symbol and pos['is_open'] for pos in positions)
                        
                        if not has_position:
                            logging.info(f"   ⚠️ No position to sell, converting to BUY")
                            side = OrderSide.BUY
                            action = 'BUY'
                    
                    # Execute trade
                    result = self.paper_engine.place_order(
                        symbol=signal.symbol,
                        quantity=quantity,
                        side=side,
                        price=price,
                        order_type='stock'
                    )
                    
                    if result.get('success'):
                        logging.info(f"   ✅ SUCCESS: {action} {quantity} {signal.symbol}")
                        trades_executed += 1
                        
                        # Save state
                        self.paper_engine.save_state('current_portfolio_state.json')
                    else:
                        logging.error(f"   ❌ FAILED: {result.get('error')}")
                        
                except Exception as e:
                    logging.error(f"   ❌ Error executing signal: {e}")
            else:
                logging.info(f"   ❌ Below lowered threshold ({signal.confidence:.2%} < {confidence_threshold:.0%})")
        
        return trades_executed
    
    def run_comprehensive_test(self):
        """Run comprehensive test of lowered thresholds"""
        logging.info("🔍 COMPREHENSIVE LOWERED THRESHOLDS TEST")
        logging.info("=" * 60)
        
        # Show current thresholds
        logging.info(f"📊 Current Configuration:")
        logging.info(f"   Signal confidence threshold: {os.getenv('SIGNAL_CONFIDENCE_THRESHOLD', '0.5')}")
        logging.info(f"   Min RSI threshold: {os.getenv('MIN_RSI_THRESHOLD', '25')}")
        logging.info(f"   Max RSI threshold: {os.getenv('MAX_RSI_THRESHOLD', '75')}")
        logging.info(f"   Min volume ratio: {os.getenv('MIN_VOLUME_RATIO', '1.0')}")
        
        # Test basic signals
        basic_signals_count = self.test_basic_signals_with_lowered_thresholds()
        
        # Test enhanced signals
        enhanced_signals_count = self.test_enhanced_signals_with_lowered_thresholds()
        
        # If we got signals, test execution
        total_signals = basic_signals_count + enhanced_signals_count
        
        if total_signals > 0:
            logging.info(f"\n🎯 Found {total_signals} signals with lowered thresholds!")
            
            # Generate one set of signals for execution test
            test_signals = []
            try:
                for symbol in self.symbols[:2]:  # Test first 2 symbols
                    signals = self.basic_generator.generate_signals(symbol)
                    test_signals.extend(signals)
            except:
                pass
            
            if test_signals:
                trades_executed = self.test_signal_execution(test_signals)
                logging.info(f"\n✅ Executed {trades_executed} trades from lowered threshold signals")
            else:
                logging.info("\n⚠️ No signals available for execution test")
        else:
            logging.info(f"\n❌ Still no signals even with lowered thresholds")
        
        return total_signals

def main():
    """Main test function"""
    print("🔍 TESTING LOWERED SIGNAL THRESHOLDS (OPTION B)")
    print("=" * 60)
    print("This tests if lowering signal thresholds generates real signals")
    print()
    
    tester = LoweredThresholdTester()
    total_signals = tester.run_comprehensive_test()
    
    print("\n" + "=" * 60)
    print("🎯 LOWERED THRESHOLDS TEST RESULTS:")
    
    if total_signals > 0:
        print(f"✅ SUCCESS: Generated {total_signals} signals with lowered thresholds!")
        print()
        print("🎯 WHAT THIS MEANS:")
        print("✅ Lowering thresholds allows real signal generation")
        print("✅ Your automated trading can now use real signals")
        print("✅ The system will generate more trading opportunities")
        print()
        print("📊 Check your dashboard for new trades!")
        
    else:
        print("❌ STILL NO SIGNALS")
        print()
        print("🔍 Even with lowered thresholds, no signals were generated")
        print("This suggests the signal generation logic needs deeper tuning")
        print("Proceed to Option C for custom signal tuning")
    
    print("\n💡 NEXT STEPS:")
    print("1. Check dashboard at http://localhost:8502")
    print("2. Review logs/lowered_thresholds_test.log")
    print("3. If successful, use real signals in automated trading")
    print("4. If not successful, proceed to Option C")

if __name__ == "__main__":
    main()
