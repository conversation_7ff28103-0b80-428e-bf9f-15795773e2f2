#!/usr/bin/env python3
"""Debug position sizing calculation"""

# Test the calculation
portfolio_value = 50000
max_position_size = 0.10
price = 213.25

max_value = portfolio_value * max_position_size
print(f'Portfolio value: ${portfolio_value}')
print(f'Max position size: {max_position_size * 100}%')
print(f'Max value for position: ${max_value}')

quantity = max(1, int(max_value / price))
print(f'Price per share: ${price}')
print(f'Calculated quantity: {quantity}')

required_cash = quantity * price
print(f'Required cash: ${required_cash}')

# Test with smaller quantity
small_quantity = 10
small_cost = small_quantity * price
print(f'\nTest with {small_quantity} shares: ${small_cost}')

# Test with even smaller
tiny_quantity = 5
tiny_cost = tiny_quantity * price
print(f'Test with {tiny_quantity} shares: ${tiny_cost}')
