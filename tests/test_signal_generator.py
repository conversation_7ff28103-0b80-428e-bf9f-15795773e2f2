import unittest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from signals.signal_generator import (
    SignalGenerator, TradingSignal, SignalType, SignalStrength
)


class TestTradingSignal(unittest.TestCase):
    """Test cases for TradingSignal class"""
    
    def test_trading_signal_creation(self):
        """Test TradingSignal object creation"""
        signal = TradingSignal(
            symbol="AAPL",
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=150.0,
            target_price=165.0,
            stop_loss_price=140.0,
            expiration_date="2024-01-15",
            option_type="call",
            strike_price=155.0,
            reasoning="Strong momentum with high volume"
        )
        
        self.assertEqual(signal.symbol, "AAPL")
        self.assertEqual(signal.signal_type, SignalType.MOMENTUM_BULLISH)
        self.assertEqual(signal.strength, SignalStrength.STRONG)
        self.assertEqual(signal.confidence, 0.8)
        self.assertEqual(signal.entry_price, 150.0)
        self.assertEqual(signal.target_price, 165.0)
        self.assertEqual(signal.stop_loss_price, 140.0)
        self.assertEqual(signal.option_type, "call")
        self.assertEqual(signal.strike_price, 155.0)
        
        # Test risk-reward ratio calculation
        expected_rr = (165.0 - 150.0) / (150.0 - 140.0)
        self.assertAlmostEqual(signal.risk_reward_ratio, expected_rr, places=2)
    
    def test_trading_signal_to_dict(self):
        """Test TradingSignal to_dict method"""
        signal = TradingSignal(
            symbol="TSLA",
            signal_type=SignalType.BREAKOUT_BULLISH,
            strength=SignalStrength.VERY_STRONG,
            confidence=0.9,
            entry_price=200.0,
            target_price=240.0,
            stop_loss_price=180.0,
            expiration_date="2024-01-20"
        )
        
        signal_dict = signal.to_dict()
        
        # Check all required fields are present
        required_fields = [
            'symbol', 'signal_type', 'strength', 'confidence',
            'entry_price', 'target_price', 'stop_loss_price',
            'expiration_date', 'timestamp', 'risk_reward_ratio'
        ]
        
        for field in required_fields:
            self.assertIn(field, signal_dict)
        
        self.assertEqual(signal_dict['symbol'], "TSLA")
        self.assertEqual(signal_dict['signal_type'], SignalType.BREAKOUT_BULLISH.value)
        self.assertEqual(signal_dict['strength'], SignalStrength.VERY_STRONG.value)


class TestSignalGenerator(unittest.TestCase):
    """Test cases for SignalGenerator class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.signal_generator = SignalGenerator()
        
        # Create sample market data
        self.sample_market_data = {
            'symbol': 'AAPL',
            'timestamp': datetime.now(),
            'price_data': {
                'current_price': 150.0,
                'previous_close': 148.0,
                'volume': 50000000,
                'avg_volume': 30000000
            },
            'volume_analysis': {
                'volume_ratio': 1.67,
                'unusual_volume': True,
                'high_volume_breakout': True
            },
            'options_flow': {
                'pc_ratio_volume': 0.8,
                'high_activity': False
            },
            'historical_data': self._create_sample_historical_data()
        }
    
    def _create_sample_historical_data(self):
        """Create sample historical data for testing"""
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        base_price = 145
        prices = [base_price]
        
        for _ in range(49):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        data = []
        for i, date in enumerate(dates):
            data.append({
                'Open': prices[i] * np.random.uniform(0.99, 1.01),
                'High': prices[i] * np.random.uniform(1.00, 1.03),
                'Low': prices[i] * np.random.uniform(0.97, 1.00),
                'Close': prices[i],
                'Volume': np.random.randint(20000000, 60000000)
            })
        
        return data
    
    @patch('src.signals.signal_generator.SignalGenerator._generate_momentum_signals')
    @patch('src.signals.signal_generator.SignalGenerator._generate_breakout_signals')
    @patch('src.signals.signal_generator.SignalGenerator._generate_volatility_signals')
    @patch('src.signals.signal_generator.SignalGenerator._generate_reversal_signals')
    @patch('src.signals.signal_generator.SignalGenerator._generate_event_driven_signals')
    def test_generate_signals_success(self, mock_event, mock_reversal, mock_volatility, 
                                    mock_breakout, mock_momentum):
        """Test successful signal generation"""
        # Mock the individual signal generators
        mock_momentum.return_value = [self._create_mock_signal(SignalType.MOMENTUM_BULLISH)]
        mock_breakout.return_value = [self._create_mock_signal(SignalType.BREAKOUT_BULLISH)]
        mock_volatility.return_value = []
        mock_reversal.return_value = []
        mock_event.return_value = []
        
        # Mock market data provider
        with patch.object(self.signal_generator.market_data, 'get_comprehensive_market_data') as mock_data:
            mock_data.return_value = self.sample_market_data
            
            # Mock technical indicators
            with patch.object(self.signal_generator.technical_indicators, 'calculate_all_indicators') as mock_indicators:
                mock_indicators.return_value = pd.DataFrame(self.sample_market_data['historical_data'])
                
                signals = self.signal_generator.generate_signals("AAPL")
                
                self.assertIsInstance(signals, list)
                self.assertGreater(len(signals), 0)
                
                # Check that all signal generators were called
                mock_momentum.assert_called_once()
                mock_breakout.assert_called_once()
    
    def _create_mock_signal(self, signal_type):
        """Create a mock signal for testing"""
        return TradingSignal(
            symbol="AAPL",
            signal_type=signal_type,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=150.0,
            target_price=165.0,
            stop_loss_price=140.0,
            expiration_date="2024-01-15"
        )
    
    def test_generate_signals_no_data(self):
        """Test signal generation with no market data"""
        with patch.object(self.signal_generator.market_data, 'get_comprehensive_market_data') as mock_data:
            mock_data.return_value = {}
            
            signals = self.signal_generator.generate_signals("INVALID")
            
            self.assertEqual(signals, [])
    
    def test_generate_signals_empty_historical_data(self):
        """Test signal generation with empty historical data"""
        empty_market_data = self.sample_market_data.copy()
        empty_market_data['historical_data'] = []
        
        with patch.object(self.signal_generator.market_data, 'get_comprehensive_market_data') as mock_data:
            mock_data.return_value = empty_market_data
            
            signals = self.signal_generator.generate_signals("AAPL")
            
            self.assertEqual(signals, [])
    
    def test_filter_and_rank_signals(self):
        """Test signal filtering and ranking"""
        # Create signals with different qualities
        signals = [
            TradingSignal("AAPL", SignalType.MOMENTUM_BULLISH, SignalStrength.WEAK, 
                         0.4, 150, 160, 145, "2024-01-15"),  # Low confidence - should be filtered
            TradingSignal("AAPL", SignalType.BREAKOUT_BULLISH, SignalStrength.STRONG, 
                         0.8, 150, 180, 140, "2024-01-15"),  # High quality
            TradingSignal("AAPL", SignalType.REVERSAL_BULLISH, SignalStrength.MODERATE, 
                         0.6, 150, 165, 145, "2024-01-15"),  # Medium quality
        ]
        
        filtered_signals = self.signal_generator._filter_and_rank_signals(signals)
        
        # Should filter out low confidence signal
        self.assertEqual(len(filtered_signals), 2)
        
        # Should be ranked by quality (highest first)
        self.assertEqual(filtered_signals[0].signal_type, SignalType.BREAKOUT_BULLISH)
        self.assertEqual(filtered_signals[1].signal_type, SignalType.REVERSAL_BULLISH)
    
    def test_filter_and_rank_signals_empty(self):
        """Test signal filtering with empty list"""
        filtered_signals = self.signal_generator._filter_and_rank_signals([])
        self.assertEqual(filtered_signals, [])
    
    def test_momentum_signal_generation(self):
        """Test momentum signal generation logic"""
        # Create indicators DataFrame with momentum conditions
        indicators_data = {
            'RSI': [25],  # Oversold
            'MACD_Bullish': [1],  # Bullish MACD
            'SMA_Cross_5_20': [1],  # Bullish MA cross
            'EMA_Cross_5_20': [1],  # Bullish EMA cross
            'High_Volume': [1]  # High volume
        }
        indicators_df = pd.DataFrame(indicators_data)
        
        signals = self.signal_generator._generate_momentum_signals(
            "AAPL", indicators_df, self.sample_market_data
        )
        
        self.assertIsInstance(signals, list)
        # Should generate at least one momentum signal with these conditions
        if signals:
            self.assertEqual(signals[0].signal_type, SignalType.MOMENTUM_BULLISH)
    
    def test_breakout_signal_generation(self):
        """Test breakout signal generation logic"""
        # Create indicators DataFrame with breakout conditions
        indicators_data = {
            'Breakout_High': [1],  # Price breakout
            'ADX_Strong_Trend': [1],  # Strong trend
            'BB_Position': [1.1],  # Above Bollinger Band
            'Vol_Expansion': [1]  # Volatility expansion
        }
        indicators_df = pd.DataFrame(indicators_data)
        
        # Add volume analysis indicating unusual volume
        market_data_with_volume = self.sample_market_data.copy()
        market_data_with_volume['volume_analysis']['unusual_volume'] = True
        
        signals = self.signal_generator._generate_breakout_signals(
            "AAPL", indicators_df, market_data_with_volume
        )
        
        self.assertIsInstance(signals, list)
        # Should generate breakout signal with these conditions
        if signals:
            self.assertEqual(signals[0].signal_type, SignalType.BREAKOUT_BULLISH)
    
    def test_volatility_signal_generation(self):
        """Test volatility signal generation logic"""
        # Create indicators DataFrame with volatility expansion
        indicators_data = {
            'Vol_Expansion': [1],
            'BB_Width': [0.15]  # Current BB width
        }
        indicators_df = pd.DataFrame(indicators_data)
        
        # Add rolling mean for BB_Width comparison
        indicators_df['BB_Width_Mean'] = [0.10]  # Historical mean
        
        signals = self.signal_generator._generate_volatility_signals(
            "AAPL", indicators_df, self.sample_market_data
        )
        
        self.assertIsInstance(signals, list)
    
    def test_reversal_signal_generation(self):
        """Test reversal signal generation logic"""
        # Create indicators DataFrame with reversal conditions
        indicators_data = {
            'RSI': [20],  # Extremely oversold
            'Williams_R': [-85],  # Oversold
            'Near_Support': [1],  # Near support level
            'Support_20': [145]  # Support level
        }
        indicators_df = pd.DataFrame(indicators_data)
        
        signals = self.signal_generator._generate_reversal_signals(
            "AAPL", indicators_df, self.sample_market_data
        )
        
        self.assertIsInstance(signals, list)
        # Should generate reversal signal with these extreme oversold conditions
        if signals:
            self.assertEqual(signals[0].signal_type, SignalType.REVERSAL_BULLISH)
    
    def test_event_driven_signal_generation(self):
        """Test event-driven signal generation"""
        # Mock earnings calendar
        with patch.object(self.signal_generator.market_data, 'get_earnings_calendar') as mock_earnings:
            mock_earnings.return_value = [
                {'symbol': 'AAPL', 'reportDate': '2024-01-20'}
            ]
            
            signals = self.signal_generator._generate_event_driven_signals(
                "AAPL", self.sample_market_data
            )
            
            self.assertIsInstance(signals, list)
            if signals:
                self.assertEqual(signals[0].signal_type, SignalType.EVENT_DRIVEN)
    
    def test_signal_history_tracking(self):
        """Test that signal history is properly tracked"""
        initial_history_length = len(self.signal_generator.signal_history)
        
        # Mock successful signal generation
        with patch.object(self.signal_generator.market_data, 'get_comprehensive_market_data') as mock_data:
            mock_data.return_value = self.sample_market_data
            
            with patch.object(self.signal_generator.technical_indicators, 'calculate_all_indicators') as mock_indicators:
                mock_indicators.return_value = pd.DataFrame(self.sample_market_data['historical_data'])
                
                # Mock individual signal generators to return test signals
                with patch.object(self.signal_generator, '_generate_momentum_signals') as mock_momentum:
                    mock_momentum.return_value = [self._create_mock_signal(SignalType.MOMENTUM_BULLISH)]
                    
                    with patch.object(self.signal_generator, '_generate_breakout_signals') as mock_breakout:
                        mock_breakout.return_value = []
                        
                        with patch.object(self.signal_generator, '_generate_volatility_signals') as mock_vol:
                            mock_vol.return_value = []
                            
                            with patch.object(self.signal_generator, '_generate_reversal_signals') as mock_rev:
                                mock_rev.return_value = []
                                
                                with patch.object(self.signal_generator, '_generate_event_driven_signals') as mock_event:
                                    mock_event.return_value = []
                                    
                                    signals = self.signal_generator.generate_signals("AAPL")
                                    
                                    # History should have increased
                                    self.assertGreater(len(self.signal_generator.signal_history), initial_history_length)


if __name__ == '__main__':
    unittest.main()
