import unittest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from indicators.technical_indicators import TechnicalIndicators


class TestTechnicalIndicators(unittest.TestCase):
    """Test cases for TechnicalIndicators class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.indicators = TechnicalIndicators()
        
        # Create sample OHLCV data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)  # For reproducible tests
        
        # Generate realistic price data
        base_price = 100
        price_changes = np.random.normal(0, 0.02, 100)  # 2% daily volatility
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        self.sample_data = pd.DataFrame({
            'Open': [p * np.random.uniform(0.99, 1.01) for p in prices],
            'High': [p * np.random.uniform(1.00, 1.03) for p in prices],
            'Low': [p * np.random.uniform(0.97, 1.00) for p in prices],
            'Close': prices,
            'Volume': np.random.randint(1000000, 5000000, 100)
        }, index=dates)
        
        # Ensure High >= Close >= Low and High >= Open >= Low
        for i in range(len(self.sample_data)):
            row = self.sample_data.iloc[i]
            high = max(row['Open'], row['High'], row['Close'])
            low = min(row['Open'], row['Low'], row['Close'])
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('High')] = high
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('Low')] = low
    
    def test_calculate_all_indicators_success(self):
        """Test successful calculation of all indicators"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        
        # Check that result is not empty
        self.assertFalse(result.empty)
        
        # Check that original columns are preserved
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            self.assertIn(col, result.columns)
        
        # Check that key indicators are calculated
        expected_indicators = [
            'SMA_5', 'SMA_20', 'SMA_50', 'EMA_5', 'EMA_20',
            'RSI', 'MACD', 'MACD_Signal', 'BB_Upper', 'BB_Lower',
            'ATR', 'Volume_SMA_20', 'ADX'
        ]
        
        for indicator in expected_indicators:
            self.assertIn(indicator, result.columns, f"Missing indicator: {indicator}")
    
    def test_calculate_all_indicators_empty_data(self):
        """Test handling of empty data"""
        empty_data = pd.DataFrame()
        result = self.indicators.calculate_all_indicators(empty_data)
        
        self.assertTrue(result.empty)
    
    def test_calculate_all_indicators_missing_columns(self):
        """Test handling of missing required columns"""
        incomplete_data = self.sample_data[['Close', 'Volume']].copy()
        result = self.indicators.calculate_all_indicators(incomplete_data)
        
        self.assertTrue(result.empty)
    
    def test_moving_averages(self):
        """Test moving average calculations"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        
        # Check SMA calculations
        self.assertTrue(all(result['SMA_5'].dropna() > 0))
        self.assertTrue(all(result['SMA_20'].dropna() > 0))
        
        # Check that shorter MA is more responsive (generally)
        # SMA_5 should have more variation than SMA_20
        sma5_std = result['SMA_5'].std()
        sma20_std = result['SMA_20'].std()
        self.assertGreater(sma5_std, sma20_std * 0.8)  # Allow some tolerance
        
        # Check crossover signals
        self.assertIn('SMA_Cross_5_20', result.columns)
        self.assertTrue(all(result['SMA_Cross_5_20'].isin([0, 1])))
    
    def test_momentum_indicators(self):
        """Test momentum indicator calculations"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        
        # Test RSI
        rsi_values = result['RSI'].dropna()
        self.assertTrue(all(rsi_values >= 0))
        self.assertTrue(all(rsi_values <= 100))
        
        # Test MACD
        self.assertIn('MACD', result.columns)
        self.assertIn('MACD_Signal', result.columns)
        self.assertIn('MACD_Histogram', result.columns)
        
        # MACD histogram should be MACD - Signal
        macd_diff = result['MACD'] - result['MACD_Signal']
        histogram = result['MACD_Histogram']
        np.testing.assert_array_almost_equal(
            macd_diff.dropna().values, 
            histogram.dropna().values, 
            decimal=10
        )
    
    def test_volatility_indicators(self):
        """Test volatility indicator calculations"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        
        # Test Bollinger Bands
        bb_upper = result['BB_Upper'].dropna()
        bb_lower = result['BB_Lower'].dropna()
        bb_middle = result['BB_Middle'].dropna()
        close_prices = result['Close'].dropna()
        
        # Upper band should be above middle, middle above lower
        self.assertTrue(all(bb_upper >= bb_middle))
        self.assertTrue(all(bb_middle >= bb_lower))
        
        # Test ATR
        atr_values = result['ATR'].dropna()
        self.assertTrue(all(atr_values >= 0))
        
        # Test BB Position calculation
        bb_position = result['BB_Position'].dropna()
        # BB Position can be negative when price is below lower band
        self.assertTrue(all(bb_position >= -0.5))  # Allow some negative values
        self.assertTrue(all(bb_position <= 1.5))   # Allow some values above 1
    
    def test_volume_indicators(self):
        """Test volume indicator calculations"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        
        # Test volume ratio
        volume_ratio = result['Volume_Ratio'].dropna()
        self.assertTrue(all(volume_ratio >= 0))
        
        # Test high volume signals
        high_volume = result['High_Volume']
        self.assertTrue(all(high_volume.isin([0, 1])))
        
        # Test OBV
        self.assertIn('OBV', result.columns)
        obv_values = result['OBV'].dropna()
        self.assertTrue(len(obv_values) > 0)
    
    def test_get_signal_strength(self):
        """Test signal strength calculation"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        signal_strength = self.indicators.get_signal_strength(result)
        
        # Check that all expected signal types are present
        expected_signals = ['momentum', 'volatility', 'breakout', 'overall']
        for signal_type in expected_signals:
            self.assertIn(signal_type, signal_strength)
            self.assertGreaterEqual(signal_strength[signal_type], 0)
            self.assertLessEqual(signal_strength[signal_type], 1)
    
    def test_get_signal_strength_empty_data(self):
        """Test signal strength with empty data"""
        signal_strength = self.indicators.get_signal_strength(pd.DataFrame())
        self.assertEqual(signal_strength, {})
    
    def test_detect_patterns(self):
        """Test pattern detection"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        patterns = self.indicators.detect_patterns(result)
        
        # Should return a list
        self.assertIsInstance(patterns, list)
        
        # Each pattern should have required fields
        for pattern in patterns:
            self.assertIn('pattern', pattern)
            self.assertIn('confidence', pattern)
            self.assertIn('signal', pattern)
            self.assertIn('timeframe', pattern)
            
            # Confidence should be between 0 and 1
            self.assertGreaterEqual(pattern['confidence'], 0)
            self.assertLessEqual(pattern['confidence'], 1)
            
            # Signal should be bullish or bearish
            self.assertIn(pattern['signal'], ['bullish', 'bearish'])
    
    def test_detect_patterns_insufficient_data(self):
        """Test pattern detection with insufficient data"""
        small_data = self.sample_data.head(10)
        result = self.indicators.calculate_all_indicators(small_data)
        patterns = self.indicators.detect_patterns(result)
        
        # Should return empty list for insufficient data
        self.assertEqual(patterns, [])
    
    def test_double_bottom_detection(self):
        """Test double bottom pattern detection"""
        # Create data with a clear double bottom pattern
        double_bottom_data = self.sample_data.copy()
        
        # Manually create double bottom in the last 20 periods
        for i in range(-20, 0):
            if i in [-15, -5]:  # Two bottoms
                double_bottom_data.iloc[i, double_bottom_data.columns.get_loc('Low')] = 95
                double_bottom_data.iloc[i, double_bottom_data.columns.get_loc('Close')] = 96
            elif i in [-10]:  # Peak between bottoms
                double_bottom_data.iloc[i, double_bottom_data.columns.get_loc('High')] = 105
                double_bottom_data.iloc[i, double_bottom_data.columns.get_loc('Close')] = 104
        
        result = self.indicators.calculate_all_indicators(double_bottom_data)
        patterns = self.indicators.detect_patterns(result)
        
        # Should detect some pattern (not necessarily double bottom due to complexity)
        self.assertIsInstance(patterns, list)
    
    def test_options_indicators(self):
        """Test options-specific indicators"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        
        # Test price change calculations
        self.assertIn('Price_Change_1D', result.columns)
        self.assertIn('Price_Change_5D', result.columns)
        self.assertIn('Price_Change_20D', result.columns)
        
        # Test volatility expansion
        self.assertIn('ATR_Expansion', result.columns)
        self.assertIn('Vol_Expansion', result.columns)
        
        # Test breakout signals
        self.assertIn('Breakout_High', result.columns)
        self.assertIn('Breakout_Low', result.columns)
        
        # Test support/resistance levels
        self.assertIn('Resistance_20', result.columns)
        self.assertIn('Support_20', result.columns)
        
        # Resistance should be >= Support
        resistance = result['Resistance_20'].dropna()
        support = result['Support_20'].dropna()
        
        # Align the series for comparison
        min_len = min(len(resistance), len(support))
        if min_len > 0:
            self.assertTrue(all(resistance.iloc[-min_len:].values >= support.iloc[-min_len:].values))
    
    def test_indicator_consistency(self):
        """Test that indicators are consistent across multiple runs"""
        result1 = self.indicators.calculate_all_indicators(self.sample_data)
        result2 = self.indicators.calculate_all_indicators(self.sample_data)
        
        # Results should be identical
        pd.testing.assert_frame_equal(result1, result2)
    
    def test_indicator_values_realistic(self):
        """Test that indicator values are within realistic ranges"""
        result = self.indicators.calculate_all_indicators(self.sample_data)
        
        # RSI should be between 0 and 100
        rsi = result['RSI'].dropna()
        self.assertTrue(all(rsi >= 0))
        self.assertTrue(all(rsi <= 100))
        
        # ATR should be positive
        atr = result['ATR'].dropna()
        self.assertTrue(all(atr >= 0))
        
        # Volume ratio should be positive
        vol_ratio = result['Volume_Ratio'].dropna()
        self.assertTrue(all(vol_ratio >= 0))
        
        # BB Position should be between 0 and 1 (mostly)
        bb_pos = result['BB_Position'].dropna()
        # Allow some values outside 0-1 for extreme cases
        self.assertTrue(all(bb_pos >= -0.5))
        self.assertTrue(all(bb_pos <= 1.5))


if __name__ == '__main__':
    unittest.main()
