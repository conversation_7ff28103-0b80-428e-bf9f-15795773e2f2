#!/usr/bin/env python3
"""
AWOT Trading Platform - Security Test Suite
Tests for security vulnerabilities, authentication, and data protection
"""

import unittest
import sys
import os
import tempfile
import json
from unittest.mock import patch, Mock
import hashlib
import base64

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from trading.robinhood_client import RobinhoodClient
from notifications.notification_manager import NotificationManager


class TestCredentialSecurity(unittest.TestCase):
    """Test credential handling and security"""
    
    def test_no_hardcoded_credentials(self):
        """Test that no credentials are hardcoded in the codebase"""
        # Check key files for hardcoded credentials
        sensitive_patterns = [
            'password=',
            'api_key=',
            'secret=',
            'token=',
            'username=',
            'AKIA',  # AWS access key pattern
            'sk_',   # Stripe secret key pattern
        ]
        
        # Files to check
        files_to_check = [
            'src/trading/robinhood_client.py',
            'src/notifications/notification_manager.py',
            'src/data/market_data.py'
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    content = f.read().lower()
                    
                    for pattern in sensitive_patterns:
                        # Allow patterns in comments or as variable names
                        lines = content.split('\n')
                        for line_num, line in enumerate(lines, 1):
                            if pattern in line and not line.strip().startswith('#'):
                                # Check if it's just a variable assignment without actual value
                                if '=' in line:
                                    parts = line.split('=', 1)
                                    if len(parts) > 1:
                                        value = parts[1].strip()
                                        # Allow environment variable references and None
                                        if not (value.startswith('os.getenv') or 
                                               value.startswith('None') or
                                               value.startswith('""') or
                                               value.startswith("''")):
                                            self.fail(f"Potential hardcoded credential in {file_path}:{line_num}: {line.strip()}")
    
    def test_environment_variable_usage(self):
        """Test that credentials are loaded from environment variables"""
        client = RobinhoodClient()
        
        # These should be None or loaded from environment
        self.assertIsInstance(client.username, (type(None), str))
        self.assertIsInstance(client.password, (type(None), str))
        self.assertIsInstance(client.totp_code, (type(None), str))
        
        # If credentials are set, they should not be obvious test values
        if client.username:
            self.assertNotIn('test', client.username.lower())
            self.assertNotIn('demo', client.username.lower())
        
        if client.password:
            self.assertNotEqual(client.password, 'password')
            self.assertNotEqual(client.password, '123456')
    
    def test_sensitive_data_not_logged(self):
        """Test that sensitive data is not logged"""
        # Mock logger to capture log messages
        with patch('loguru.logger') as mock_logger:
            client = RobinhoodClient()
            
            # Simulate authentication attempt
            client.username = "test_user"
            client.password = "test_password"
            client.authenticate()
            
            # Check that password is not in any log calls
            for call in mock_logger.info.call_args_list:
                args = str(call)
                self.assertNotIn('test_password', args)
                self.assertNotIn('password', args.lower())


class TestDataProtection(unittest.TestCase):
    """Test data protection and encryption"""
    
    def test_configuration_file_security(self):
        """Test that configuration files don't contain sensitive data"""
        config_files = [
            '.env.template',
            'config/settings.py'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    content = f.read()
                    
                    # Should not contain actual credentials
                    self.assertNotIn('password123', content)
                    self.assertNotIn('secret123', content)
                    self.assertNotIn('AKIA', content)  # AWS keys
                    
                    # Should contain placeholder text
                    if config_file.endswith('.template'):
                        self.assertIn('your_', content.lower())
                        self.assertIn('_here', content.lower())
    
    def test_temporary_file_security(self):
        """Test that temporary files are handled securely"""
        from trading.paper_trading import PaperTradingEngine
        
        engine = PaperTradingEngine()
        
        # Create a temporary file for state saving
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
            temp_filename = temp_file.name
        
        try:
            # Save state to temporary file
            engine.save_state(temp_filename)
            
            # Check file permissions (should not be world-readable)
            file_stat = os.stat(temp_filename)
            file_mode = file_stat.st_mode
            
            # Check that file is not world-readable (others don't have read permission)
            world_readable = bool(file_mode & 0o004)
            self.assertFalse(world_readable, "Temporary file should not be world-readable")
            
        finally:
            # Clean up
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
    
    def test_api_key_masking(self):
        """Test that API keys are properly masked in logs and outputs"""
        from data.market_data import MarketDataProvider
        
        # Mock an API key
        test_api_key = "ABCD1234567890"
        
        with patch.dict(os.environ, {'ALPHA_VANTAGE_API_KEY': test_api_key}):
            provider = MarketDataProvider()
            
            # The API key should not appear in string representation
            provider_str = str(provider.__dict__)
            self.assertNotIn(test_api_key, provider_str)


class TestInputValidation(unittest.TestCase):
    """Test input validation and sanitization"""
    
    def test_sql_injection_prevention(self):
        """Test prevention of SQL injection attacks"""
        from trading.paper_trading import PaperTradingEngine
        
        engine = PaperTradingEngine()
        
        # Try to inject SQL-like strings
        malicious_symbols = [
            "AAPL'; DROP TABLE trades; --",
            "AAPL' OR '1'='1",
            "AAPL\"; DELETE FROM portfolio; --"
        ]
        
        for symbol in malicious_symbols:
            # These should either be rejected or safely handled
            try:
                result = engine.place_order(
                    symbol=symbol,
                    quantity=10,
                    side='buy',
                    price=100.0,
                    order_type='stock'
                )
                
                # If accepted, symbol should be sanitized
                if result.get('success'):
                    trades = engine.get_trade_history()
                    if trades:
                        last_trade = trades[-1]
                        # Symbol should not contain SQL injection characters
                        self.assertNotIn(';', last_trade['symbol'])
                        self.assertNotIn('DROP', last_trade['symbol'])
                        self.assertNotIn('DELETE', last_trade['symbol'])
                        
            except Exception:
                # Rejection is also acceptable
                pass
    
    def test_xss_prevention(self):
        """Test prevention of XSS attacks in notifications"""
        notification_manager = NotificationManager()
        
        # Try to inject script tags
        malicious_messages = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//"
        ]
        
        for message in malicious_messages:
            # Mock the notification sending to capture the processed message
            with patch.object(notification_manager, '_send_email') as mock_send:
                mock_send.return_value = True
                
                notification_manager.send_notification(
                    'system_status',
                    'Test',
                    message,
                    'low'
                )
                
                # Check that script tags are not present in the call
                if mock_send.called:
                    call_args = str(mock_send.call_args)
                    self.assertNotIn('<script>', call_args)
                    self.assertNotIn('javascript:', call_args)
                    self.assertNotIn('onerror=', call_args)
    
    def test_path_traversal_prevention(self):
        """Test prevention of path traversal attacks"""
        from trading.paper_trading import PaperTradingEngine
        
        engine = PaperTradingEngine()
        
        # Try path traversal attacks
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/shadow",
            "C:\\Windows\\System32\\config\\SAM"
        ]
        
        for path in malicious_paths:
            # These should be rejected or sanitized
            try:
                engine.save_state(path)
                
                # If it didn't raise an exception, check that file wasn't created in dangerous location
                if os.path.exists(path):
                    # This would be a security vulnerability
                    os.unlink(path)  # Clean up
                    self.fail(f"Path traversal vulnerability: file created at {path}")
                    
            except (ValueError, OSError, PermissionError):
                # These exceptions are expected for malicious paths
                pass


class TestAuthenticationSecurity(unittest.TestCase):
    """Test authentication and authorization security"""
    
    def test_2fa_requirement(self):
        """Test that 2FA is properly implemented"""
        client = RobinhoodClient()
        
        # If TOTP secret is provided, it should be used
        if client.totp_code:
            # TOTP code should be properly formatted
            self.assertIsInstance(client.totp_code, str)
            self.assertGreater(len(client.totp_code), 10)  # TOTP secrets are typically longer
    
    def test_session_management(self):
        """Test session management security"""
        client = RobinhoodClient()
        
        # Test that logout properly clears session data
        client.is_authenticated = True
        client.account_info = {'test': 'data'}
        client.portfolio_info = {'test': 'data'}
        
        client.logout()
        
        self.assertFalse(client.is_authenticated)
        self.assertIsNone(client.account_info)
        self.assertIsNone(client.portfolio_info)
    
    def test_rate_limiting(self):
        """Test that rate limiting is implemented"""
        client = RobinhoodClient()
        
        # Check that rate limiting attributes exist
        self.assertTrue(hasattr(client, 'last_request_time'))
        self.assertTrue(hasattr(client, 'min_request_interval'))
        
        # Rate limit should be reasonable (not too aggressive, not too lenient)
        self.assertGreaterEqual(client.min_request_interval, 0.1)  # At least 100ms
        self.assertLessEqual(client.min_request_interval, 5.0)     # At most 5 seconds


class TestNetworkSecurity(unittest.TestCase):
    """Test network security measures"""
    
    def test_https_usage(self):
        """Test that HTTPS is used for API calls"""
        # Check that API endpoints use HTTPS
        api_endpoints = [
            "https://api.telegram.org",  # Telegram API
            "https://api.twilio.com",    # Twilio API
            "https://www.alphavantage.co"  # Alpha Vantage API
        ]
        
        for endpoint in api_endpoints:
            self.assertTrue(endpoint.startswith('https://'), 
                          f"API endpoint should use HTTPS: {endpoint}")
    
    def test_certificate_validation(self):
        """Test that SSL certificate validation is enabled"""
        import requests
        
        # Mock requests to check that verify=True is used
        with patch('requests.post') as mock_post:
            notification_manager = NotificationManager()
            
            # Try to send a notification (this will be mocked)
            notification_manager._send_telegram("Test", "Test message")
            
            # Check that SSL verification is enabled
            if mock_post.called:
                call_kwargs = mock_post.call_args[1]
                # verify should be True or not specified (defaults to True)
                verify_setting = call_kwargs.get('verify', True)
                self.assertTrue(verify_setting, "SSL certificate verification should be enabled")


class TestErrorHandlingSecurity(unittest.TestCase):
    """Test that error handling doesn't leak sensitive information"""
    
    def test_error_message_sanitization(self):
        """Test that error messages don't contain sensitive information"""
        client = RobinhoodClient()
        
        # Mock a failed authentication
        with patch('robin_stocks.robinhood.login') as mock_login:
            mock_login.return_value = None
            
            # Set fake credentials
            client.username = "test_user"
            client.password = "test_password"
            
            # Try to authenticate
            result = client.authenticate()
            
            self.assertFalse(result)
            
            # Error should be logged, but password should not be in logs
            # This is tested in the logging security tests
    
    def test_exception_information_disclosure(self):
        """Test that exceptions don't disclose sensitive information"""
        from trading.paper_trading import PaperTradingEngine
        
        engine = PaperTradingEngine()
        
        # Try to cause an exception with sensitive data
        try:
            # This should cause an exception
            engine.place_order(
                symbol=None,  # Invalid symbol
                quantity=-1,  # Invalid quantity
                side="invalid_side",  # Invalid side
                price="not_a_number",  # Invalid price
                order_type="invalid_type"
            )
        except Exception as e:
            error_message = str(e)
            
            # Error message should not contain sensitive information
            self.assertNotIn('password', error_message.lower())
            self.assertNotIn('secret', error_message.lower())
            self.assertNotIn('token', error_message.lower())


def run_security_tests():
    """Run all security tests"""
    print("🔒 AWOT Trading Platform - Security Test Suite")
    print("=" * 80)
    print("Running security vulnerability and protection tests...")
    print("=" * 80)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add security test classes
    test_classes = [
        TestCredentialSecurity,
        TestDataProtection,
        TestInputValidation,
        TestAuthenticationSecurity,
        TestNetworkSecurity,
        TestErrorHandlingSecurity
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(test_suite)
    
    # Security summary
    print("\n" + "=" * 80)
    print("🔒 SECURITY TEST SUMMARY")
    print("=" * 80)
    
    if result.wasSuccessful():
        print("🟢 All security tests passed!")
        print("✅ Platform meets security requirements")
        print("🛡️ No obvious security vulnerabilities detected")
    else:
        print("🔴 Some security tests failed")
        print("⚠️ Security vulnerabilities may exist")
        print("🚨 Review and fix security issues before production deployment")
    
    # Security recommendations
    print("\n📋 Security Recommendations:")
    print("• Use strong, unique passwords for all accounts")
    print("• Enable 2FA on all external services")
    print("• Regularly rotate API keys and credentials")
    print("• Monitor logs for suspicious activity")
    print("• Keep all dependencies updated")
    print("• Use HTTPS for all external communications")
    print("• Implement proper input validation")
    print("• Regular security audits and penetration testing")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_security_tests()
    sys.exit(0 if success else 1)
