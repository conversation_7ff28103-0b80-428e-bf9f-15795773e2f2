import unittest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from risk.risk_manager import RiskManager, RiskLevel, RiskMetric
from strategies.strategy import Position, PositionType
from signals.signal_generator import TradingSignal, SignalType, SignalStrength


class TestRiskMetric(unittest.TestCase):
    """Test cases for RiskMetric class"""
    
    def test_risk_metric_creation(self):
        """Test RiskMetric object creation"""
        metric = RiskMetric(
            name="Test Metric",
            value=0.15,
            threshold=0.10,
            risk_level=RiskLevel.HIGH,
            description="Test description"
        )
        
        self.assertEqual(metric.name, "Test Metric")
        self.assertEqual(metric.value, 0.15)
        self.assertEqual(metric.threshold, 0.10)
        self.assertEqual(metric.risk_level, RiskLevel.HIGH)
        self.assertTrue(metric.is_breached)
    
    def test_risk_metric_not_breached(self):
        """Test RiskMetric when threshold is not breached"""
        metric = RiskMetric(
            name="Safe Metric",
            value=0.05,
            threshold=0.10,
            risk_level=RiskLevel.LOW
        )
        
        self.assertFalse(metric.is_breached)
    
    def test_risk_metric_to_dict(self):
        """Test RiskMetric to_dict method"""
        metric = RiskMetric(
            name="Test Metric",
            value=0.15,
            threshold=0.10,
            risk_level=RiskLevel.HIGH
        )
        
        metric_dict = metric.to_dict()
        
        required_fields = ['name', 'value', 'threshold', 'risk_level', 'is_breached', 'description']
        for field in required_fields:
            self.assertIn(field, metric_dict)
        
        self.assertEqual(metric_dict['risk_level'], 'HIGH')
        self.assertTrue(metric_dict['is_breached'])


class TestRiskManager(unittest.TestCase):
    """Test cases for RiskManager class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.risk_manager = RiskManager(initial_capital=100000)
        
        # Create sample trading signal
        self.sample_signal = TradingSignal(
            symbol="AAPL",
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=150.0,
            target_price=165.0,
            stop_loss_price=140.0,
            expiration_date="2024-01-15",
            option_type="call"
        )
        
        # Create sample positions
        self.sample_positions = [
            Position(
                symbol="AAPL",
                position_type=PositionType.LONG_CALL,
                entry_price=5.0,
                quantity=10,
                strike_price=150.0,
                expiration_date="2024-01-15",
                stop_loss=3.0,
                take_profit=8.0
            ),
            Position(
                symbol="TSLA",
                position_type=PositionType.LONG_PUT,
                entry_price=4.0,
                quantity=5,
                strike_price=200.0,
                expiration_date="2024-01-20",
                stop_loss=2.0,
                take_profit=7.0
            )
        ]
        
        # Update positions with current prices
        self.sample_positions[0].update_price(6.0)
        self.sample_positions[1].update_price(3.5)
    
    def test_risk_manager_initialization(self):
        """Test RiskManager initialization"""
        self.assertEqual(self.risk_manager.initial_capital, 100000)
        self.assertEqual(self.risk_manager.current_capital, 100000)
        self.assertEqual(self.risk_manager.max_risk_per_trade, 0.10)
        self.assertEqual(self.risk_manager.max_positions, 3)
    
    def test_calculate_position_size_basic(self):
        """Test basic position size calculation"""
        portfolio_value = 100000
        current_price = 5.0
        
        result = self.risk_manager.calculate_position_size(
            self.sample_signal, current_price, portfolio_value, []
        )
        
        self.assertIn('position_size', result)
        self.assertIn('risk_amount', result)
        self.assertIn('risk_percentage', result)
        self.assertIn('risk_analysis', result)
        
        # Position size should be positive
        self.assertGreater(result['position_size'], 0)
        
        # Risk percentage should be reasonable
        self.assertLessEqual(result['risk_percentage'], self.risk_manager.max_risk_per_trade * 2)
    
    def test_calculate_position_size_invalid_stop_loss(self):
        """Test position size calculation with invalid stop loss"""
        # Create signal with invalid stop loss (higher than entry price for long)
        invalid_signal = TradingSignal(
            symbol="AAPL",
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=150.0,
            target_price=165.0,
            stop_loss_price=155.0,  # Invalid - higher than entry for long
            expiration_date="2024-01-15"
        )
        
        result = self.risk_manager.calculate_position_size(
            invalid_signal, 5.0, 100000, []
        )
        
        self.assertEqual(result['position_size'], 0)
        self.assertIn('Invalid stop loss', result['risk_analysis'])
    
    def test_signal_adjustment_calculation(self):
        """Test signal adjustment calculation"""
        # High confidence, strong signal
        high_quality_signal = TradingSignal(
            symbol="AAPL",
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.VERY_STRONG,
            confidence=0.9,
            entry_price=150.0,
            target_price=180.0,  # 3:1 risk-reward
            stop_loss_price=140.0,
            expiration_date="2024-01-15"
        )
        
        adjustment = self.risk_manager._calculate_signal_adjustment(high_quality_signal)
        self.assertGreater(adjustment, 1.0)  # Should increase position size
        
        # Low confidence, weak signal
        low_quality_signal = TradingSignal(
            symbol="AAPL",
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.WEAK,
            confidence=0.5,
            entry_price=150.0,
            target_price=155.0,  # 1:2 risk-reward
            stop_loss_price=140.0,
            expiration_date="2024-01-15"
        )
        
        adjustment = self.risk_manager._calculate_signal_adjustment(low_quality_signal)
        self.assertLess(adjustment, 1.0)  # Should decrease position size
    
    def test_volatility_adjustment(self):
        """Test volatility adjustment calculation"""
        # Test with different volatility levels
        self.risk_manager.volatility_adjustment_factor = 1.6  # High volatility
        adjustment_high = self.risk_manager._calculate_volatility_adjustment("AAPL")
        self.assertLess(adjustment_high, 1.0)  # Should reduce position size
        
        self.risk_manager.volatility_adjustment_factor = 0.7  # Low volatility
        adjustment_low = self.risk_manager._calculate_volatility_adjustment("AAPL")
        self.assertGreater(adjustment_low, 1.0)  # Should increase position size
        
        self.risk_manager.volatility_adjustment_factor = 1.0  # Normal volatility
        adjustment_normal = self.risk_manager._calculate_volatility_adjustment("AAPL")
        self.assertEqual(adjustment_normal, 1.0)  # Should not change position size
    
    def test_portfolio_constraints_max_positions(self):
        """Test portfolio constraints with maximum positions"""
        # Create positions at the limit
        max_positions = [
            Position("AAPL", PositionType.LONG_CALL, 5.0, 10, 150.0, "2024-01-15"),
            Position("TSLA", PositionType.LONG_CALL, 4.0, 10, 200.0, "2024-01-15"),
            Position("MSFT", PositionType.LONG_CALL, 3.0, 10, 300.0, "2024-01-15")
        ]
        
        # All positions are open
        for pos in max_positions:
            pos.is_open = True
        
        constrained_size = self.risk_manager._apply_portfolio_constraints(
            10, self.sample_signal, 5.0, 100000, max_positions
        )
        
        self.assertEqual(constrained_size, 0)  # Should reject new position
    
    def test_portfolio_constraints_single_position_risk(self):
        """Test single position risk constraint"""
        # Large position that would exceed single position limit
        large_position_size = 50  # Would be 50 * 5 = $2500 = 2.5% of $100k portfolio
        
        constrained_size = self.risk_manager._apply_portfolio_constraints(
            large_position_size, self.sample_signal, 5.0, 100000, []
        )
        
        # Should be constrained by single position risk limit (15% = $15k / $5 = 3000 contracts max)
        self.assertLessEqual(constrained_size * 5.0, 100000 * self.risk_manager.max_single_position_risk)
    
    def test_assess_portfolio_risk(self):
        """Test portfolio risk assessment"""
        portfolio_value = 100000
        risk_metrics = self.risk_manager.assess_portfolio_risk(self.sample_positions, portfolio_value)
        
        # Check that all expected metrics are present
        expected_metrics = ['concentration', 'total_risk', 'drawdown', 'time_decay']
        for metric_name in expected_metrics:
            self.assertIn(metric_name, risk_metrics)
            self.assertIsInstance(risk_metrics[metric_name], RiskMetric)
        
        # Check concentration metric
        concentration_metric = risk_metrics['concentration']
        expected_concentration = len(self.sample_positions) / self.risk_manager.max_positions
        self.assertAlmostEqual(concentration_metric.value, expected_concentration, places=2)
    
    def test_calculate_total_portfolio_risk(self):
        """Test total portfolio risk calculation"""
        portfolio_value = 100000
        total_risk = self.risk_manager._calculate_total_portfolio_risk(self.sample_positions, portfolio_value)
        
        # Should be positive
        self.assertGreaterEqual(total_risk, 0)
        
        # Should be reasonable (less than 100%)
        self.assertLess(total_risk, 1.0)
    
    def test_time_decay_risk_calculation(self):
        """Test time decay risk calculation"""
        # Create positions with different expiration dates
        positions_with_expiry = [
            Position("AAPL", PositionType.LONG_CALL, 5.0, 10, 150.0, 
                    (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')),  # Near expiry
            Position("TSLA", PositionType.LONG_CALL, 4.0, 10, 200.0, 
                    (datetime.now() + timedelta(days=10)).strftime('%Y-%m-%d'))  # Far expiry
        ]
        
        time_decay_risk = self.risk_manager._calculate_time_decay_risk(positions_with_expiry)
        
        # Should detect 50% of positions near expiry
        self.assertAlmostEqual(time_decay_risk, 0.5, places=1)
    
    def test_generate_risk_alerts(self):
        """Test risk alert generation"""
        portfolio_value = 100000
        
        # Create a position near expiry
        near_expiry_position = Position(
            "AAPL", PositionType.LONG_CALL, 5.0, 10, 150.0,
            (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        )
        positions_with_alerts = self.sample_positions + [near_expiry_position]
        
        alerts = self.risk_manager.generate_risk_alerts(positions_with_alerts, portfolio_value)
        
        self.assertIsInstance(alerts, list)
        
        # Should have at least one expiry warning
        expiry_alerts = [a for a in alerts if a['type'] == 'expiry_warning']
        self.assertGreater(len(expiry_alerts), 0)
        
        # Check alert structure
        for alert in alerts:
            required_fields = ['type', 'severity', 'message', 'recommendation', 'timestamp']
            for field in required_fields:
                self.assertIn(field, alert)
    
    def test_get_risk_summary(self):
        """Test comprehensive risk summary"""
        portfolio_value = 100000
        risk_summary = self.risk_manager.get_risk_summary(self.sample_positions, portfolio_value)
        
        # Check required fields
        required_fields = [
            'overall_risk_score', 'overall_risk_level', 'risk_metrics',
            'active_alerts', 'alerts', 'recommendations'
        ]
        for field in required_fields:
            self.assertIn(field, risk_summary)
        
        # Check data types
        self.assertIsInstance(risk_summary['overall_risk_score'], (int, float))
        self.assertIsInstance(risk_summary['risk_metrics'], dict)
        self.assertIsInstance(risk_summary['alerts'], list)
        self.assertIsInstance(risk_summary['recommendations'], list)
        
        # Risk score should be between 0 and 100
        self.assertGreaterEqual(risk_summary['overall_risk_score'], 0)
        self.assertLessEqual(risk_summary['overall_risk_score'], 100)
    
    def test_risk_recommendations(self):
        """Test risk recommendation generation"""
        # Create high-risk scenario
        high_risk_positions = []
        for i in range(5):  # More than max positions
            pos = Position(f"STOCK{i}", PositionType.LONG_CALL, 5.0, 20, 150.0, "2024-01-15")
            pos.unrealized_pnl = -500  # Losing positions
            high_risk_positions.append(pos)
        
        portfolio_value = 50000  # Smaller portfolio for higher risk ratios
        risk_summary = self.risk_manager.get_risk_summary(high_risk_positions, portfolio_value)
        
        recommendations = risk_summary['recommendations']
        self.assertIsInstance(recommendations, list)
        self.assertGreater(len(recommendations), 0)
        
        # Should have risk-related recommendations
        risk_recommendations = [r for r in recommendations if '🔴' in r or '⚠️' in r or '📉' in r]
        self.assertGreater(len(risk_recommendations), 0)
    
    def test_sector_exposure_calculation(self):
        """Test sector exposure calculation (simplified)"""
        positions = [
            Position("AAPL", PositionType.LONG_CALL, 5.0, 10, 150.0, "2024-01-15"),
            Position("AMZN", PositionType.LONG_CALL, 4.0, 10, 200.0, "2024-01-15"),
            Position("TSLA", PositionType.LONG_CALL, 3.0, 10, 300.0, "2024-01-15")
        ]
        
        # All positions are open
        for pos in positions:
            pos.is_open = True
        
        # Test exposure for 'A' symbols (simplified sector grouping)
        exposure = self.risk_manager._calculate_sector_exposure("AAPL", positions)
        
        # Should find 2 out of 3 positions starting with 'A'
        expected_exposure = 2 / 3
        self.assertAlmostEqual(exposure, expected_exposure, places=2)


if __name__ == '__main__':
    unittest.main()
