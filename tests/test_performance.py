#!/usr/bin/env python3
"""
AWOT Trading Platform - Performance Test Suite
Tests for performance, scalability, and stress testing
"""

import unittest
import time
import threading
import sys
import os
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import statistics
import psutil
import gc

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from indicators.technical_indicators import TechnicalIndicators
from signals.signal_generator import SignalGenerator
from trading.paper_trading import PaperTradingEngine
from trading.order_manager import OrderManager
from trading.robinhood_client import RobinhoodClient, OrderSide
from trading.live_trading_engine import LiveTradingEngine, TradingMode


class PerformanceTestCase(unittest.TestCase):
    """Base class for performance tests"""
    
    def setUp(self):
        self.start_time = time.time()
        self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    
    def tearDown(self):
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        execution_time = end_time - self.start_time
        memory_delta = end_memory - self.start_memory
        
        print(f"   ⏱️ Execution time: {execution_time:.3f}s")
        print(f"   💾 Memory delta: {memory_delta:+.2f}MB")
    
    def measure_execution_time(self, func, *args, **kwargs):
        """Measure execution time of a function"""
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        return result, end - start


class TestTechnicalIndicatorsPerformance(PerformanceTestCase):
    """Performance tests for technical indicators"""
    
    def setUp(self):
        super().setUp()
        self.indicators = TechnicalIndicators()
        # Large dataset for performance testing
        self.large_prices = list(range(100, 200)) * 100  # 10,000 data points
        self.large_volumes = list(range(1000, 2000)) * 100
    
    def test_sma_performance_large_dataset(self):
        """Test SMA calculation performance with large dataset"""
        _, execution_time = self.measure_execution_time(
            self.indicators.calculate_sma, self.large_prices, 20
        )
        
        # Should complete within reasonable time
        self.assertLess(execution_time, 1.0, "SMA calculation too slow for large dataset")
        print(f"   📊 SMA calculation rate: {len(self.large_prices)/execution_time:.0f} points/sec")
    
    def test_rsi_performance_large_dataset(self):
        """Test RSI calculation performance with large dataset"""
        _, execution_time = self.measure_execution_time(
            self.indicators.calculate_rsi, self.large_prices, 14
        )
        
        self.assertLess(execution_time, 2.0, "RSI calculation too slow for large dataset")
        print(f"   📊 RSI calculation rate: {len(self.large_prices)/execution_time:.0f} points/sec")
    
    def test_multiple_indicators_concurrent(self):
        """Test concurrent calculation of multiple indicators"""
        def calculate_all_indicators():
            sma = self.indicators.calculate_sma(self.large_prices, 20)
            ema = self.indicators.calculate_ema(self.large_prices, 20)
            rsi = self.indicators.calculate_rsi(self.large_prices, 14)
            return sma, ema, rsi
        
        _, execution_time = self.measure_execution_time(calculate_all_indicators)
        
        self.assertLess(execution_time, 3.0, "Multiple indicators calculation too slow")
        print(f"   📊 Combined indicators rate: {len(self.large_prices)/execution_time:.0f} points/sec")


class TestSignalGeneratorPerformance(PerformanceTestCase):
    """Performance tests for signal generation"""
    
    def setUp(self):
        super().setUp()
        self.signal_generator = SignalGenerator()
    
    def test_signal_generation_speed(self):
        """Test signal generation speed for multiple symbols"""
        symbols = ['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'AMZN'] * 10  # 50 symbols
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [
                executor.submit(self.signal_generator.generate_signals, symbol)
                for symbol in symbols
            ]
            
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        self.assertLess(execution_time, 30.0, "Signal generation too slow")
        print(f"   📊 Signal generation rate: {len(symbols)/execution_time:.1f} symbols/sec")
    
    def test_signal_validation_performance(self):
        """Test signal validation performance"""
        from signals.signal_generator import TradingSignal, SignalType, SignalStrength
        
        # Create many test signals
        signals = []
        for i in range(1000):
            signal = TradingSignal(
                symbol=f'TEST{i}',
                signal_type=SignalType.MOMENTUM_BULLISH,
                strength=SignalStrength.STRONG,
                confidence=0.8,
                entry_price=100.0 + i,
                target_price=110.0 + i,
                stop_loss=90.0 + i
            )
            signals.append(signal)
        
        start_time = time.time()
        
        for signal in signals:
            self.signal_generator.validate_signal(signal)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        self.assertLess(execution_time, 1.0, "Signal validation too slow")
        print(f"   📊 Signal validation rate: {len(signals)/execution_time:.0f} signals/sec")


class TestPaperTradingPerformance(PerformanceTestCase):
    """Performance tests for paper trading"""
    
    def setUp(self):
        super().setUp()
        self.paper_engine = PaperTradingEngine()
    
    def test_high_frequency_trading_simulation(self):
        """Test high-frequency trading simulation"""
        num_trades = 1000
        
        start_time = time.time()
        
        for i in range(num_trades):
            # Alternate between buy and sell
            side = OrderSide.BUY if i % 2 == 0 else OrderSide.SELL
            
            if side == OrderSide.BUY or i > 0:  # Can only sell if we have positions
                self.paper_engine.place_order(
                    symbol='AAPL',
                    quantity=10,
                    side=side,
                    price=150.0 + (i % 10),
                    order_type='stock'
                )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        self.assertLess(execution_time, 10.0, "Paper trading too slow for high frequency")
        print(f"   📊 Paper trading rate: {num_trades/execution_time:.0f} trades/sec")
    
    def test_portfolio_update_performance(self):
        """Test portfolio update performance with many positions"""
        # Create many positions
        symbols = [f'STOCK{i}' for i in range(100)]
        
        for symbol in symbols:
            self.paper_engine.place_order(
                symbol=symbol,
                quantity=10,
                side=OrderSide.BUY,
                price=100.0,
                order_type='stock'
            )
        
        # Create market data for all symbols
        market_data = {symbol: 105.0 for symbol in symbols}
        
        start_time = time.time()
        
        # Update positions multiple times
        for _ in range(10):
            self.paper_engine.update_positions(market_data)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        total_updates = len(symbols) * 10
        self.assertLess(execution_time, 5.0, "Portfolio updates too slow")
        print(f"   📊 Portfolio update rate: {total_updates/execution_time:.0f} updates/sec")


class TestOrderManagerPerformance(PerformanceTestCase):
    """Performance tests for order management"""
    
    def setUp(self):
        super().setUp()
        self.client = RobinhoodClient()
        self.order_manager = OrderManager(self.client)
        self.order_manager.start()
    
    def tearDown(self):
        self.order_manager.stop()
        super().tearDown()
    
    def test_concurrent_order_submission(self):
        """Test concurrent order submission performance"""
        num_orders = 100
        
        def submit_order(i):
            return self.order_manager.submit_stock_order(
                symbol=f'TEST{i}',
                quantity=10,
                side=OrderSide.BUY,
                price=100.0
            )
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(submit_order, i) for i in range(num_orders)]
            order_ids = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Check that all orders were submitted
        successful_orders = len([oid for oid in order_ids if oid])
        
        self.assertGreater(successful_orders, num_orders * 0.9, "Too many order failures")
        self.assertLess(execution_time, 30.0, "Order submission too slow")
        print(f"   📊 Order submission rate: {successful_orders/execution_time:.1f} orders/sec")
    
    def test_order_status_checking_performance(self):
        """Test order status checking performance"""
        # Submit some orders first
        order_ids = []
        for i in range(50):
            order_id = self.order_manager.submit_stock_order(
                symbol=f'TEST{i}',
                quantity=10,
                side=OrderSide.BUY,
                price=100.0
            )
            if order_id:
                order_ids.append(order_id)
        
        # Wait a moment for orders to process
        time.sleep(2)
        
        start_time = time.time()
        
        # Check status of all orders
        for order_id in order_ids:
            self.order_manager.get_order_status(order_id)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        self.assertLess(execution_time, 10.0, "Order status checking too slow")
        print(f"   📊 Status check rate: {len(order_ids)/execution_time:.1f} checks/sec")


class TestLiveTradingEnginePerformance(PerformanceTestCase):
    """Performance tests for live trading engine"""
    
    def setUp(self):
        super().setUp()
        self.engine = LiveTradingEngine(TradingMode.PAPER)
    
    def tearDown(self):
        if self.engine.state != TradingState.STOPPED:
            self.engine.stop()
        super().tearDown()
    
    def test_engine_startup_time(self):
        """Test trading engine startup performance"""
        start_time = time.time()
        
        success = self.engine.start()
        
        end_time = time.time()
        startup_time = end_time - start_time
        
        self.assertTrue(success, "Engine failed to start")
        self.assertLess(startup_time, 10.0, "Engine startup too slow")
        print(f"   📊 Engine startup time: {startup_time:.2f}s")
    
    def test_configuration_update_performance(self):
        """Test configuration update performance"""
        self.engine.start()
        
        configs = [
            {'max_positions': i, 'risk_per_trade': 0.1 + i * 0.01}
            for i in range(1, 101)
        ]
        
        start_time = time.time()
        
        for config in configs:
            self.engine.update_config(config)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        self.assertLess(execution_time, 5.0, "Configuration updates too slow")
        print(f"   📊 Config update rate: {len(configs)/execution_time:.1f} updates/sec")
    
    def test_strategy_toggle_performance(self):
        """Test strategy enable/disable performance"""
        self.engine.start()
        
        strategies = ['momentum', 'breakout']
        
        start_time = time.time()
        
        # Toggle strategies multiple times
        for _ in range(100):
            for strategy in strategies:
                self.engine.disable_strategy(strategy)
                self.engine.enable_strategy(strategy)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        total_toggles = 100 * len(strategies) * 2  # disable + enable
        self.assertLess(execution_time, 10.0, "Strategy toggling too slow")
        print(f"   📊 Strategy toggle rate: {total_toggles/execution_time:.1f} toggles/sec")


class TestMemoryUsage(PerformanceTestCase):
    """Memory usage and leak tests"""
    
    def test_memory_usage_under_load(self):
        """Test memory usage under sustained load"""
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Create and destroy many objects
        for cycle in range(10):
            # Create many trading objects
            engines = [PaperTradingEngine() for _ in range(10)]
            indicators = [TechnicalIndicators() for _ in range(10)]
            
            # Use them briefly
            for engine in engines:
                engine.place_order('TEST', 10, OrderSide.BUY, 100.0, 'stock')
            
            for indicator in indicators:
                indicator.calculate_sma([100, 101, 102, 103, 104], 3)
            
            # Clean up
            del engines
            del indicators
            gc.collect()
            
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_growth = current_memory - initial_memory
            
            print(f"   💾 Cycle {cycle + 1}: {current_memory:.1f}MB (+{memory_growth:.1f}MB)")
        
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        total_growth = final_memory - initial_memory
        
        # Memory growth should be reasonable (less than 100MB)
        self.assertLess(total_growth, 100, f"Excessive memory growth: {total_growth:.1f}MB")
    
    def test_long_running_stability(self):
        """Test stability during long-running operations"""
        engine = LiveTradingEngine(TradingMode.PAPER)
        engine.start()
        
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            # Run for a while with periodic operations
            for i in range(60):  # 1 minute of operations
                # Simulate trading activity
                engine.update_config({'max_positions': 3 + (i % 3)})
                
                if i % 10 == 0:
                    current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                    memory_growth = current_memory - initial_memory
                    print(f"   💾 {i}s: {current_memory:.1f}MB (+{memory_growth:.1f}MB)")
                
                time.sleep(1)
            
            final_memory = psutil.Process().memory_info().rss / 1024 / 1024
            total_growth = final_memory - initial_memory
            
            # Memory growth should be minimal for long-running operations
            self.assertLess(total_growth, 50, f"Memory leak detected: {total_growth:.1f}MB growth")
            
        finally:
            engine.stop()


def run_performance_tests():
    """Run all performance tests"""
    print("🚀 AWOT Trading Platform - Performance Test Suite")
    print("=" * 80)
    print("Running performance, scalability, and stress tests...")
    print("=" * 80)
    
    # Get system info
    cpu_count = psutil.cpu_count()
    memory_gb = psutil.virtual_memory().total / 1024 / 1024 / 1024
    print(f"🖥️ System: {cpu_count} CPUs, {memory_gb:.1f}GB RAM")
    print("-" * 80)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add performance test classes
    test_classes = [
        TestTechnicalIndicatorsPerformance,
        TestSignalGeneratorPerformance,
        TestPaperTradingPerformance,
        TestOrderManagerPerformance,
        TestLiveTradingEnginePerformance,
        TestMemoryUsage
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    
    start_time = time.time()
    result = runner.run(test_suite)
    end_time = time.time()
    
    # Performance summary
    print("\n" + "=" * 80)
    print("📊 PERFORMANCE TEST SUMMARY")
    print("=" * 80)
    
    total_time = end_time - start_time
    print(f"⏱️ Total execution time: {total_time:.2f} seconds")
    print(f"📈 Tests per second: {result.testsRun / total_time:.1f}")
    
    if result.wasSuccessful():
        print("🟢 All performance tests passed!")
        print("✅ Platform meets performance requirements")
    else:
        print("🔴 Some performance tests failed")
        print("⚠️ Performance optimization needed")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_performance_tests()
    sys.exit(0 if success else 1)
