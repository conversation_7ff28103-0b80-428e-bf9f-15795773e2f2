#!/usr/bin/env python3
"""
AWOT Trading Platform - Comprehensive Test Suite
Automated testing for all platform components and features
"""

import unittest
import sys
import os
import time
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import tempfile
import json

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import all modules to test
from indicators.technical_indicators import TechnicalIndicators
from signals.signal_generator import SignalGenerator, TradingSignal, SignalType, SignalStrength
from strategies.strategy import MomentumStrategy, BreakoutStrategy, Position, PositionType
from risk.risk_manager import RiskManager
from data.market_data import MarketDataProvider
from notifications.notification_manager import NotificationManager, NotificationType, NotificationPriority
from trading.robinhood_client import RobinhoodClient, OrderSide, OptionType
from trading.order_manager import OrderManager, OrderPriority
from trading.live_trading_engine import LiveTradingEngine, TradingMode, TradingState
from trading.paper_trading import PaperTradingEngine


class TestTechnicalIndicators(unittest.TestCase):
    """Test technical indicators calculations"""
    
    def setUp(self):
        self.indicators = TechnicalIndicators()
        # Sample price data
        self.prices = [100, 102, 101, 103, 105, 104, 106, 108, 107, 109]
        self.volumes = [1000, 1200, 800, 1500, 1800, 1100, 1600, 2000, 1300, 1700]
    
    def test_sma_calculation(self):
        """Test Simple Moving Average calculation"""
        sma = self.indicators.calculate_sma(self.prices, period=5)
        self.assertIsInstance(sma, list)
        self.assertEqual(len(sma), len(self.prices))
        # Check that early values are None (not enough data)
        self.assertIsNone(sma[0])
        self.assertIsNone(sma[3])
        # Check calculation
        expected_sma_5 = sum(self.prices[:5]) / 5
        self.assertAlmostEqual(sma[4], expected_sma_5, places=2)
    
    def test_ema_calculation(self):
        """Test Exponential Moving Average calculation"""
        ema = self.indicators.calculate_ema(self.prices, period=5)
        self.assertIsInstance(ema, list)
        self.assertEqual(len(ema), len(self.prices))
        # EMA should have values for all periods
        self.assertIsNotNone(ema[-1])
    
    def test_rsi_calculation(self):
        """Test RSI calculation"""
        rsi = self.indicators.calculate_rsi(self.prices, period=5)
        self.assertIsInstance(rsi, list)
        # RSI should be between 0 and 100
        for value in rsi:
            if value is not None:
                self.assertGreaterEqual(value, 0)
                self.assertLessEqual(value, 100)
    
    def test_bollinger_bands(self):
        """Test Bollinger Bands calculation"""
        upper, middle, lower = self.indicators.calculate_bollinger_bands(self.prices, period=5)
        self.assertEqual(len(upper), len(self.prices))
        self.assertEqual(len(middle), len(self.prices))
        self.assertEqual(len(lower), len(self.prices))
        
        # Upper band should be above middle, middle above lower
        for i in range(len(self.prices)):
            if all(x is not None for x in [upper[i], middle[i], lower[i]]):
                self.assertGreater(upper[i], middle[i])
                self.assertGreater(middle[i], lower[i])
    
    def test_macd_calculation(self):
        """Test MACD calculation"""
        macd_line, signal_line, histogram = self.indicators.calculate_macd(self.prices)
        self.assertEqual(len(macd_line), len(self.prices))
        self.assertEqual(len(signal_line), len(self.prices))
        self.assertEqual(len(histogram), len(self.prices))


class TestSignalGenerator(unittest.TestCase):
    """Test signal generation logic"""
    
    def setUp(self):
        self.signal_generator = SignalGenerator()
    
    @patch('data.market_data.MarketDataProvider.get_stock_data')
    def test_signal_generation(self, mock_get_data):
        """Test signal generation with mocked data"""
        # Mock market data
        mock_data = {
            'prices': [100, 102, 101, 103, 105, 104, 106, 108, 107, 109],
            'volumes': [1000, 1200, 800, 1500, 1800, 1100, 1600, 2000, 1300, 1700],
            'timestamps': [datetime.now() - timedelta(days=i) for i in range(10, 0, -1)]
        }
        mock_get_data.return_value = mock_data
        
        signals = self.signal_generator.generate_signals('AAPL')
        self.assertIsInstance(signals, list)
        
        # Check signal properties
        for signal in signals:
            self.assertIsInstance(signal, TradingSignal)
            self.assertEqual(signal.symbol, 'AAPL')
            self.assertIsInstance(signal.signal_type, SignalType)
            self.assertIsInstance(signal.strength, SignalStrength)
            self.assertGreaterEqual(signal.confidence, 0)
            self.assertLessEqual(signal.confidence, 1)
    
    def test_signal_validation(self):
        """Test signal validation logic"""
        # Create a test signal
        signal = TradingSignal(
            symbol='AAPL',
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=150.0,
            target_price=165.0,
            stop_loss=140.0
        )
        
        # Test validation
        is_valid = self.signal_generator.validate_signal(signal)
        self.assertTrue(is_valid)
        
        # Test invalid signal (target below entry for bullish signal)
        invalid_signal = TradingSignal(
            symbol='AAPL',
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=150.0,
            target_price=140.0,  # Invalid: target below entry for bullish
            stop_loss=135.0
        )
        
        is_valid = self.signal_generator.validate_signal(invalid_signal)
        self.assertFalse(is_valid)


class TestStrategies(unittest.TestCase):
    """Test trading strategies"""
    
    def setUp(self):
        self.momentum_strategy = MomentumStrategy()
        self.breakout_strategy = BreakoutStrategy()
    
    def test_momentum_strategy_analysis(self):
        """Test momentum strategy signal analysis"""
        # Create test signal
        signal = TradingSignal(
            symbol='AAPL',
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=150.0,
            target_price=165.0,
            stop_loss=140.0
        )
        
        analysis = self.momentum_strategy.analyze_signal(signal)
        self.assertIsInstance(analysis, dict)
        self.assertIn('suitable', analysis)
        self.assertIn('risk_reward_ratio', analysis)
        self.assertIn('position_size_recommendation', analysis)
    
    def test_breakout_strategy_analysis(self):
        """Test breakout strategy signal analysis"""
        signal = TradingSignal(
            symbol='TSLA',
            signal_type=SignalType.BREAKOUT_BULLISH,
            strength=SignalStrength.MEDIUM,
            confidence=0.7,
            entry_price=200.0,
            target_price=220.0,
            stop_loss=190.0
        )
        
        analysis = self.breakout_strategy.analyze_signal(signal)
        self.assertIsInstance(analysis, dict)
        self.assertIn('suitable', analysis)
    
    def test_position_management(self):
        """Test position management logic"""
        # Create test position
        position = Position(
            symbol='AAPL',
            position_type=PositionType.LONG,
            quantity=100,
            entry_price=150.0,
            current_price=155.0,
            entry_time=datetime.now()
        )
        
        # Test position evaluation
        should_exit = self.momentum_strategy.should_exit_position(position)
        self.assertIsInstance(should_exit, bool)


class TestRiskManager(unittest.TestCase):
    """Test risk management functionality"""
    
    def setUp(self):
        self.risk_manager = RiskManager()
    
    def test_position_sizing(self):
        """Test position sizing calculation"""
        signal = TradingSignal(
            symbol='AAPL',
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=150.0,
            target_price=165.0,
            stop_loss=140.0
        )
        
        portfolio_value = 50000
        current_positions = []
        
        sizing_info = self.risk_manager.calculate_position_size(
            signal, signal.entry_price, portfolio_value, current_positions
        )
        
        self.assertIsInstance(sizing_info, dict)
        self.assertIn('position_size', sizing_info)
        self.assertIn('risk_amount', sizing_info)
        self.assertIn('risk_percentage', sizing_info)
        
        # Position size should be positive
        self.assertGreater(sizing_info['position_size'], 0)
        
        # Risk should be within limits
        self.assertLessEqual(sizing_info['risk_percentage'], 0.15)  # Max 15% risk
    
    def test_portfolio_risk_assessment(self):
        """Test portfolio risk assessment"""
        positions = [
            Position('AAPL', PositionType.LONG, 100, 150.0, 155.0, datetime.now()),
            Position('TSLA', PositionType.LONG, 50, 200.0, 195.0, datetime.now())
        ]
        
        portfolio_value = 50000
        risk_summary = self.risk_manager.get_risk_summary(positions, portfolio_value)
        
        self.assertIsInstance(risk_summary, dict)
        self.assertIn('overall_risk_score', risk_summary)
        self.assertIn('overall_risk_level', risk_summary)
        self.assertIn('position_concentration', risk_summary)
        
        # Risk score should be between 0 and 100
        self.assertGreaterEqual(risk_summary['overall_risk_score'], 0)
        self.assertLessEqual(risk_summary['overall_risk_score'], 100)


class TestNotificationManager(unittest.TestCase):
    """Test notification system"""
    
    def setUp(self):
        self.notification_manager = NotificationManager()
    
    def test_notification_sending(self):
        """Test notification sending (mocked)"""
        with patch.object(self.notification_manager, '_send_email') as mock_email:
            mock_email.return_value = True
            
            result = self.notification_manager.send_notification(
                NotificationType.TRADE_EXECUTED,
                "Test Trade",
                "Test trade executed successfully",
                NotificationPriority.HIGH
            )
            
            self.assertTrue(result)
    
    def test_trade_notification(self):
        """Test trade-specific notification"""
        trade_data = {
            'symbol': 'AAPL',
            'action': 'BUY',
            'quantity': 100,
            'price': 150.0,
            'status': 'Filled'
        }
        
        with patch.object(self.notification_manager, 'send_notification') as mock_send:
            mock_send.return_value = True
            
            result = self.notification_manager.send_trade_notification(trade_data)
            self.assertTrue(result)
            mock_send.assert_called_once()
    
    def test_notification_history(self):
        """Test notification history tracking"""
        # Send a test notification
        self.notification_manager.send_notification(
            NotificationType.SYSTEM_STATUS,
            "Test",
            "Test message",
            NotificationPriority.LOW
        )
        
        history = self.notification_manager.get_notification_history()
        self.assertIsInstance(history, list)


class TestPaperTrading(unittest.TestCase):
    """Test paper trading functionality"""
    
    def setUp(self):
        self.paper_engine = PaperTradingEngine(initial_capital=50000)
    
    def test_stock_order_execution(self):
        """Test stock order execution in paper trading"""
        result = self.paper_engine.place_order(
            symbol='AAPL',
            quantity=100,
            side=OrderSide.BUY,
            price=150.0,
            order_type='stock'
        )
        
        self.assertTrue(result['success'])
        self.assertIn('trade_id', result)
        self.assertIn('execution_price', result)
        
        # Check portfolio state
        summary = self.paper_engine.get_portfolio_summary()
        self.assertEqual(summary['open_positions'], 1)
        self.assertLess(summary['cash'], 50000)  # Cash should be reduced
    
    def test_option_order_execution(self):
        """Test option order execution in paper trading"""
        result = self.paper_engine.place_order(
            symbol='TSLA',
            quantity=5,
            side=OrderSide.BUY,
            price=8.50,
            order_type='option',
            strike_price=200.0,
            expiration_date='2024-01-19',
            option_type=OptionType.CALL
        )
        
        self.assertTrue(result['success'])
        
        # Check portfolio state
        summary = self.paper_engine.get_portfolio_summary()
        self.assertEqual(summary['open_positions'], 1)
    
    def test_insufficient_funds(self):
        """Test handling of insufficient funds"""
        # Try to buy more than available cash
        result = self.paper_engine.place_order(
            symbol='AAPL',
            quantity=1000,  # Very large quantity
            side=OrderSide.BUY,
            price=1000.0,   # High price
            order_type='stock'
        )
        
        self.assertFalse(result['success'])
        self.assertIn('Insufficient cash', result['error'])
    
    def test_position_updates(self):
        """Test position price updates"""
        # Place an order first
        self.paper_engine.place_order(
            symbol='AAPL',
            quantity=100,
            side=OrderSide.BUY,
            price=150.0,
            order_type='stock'
        )
        
        # Update with new market data
        market_data = {'AAPL': 155.0}
        self.paper_engine.update_positions(market_data)
        
        # Check that position was updated
        positions = self.paper_engine.get_positions()
        aapl_position = next(p for p in positions if p['symbol'] == 'AAPL')
        self.assertEqual(aapl_position['current_price'], 155.0)
        self.assertGreater(aapl_position['unrealized_pnl'], 0)  # Should be profitable
    
    def test_portfolio_reset(self):
        """Test portfolio reset functionality"""
        # Make some trades
        self.paper_engine.place_order('AAPL', 100, OrderSide.BUY, 150.0, 'stock')
        
        # Reset portfolio
        self.paper_engine.reset_portfolio()
        
        # Check that everything is reset
        summary = self.paper_engine.get_portfolio_summary()
        self.assertEqual(summary['cash'], 50000)
        self.assertEqual(summary['open_positions'], 0)
        self.assertEqual(summary['total_pnl'], 0)


class TestRobinhoodClient(unittest.TestCase):
    """Test Robinhood client (paper trading mode)"""
    
    def setUp(self):
        self.client = RobinhoodClient()  # Will use paper trading by default
    
    def test_authentication(self):
        """Test authentication (paper mode)"""
        result = self.client.authenticate()
        self.assertTrue(result)  # Should succeed in paper mode
    
    def test_account_info(self):
        """Test getting account information"""
        account_info = self.client.get_account_info()
        self.assertIsInstance(account_info, dict)
        self.assertIn('buying_power', account_info)
        self.assertIn('total_value', account_info)
    
    def test_stock_order_placement(self):
        """Test stock order placement"""
        result = self.client.place_stock_order(
            symbol='AAPL',
            quantity=100,
            side=OrderSide.BUY,
            price=150.0
        )
        
        self.assertIsInstance(result, dict)
        self.assertIn('success', result)
    
    def test_option_order_placement(self):
        """Test option order placement"""
        result = self.client.place_option_order(
            symbol='TSLA',
            strike_price=200.0,
            expiration_date='2024-01-19',
            option_type=OptionType.CALL,
            quantity=5,
            side=OrderSide.BUY,
            price=8.50
        )
        
        self.assertIsInstance(result, dict)
        self.assertIn('success', result)


class TestOrderManager(unittest.TestCase):
    """Test order management system"""
    
    def setUp(self):
        self.client = RobinhoodClient()
        self.order_manager = OrderManager(self.client)
    
    def test_order_submission(self):
        """Test order submission"""
        order_id = self.order_manager.submit_stock_order(
            symbol='AAPL',
            quantity=100,
            side=OrderSide.BUY,
            price=150.0,
            priority=OrderPriority.HIGH
        )
        
        self.assertIsInstance(order_id, str)
        self.assertNotEqual(order_id, '')
    
    def test_order_cancellation(self):
        """Test order cancellation"""
        # Submit an order first
        order_id = self.order_manager.submit_stock_order(
            symbol='AAPL',
            quantity=100,
            side=OrderSide.BUY,
            price=150.0
        )
        
        # Cancel the order
        result = self.order_manager.cancel_order(order_id)
        self.assertTrue(result)
    
    def test_order_statistics(self):
        """Test order statistics tracking"""
        stats = self.order_manager.get_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn('total_orders', stats)
        self.assertIn('successful_orders', stats)
        self.assertIn('failed_orders', stats)


class TestLiveTradingEngine(unittest.TestCase):
    """Test live trading engine"""
    
    def setUp(self):
        self.engine = LiveTradingEngine(TradingMode.PAPER)
    
    def test_engine_initialization(self):
        """Test engine initialization"""
        self.assertEqual(self.engine.trading_mode, TradingMode.PAPER)
        self.assertEqual(self.engine.state, TradingState.STOPPED)
    
    def test_engine_start_stop(self):
        """Test engine start and stop"""
        # Start engine
        result = self.engine.start()
        self.assertTrue(result)
        self.assertEqual(self.engine.state, TradingState.RUNNING)
        
        # Stop engine
        self.engine.stop()
        self.assertEqual(self.engine.state, TradingState.STOPPED)
    
    def test_engine_pause_resume(self):
        """Test engine pause and resume"""
        # Start engine first
        self.engine.start()
        
        # Pause
        self.engine.pause()
        self.assertEqual(self.engine.state, TradingState.PAUSED)
        
        # Resume
        self.engine.resume()
        self.assertEqual(self.engine.state, TradingState.RUNNING)
        
        # Cleanup
        self.engine.stop()
    
    def test_strategy_management(self):
        """Test strategy enable/disable"""
        # Start engine
        self.engine.start()
        
        # Test strategy management
        self.engine.disable_strategy('momentum')
        status = self.engine.get_status()
        self.assertNotIn('momentum', status['active_strategies'])
        
        self.engine.enable_strategy('momentum')
        status = self.engine.get_status()
        self.assertIn('momentum', status['active_strategies'])
        
        # Cleanup
        self.engine.stop()
    
    def test_configuration_update(self):
        """Test configuration updates"""
        new_config = {
            'max_positions': 5,
            'risk_per_trade': 0.15,
            'max_daily_trades': 20
        }
        
        self.engine.update_config(new_config)
        status = self.engine.get_status()
        
        self.assertEqual(status['config']['max_positions'], 5)
        self.assertEqual(status['config']['risk_per_trade'], 0.15)
        self.assertEqual(status['config']['max_daily_trades'], 20)
    
    def test_emergency_stop(self):
        """Test emergency stop functionality"""
        # Start engine
        self.engine.start()
        
        # Trigger emergency stop
        self.engine.emergency_stop()
        
        # Should be paused after emergency stop
        status = self.engine.get_status()
        self.assertEqual(status['state'], 'paused')
        
        # Cleanup
        self.engine.stop()


class TestIntegration(unittest.TestCase):
    """Integration tests for complete workflows"""
    
    def test_signal_to_execution_workflow(self):
        """Test complete signal generation to execution workflow"""
        # Initialize components
        signal_generator = SignalGenerator()
        risk_manager = RiskManager()
        paper_engine = PaperTradingEngine()
        
        # Mock market data
        with patch('data.market_data.MarketDataProvider.get_stock_data') as mock_data:
            mock_data.return_value = {
                'prices': [100, 102, 104, 106, 108],
                'volumes': [1000, 1200, 1400, 1600, 1800],
                'timestamps': [datetime.now() - timedelta(days=i) for i in range(5, 0, -1)]
            }
            
            # Generate signals
            signals = signal_generator.generate_signals('AAPL')
            
            if signals:
                signal = signals[0]
                
                # Calculate position size
                sizing_info = risk_manager.calculate_position_size(
                    signal, signal.entry_price, 50000, []
                )
                
                # Execute trade
                if sizing_info['position_size'] > 0:
                    result = paper_engine.place_order(
                        symbol=signal.symbol,
                        quantity=sizing_info['position_size'],
                        side=OrderSide.BUY,
                        price=signal.entry_price,
                        order_type='stock'
                    )
                    
                    self.assertTrue(result['success'])
    
    def test_end_to_end_trading_workflow(self):
        """Test end-to-end trading workflow"""
        # Initialize trading engine
        engine = LiveTradingEngine(TradingMode.PAPER)
        
        try:
            # Start engine
            result = engine.start()
            self.assertTrue(result)
            
            # Let it run briefly
            time.sleep(2)
            
            # Check status
            status = engine.get_status()
            self.assertEqual(status['state'], 'running')
            
            # Test configuration update
            engine.update_config({'max_positions': 2})
            
            # Test strategy management
            engine.disable_strategy('breakout')
            engine.enable_strategy('breakout')
            
            # Test pause/resume
            engine.pause()
            status = engine.get_status()
            self.assertEqual(status['state'], 'paused')
            
            engine.resume()
            status = engine.get_status()
            self.assertEqual(status['state'], 'running')
            
        finally:
            # Always stop the engine
            engine.stop()


def run_comprehensive_tests():
    """Run all tests and generate comprehensive report"""
    print("🧪 AWOT Trading Platform - Comprehensive Test Suite")
    print("=" * 80)
    print("Running automated tests for all platform components...")
    print("=" * 80)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestTechnicalIndicators,
        TestSignalGenerator,
        TestStrategies,
        TestRiskManager,
        TestNotificationManager,
        TestPaperTrading,
        TestRobinhoodClient,
        TestOrderManager,
        TestLiveTradingEngine,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        buffer=True
    )
    
    start_time = time.time()
    result = runner.run(test_suite)
    end_time = time.time()
    
    # Generate summary report
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    passed = total_tests - failures - errors - skipped
    
    print(f"Total Tests Run: {total_tests}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failures}")
    print(f"🚨 Errors: {errors}")
    print(f"⏭️ Skipped: {skipped}")
    print(f"⏱️ Execution Time: {end_time - start_time:.2f} seconds")
    
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    # Component-wise breakdown
    print(f"\n📋 Component Test Results:")
    component_results = {}
    
    for test_class in test_classes:
        class_name = test_class.__name__
        component_name = class_name.replace('Test', '')
        
        # Count tests for this component
        class_tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        component_test_count = class_tests.countTestCases()
        
        # This is a simplified count - in a real implementation,
        # you'd track results per component
        component_results[component_name] = {
            'total': component_test_count,
            'status': '✅ PASS' if failures == 0 and errors == 0 else '❌ FAIL'
        }
    
    for component, results in component_results.items():
        print(f"   {component}: {results['total']} tests - {results['status']}")
    
    # Detailed failure/error reporting
    if failures or errors:
        print(f"\n🔍 DETAILED FAILURE/ERROR REPORT:")
        print("-" * 80)
        
        for test, traceback in result.failures:
            print(f"❌ FAILURE: {test}")
            print(f"   {traceback}")
            print("-" * 40)
        
        for test, traceback in result.errors:
            print(f"🚨 ERROR: {test}")
            print(f"   {traceback}")
            print("-" * 40)
    
    # Overall assessment
    print(f"\n🎯 OVERALL ASSESSMENT:")
    if success_rate >= 95:
        print("🟢 EXCELLENT - Platform is production ready!")
    elif success_rate >= 85:
        print("🟡 GOOD - Minor issues need attention")
    elif success_rate >= 70:
        print("🟠 FAIR - Several issues need fixing")
    else:
        print("🔴 POOR - Major issues require immediate attention")
    
    print("=" * 80)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
