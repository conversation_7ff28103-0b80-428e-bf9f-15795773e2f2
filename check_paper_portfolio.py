#!/usr/bin/env python3
"""
Check Current Paper Trading Portfolio
Shows what's currently trading with paper money
"""

import sys
import os
from datetime import datetime
import json

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from trading.paper_trading import PaperTradingEngine
    from data.market_data import MarketDataProvider
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the AWOT directory")
    sys.exit(1)

def format_currency(amount):
    """Format currency with proper signs"""
    return f"${amount:,.2f}"

def format_percentage(pct):
    """Format percentage with proper signs"""
    sign = "+" if pct >= 0 else ""
    return f"{sign}{pct:.2f}%"

def check_paper_portfolio():
    """Check current paper trading portfolio"""
    print("📊 AWOT Paper Trading Portfolio Status")
    print("=" * 60)
    
    try:
        # Initialize paper trading engine
        paper_engine = PaperTradingEngine()
        
        # Get portfolio summary
        portfolio = paper_engine.get_portfolio_summary()
        
        print(f"\n💰 PORTFOLIO OVERVIEW:")
        print(f"  Initial Capital: {format_currency(portfolio['initial_capital'])}")
        print(f"  Current Value:   {format_currency(portfolio['portfolio_value'])}")
        print(f"  Available Cash:  {format_currency(portfolio['cash'])}")
        print(f"  Total P&L:       {format_currency(portfolio['total_pnl'])} ({format_percentage(portfolio['total_return'] * 100)})")
        print(f"  Unrealized P&L:  {format_currency(portfolio['unrealized_pnl'])}")
        print(f"  Realized P&L:    {format_currency(portfolio['realized_pnl'])}")
        print(f"  Max Drawdown:    {format_percentage(portfolio['max_drawdown'] * 100)}")
        
        # Get current positions
        positions = paper_engine.get_positions()
        open_positions = [p for p in positions if p['is_open']]
        closed_positions = [p for p in positions if not p['is_open']]
        
        print(f"\n📈 CURRENT POSITIONS ({len(open_positions)} open):")
        if open_positions:
            print("  Symbol | Qty | Entry Price | Current Price | P&L      | P&L%     | Type")
            print("  " + "-" * 70)
            
            for pos in open_positions:
                symbol = pos['symbol']
                qty = pos['quantity']
                entry_price = pos['entry_price']
                current_price = pos['current_price']
                pnl = pos['unrealized_pnl']
                pnl_pct = (current_price - entry_price) / entry_price * 100
                pos_type = pos['position_type']
                
                print(f"  {symbol:6} | {qty:3} | {format_currency(entry_price):>10} | {format_currency(current_price):>12} | {format_currency(pnl):>8} | {format_percentage(pnl_pct):>7} | {pos_type}")
        else:
            print("  📭 No open positions")
        
        # Recent trades
        trade_history = paper_engine.get_trade_history()
        recent_trades = sorted(trade_history, key=lambda x: x['timestamp'], reverse=True)[:10]
        
        print(f"\n📋 RECENT TRADES (last 10):")
        if recent_trades:
            print("  Time     | Symbol | Action | Qty | Price     | P&L      | Status")
            print("  " + "-" * 65)
            
            for trade in recent_trades:
                time_str = trade['timestamp'].strftime('%H:%M:%S')
                symbol = trade['symbol']
                action = trade['action']
                qty = trade['quantity']
                price = trade['price']
                pnl = trade.get('realized_pnl', 0)
                status = "CLOSED" if trade.get('realized_pnl') is not None else "OPEN"
                
                print(f"  {time_str} | {symbol:6} | {action:6} | {qty:3} | {format_currency(price):>8} | {format_currency(pnl):>8} | {status}")
        else:
            print("  📭 No recent trades")
        
        # Trading statistics
        stats = portfolio['stats']
        print(f"\n📊 TRADING STATISTICS:")
        print(f"  Total Trades:    {stats['total_trades']}")
        print(f"  Winning Trades:  {stats['winning_trades']}")
        print(f"  Losing Trades:   {stats['losing_trades']}")
        
        if stats['total_trades'] > 0:
            win_rate = stats['winning_trades'] / stats['total_trades'] * 100
            print(f"  Win Rate:        {format_percentage(win_rate)}")
        
        if 'profit_factor' in stats:
            print(f"  Profit Factor:   {stats['profit_factor']:.2f}")
        
        # Check if trading engine is active
        print(f"\n🤖 TRADING ENGINE STATUS:")
        try:
            # Try to check if live trading engine is running
            from trading.live_trading_engine import LiveTradingEngine
            # This is just a status check, not starting anything
            print("  Engine Available: ✅ Ready")
        except Exception as e:
            print(f"  Engine Status: ⚠️ {e}")
        
        # Market data status
        print(f"\n📡 MARKET DATA STATUS:")
        try:
            market_data = MarketDataProvider()
            print("  Market Data: ✅ Available")
            
            # Test with a sample symbol
            test_price = market_data.get_current_price('AAPL')
            if test_price:
                print(f"  Sample Price (AAPL): ${test_price:.2f}")
            else:
                print("  Sample Price: ⚠️ No data (market closed?)")
                
        except Exception as e:
            print(f"  Market Data: ❌ Error: {e}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if len(open_positions) == 0:
            print("  🎯 No open positions - ready to start trading!")
            print("  📈 Consider starting with small positions (10-20 shares)")
            print("  🎯 Watch for signals in your dashboard")
        elif len(open_positions) > 5:
            print("  ⚠️ Many open positions - consider risk management")
            print("  📊 Monitor portfolio concentration")
        else:
            print("  ✅ Good position count - monitor performance")
        
        if portfolio['total_return'] < -0.05:  # -5%
            print("  ⚠️ Portfolio down >5% - review strategy")
        elif portfolio['total_return'] > 0.05:  # +5%
            print("  🎉 Portfolio up >5% - good performance!")
        
        print(f"\n🎯 NEXT STEPS:")
        print("  1. Open your dashboard: http://localhost:8502")
        print("  2. Check the Portfolio tab for real-time updates")
        print("  3. Monitor signals in the Enhanced Signals tab")
        print("  4. Use the Live Trading tab to place paper trades")
        
    except Exception as e:
        print(f"❌ Error checking portfolio: {e}")
        print("\n🔧 Troubleshooting:")
        print("  1. Make sure you're in the AWOT directory")
        print("  2. Activate virtual environment: source venv/bin/activate")
        print("  3. Check if paper trading engine is initialized")

def main():
    """Main function"""
    check_paper_portfolio()

if __name__ == "__main__":
    main()
