import Foundation
import Combine
import SwiftUI

// MARK: - Trading Data Manager

@MainActor
class TradingDataManager: ObservableObject {
    // MARK: - Published Properties
    @Published var portfolio = Portfolio()
    @Published var positions: [Position] = []
    @Published var signals: [TradingSignal] = []
    @Published var engineStatus = "stopped"
    @Published var isConnected = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Computed Properties
    @Published var newSignalsCount = 0
    @Published var todaysTrades = 0
    @Published var winRate: Double = 0
    
    // MARK: - Services
    private let apiService = APIService()
    private let webSocketService = WebSocketService()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        setupWebSocketSubscriptions()
        setupPeriodicUpdates()
    }
    
    // MARK: - Public Methods
    
    func connect() {
        Task {
            await refreshAll()
            webSocketService.connect()
        }
    }
    
    func disconnect() {
        webSocketService.disconnect()
    }
    
    func refreshAll() async {
        isLoading = true
        errorMessage = nil
        
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.refreshPortfolio() }
            group.addTask { await self.refreshSignals() }
            group.addTask { await self.refreshPositions() }
            group.addTask { await self.refreshEngineStatus() }
        }
        
        isLoading = false
    }
    
    func refreshPortfolio() async {
        do {
            let newPortfolio = try await apiService.getPortfolio()
            portfolio = newPortfolio
            todaysTrades = newPortfolio.todaysTrades
            winRate = newPortfolio.winRate ?? 0
            errorMessage = nil
        } catch {
            handleError(error, context: "refreshing portfolio")
        }
    }
    
    func refreshSignals() async {
        do {
            let newSignals = try await apiService.getSignals()
            let previousCount = signals.count
            signals = newSignals
            
            // Update new signals count
            if newSignals.count > previousCount {
                newSignalsCount = newSignals.count - previousCount
                
                // Send local notification for new signals
                sendLocalNotification(
                    title: "New Trading Signals",
                    body: "\(newSignalsCount) new signals available"
                )
            }
            
            errorMessage = nil
        } catch {
            handleError(error, context: "refreshing signals")
        }
    }
    
    func refreshPositions() async {
        do {
            positions = try await apiService.getPositions()
            errorMessage = nil
        } catch {
            handleError(error, context: "refreshing positions")
        }
    }
    
    func refreshEngineStatus() async {
        do {
            let status = try await apiService.getEngineStatus()
            engineStatus = status.state
            errorMessage = nil
        } catch {
            handleError(error, context: "refreshing engine status")
        }
    }
    
    func placeOrder(symbol: String, quantity: Int, side: String, orderType: String = "market", price: Double? = nil) async -> Bool {
        do {
            let order = OrderRequest(
                symbol: symbol,
                quantity: quantity,
                side: side,
                orderType: orderType,
                price: price
            )
            
            let response = try await apiService.placeOrder(order)
            
            if response.success {
                // Refresh data after successful order
                await refreshAll()
                
                // Send success notification
                sendLocalNotification(
                    title: "Order Executed",
                    body: "\(side.capitalized) \(quantity) shares of \(symbol)"
                )
            }
            
            return response.success
        } catch {
            handleError(error, context: "placing order")
            return false
        }
    }
    
    func emergencyStop() async -> Bool {
        do {
            let success = try await apiService.emergencyStop()
            
            if success {
                await refreshAll()
                
                // Send emergency stop notification
                sendLocalNotification(
                    title: "Emergency Stop Activated",
                    body: "All trading has been halted"
                )
            }
            
            return success
        } catch {
            handleError(error, context: "executing emergency stop")
            return false
        }
    }
    
    func checkConnection() async -> Bool {
        do {
            return try await apiService.healthCheck()
        } catch {
            return false
        }
    }
    
    func clearNewSignalsCount() {
        newSignalsCount = 0
    }
    
    // MARK: - Private Methods
    
    private func setupWebSocketSubscriptions() {
        // Portfolio updates
        webSocketService.portfolioUpdates
            .receive(on: DispatchQueue.main)
            .sink { [weak self] portfolio in
                self?.portfolio = portfolio
                self?.todaysTrades = portfolio.todaysTrades
                self?.winRate = portfolio.winRate ?? 0
            }
            .store(in: &cancellables)
        
        // Signal updates
        webSocketService.signalUpdates
            .receive(on: DispatchQueue.main)
            .sink { [weak self] signals in
                guard let self = self else { return }
                
                let previousCount = self.signals.count
                self.signals = signals
                
                if signals.count > previousCount {
                    self.newSignalsCount += signals.count - previousCount
                }
            }
            .store(in: &cancellables)
        
        // Connection status
        webSocketService.connectionStatus
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isConnected in
                self?.isConnected = isConnected
            }
            .store(in: &cancellables)
    }
    
    private func setupPeriodicUpdates() {
        // Refresh data every 30 seconds
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await self?.refreshPortfolio()
                }
            }
            .store(in: &cancellables)
        
        // Check connection every 10 seconds
        Timer.publish(every: 10, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    let connected = await self?.checkConnection() ?? false
                    await MainActor.run {
                        self?.isConnected = connected
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleError(_ error: Error, context: String) {
        print("Error \(context): \(error.localizedDescription)")
        errorMessage = "Error \(context): \(error.localizedDescription)"
        
        // Send error notification for critical errors
        if context.contains("emergency") || context.contains("order") {
            sendLocalNotification(
                title: "Error",
                body: "Failed \(context). Please check your connection."
            )
        }
    }
    
    private func sendLocalNotification(title: String, body: String) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Notification error: \(error)")
            }
        }
    }
}

// MARK: - Extensions for Convenience

extension TradingDataManager {
    var totalPortfolioValue: String {
        portfolio.portfolioValue.formatted(.currency(code: "USD"))
    }
    
    var dailyPnLFormatted: String {
        let sign = portfolio.dailyPnL >= 0 ? "+" : ""
        return "\(sign)\(portfolio.dailyPnL.formatted(.currency(code: "USD")))"
    }
    
    var dailyPnLPercentFormatted: String {
        let sign = portfolio.dailyPnLPercent >= 0 ? "+" : ""
        return "\(sign)\(portfolio.dailyPnLPercent.formatted(.number.precision(.fractionLength(2))))%"
    }
    
    var availableCashFormatted: String {
        portfolio.availableCash.formatted(.currency(code: "USD"))
    }
    
    var winRateFormatted: String {
        if winRate > 0 {
            return "\(Int(winRate * 100))%"
        } else {
            return "--"
        }
    }
    
    var connectionStatusText: String {
        isConnected ? "Connected" : "Disconnected"
    }
    
    var connectionStatusColor: Color {
        isConnected ? .green : .red
    }
    
    var engineStatusColor: Color {
        switch engineStatus.lowercased() {
        case "running":
            return .green
        case "stopped":
            return .red
        case "paused":
            return .orange
        default:
            return .gray
        }
    }
}

// MARK: - Sample Data for Previews

extension TradingDataManager {
    static var preview: TradingDataManager {
        let manager = TradingDataManager()
        
        // Sample portfolio
        manager.portfolio = Portfolio()
        
        // Sample positions
        manager.positions = [
            Position(
                symbol: "AAPL",
                quantity: 100,
                avgPrice: 200.00,
                currentPrice: 211.22,
                marketValue: 21122.00,
                unrealizedPnL: 1122.00,
                unrealizedPnLPercent: 5.61,
                dayChange: 2.50,
                dayChangePercent: 1.20
            ),
            Position(
                symbol: "TSLA",
                quantity: 50,
                avgPrice: 320.00,
                currentPrice: 329.55,
                marketValue: 16477.50,
                unrealizedPnL: 477.50,
                unrealizedPnLPercent: 2.98,
                dayChange: -5.25,
                dayChangePercent: -1.57
            )
        ]
        
        // Sample signals
        manager.signals = [
            TradingSignal(
                id: "1",
                symbol: "AAPL",
                signalType: "momentum_bullish",
                strength: "STRONG",
                confidence: 0.85,
                entryPrice: 211.22,
                targetPrice: 230.00,
                stopLossPrice: 200.00,
                sentiment: "positive",
                sentimentScore: 0.6,
                reasoning: "Strong momentum with positive earnings sentiment",
                createdAt: Date().ISO8601String(),
                expiresAt: Date().addingTimeInterval(3600).ISO8601String(),
                riskRewardRatio: 1.67
            )
        ]
        
        manager.isConnected = true
        manager.engineStatus = "running"
        manager.todaysTrades = 5
        manager.winRate = 0.75
        
        return manager
    }
}
