import SwiftUI
import UserNotifications

@main
struct AWOTApp: App {
    @StateObject private var tradingData = TradingDataManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(tradingData)
                .onAppear {
                    requestNotificationPermission()
                    tradingData.connect()
                }
        }
    }
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            }
        }
    }
}

struct ContentView: View {
    @EnvironmentObject var tradingData: TradingDataManager
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            PortfolioView()
                .tabItem {
                    Image(systemName: "chart.pie.fill")
                    Text("Portfolio")
                }
                .tag(0)
            
            SignalsView()
                .tabItem {
                    Image(systemName: "bolt.fill")
                    Text("Signals")
                }
                .tag(1)
                .badge(tradingData.newSignalsCount > 0 ? tradingData.newSignalsCount : nil)
            
            TradingView()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("Trading")
                }
                .tag(2)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(3)
        }
        .accentColor(.blue)
    }
}

struct PortfolioView: View {
    @EnvironmentObject var tradingData: TradingDataManager
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Portfolio Summary Card
                    PortfolioSummaryCard(portfolio: tradingData.portfolio)
                    
                    // Quick Stats
                    QuickStatsCard(
                        openPositions: tradingData.positions.count,
                        todaysTrades: tradingData.todaysTrades,
                        winRate: tradingData.winRate
                    )
                    
                    // Positions List
                    if !tradingData.positions.isEmpty {
                        PositionsListCard(positions: tradingData.positions)
                    }
                }
                .padding()
            }
            .navigationTitle("Portfolio")
            .refreshable {
                await tradingData.refreshPortfolio()
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        Task { await tradingData.refreshAll() }
                    }) {
                        Image(systemName: "arrow.clockwise")
                    }
                }
            }
        }
    }
}

struct PortfolioSummaryCard: View {
    let portfolio: Portfolio
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Portfolio Value")
                    .font(.headline)
                    .foregroundColor(.secondary)
                Spacer()
                Text(portfolio.lastUpdated, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(portfolio.portfolioValue, format: .currency(code: "USD"))
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Today's P&L")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack {
                        Text(portfolio.dailyPnL, format: .currency(code: "USD"))
                            .fontWeight(.semibold)
                        
                        Text("(\(portfolio.dailyPnLPercent, specifier: "%.2f")%)")
                            .font(.caption)
                    }
                    .foregroundColor(portfolio.dailyPnL >= 0 ? .green : .red)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Available Cash")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(portfolio.availableCash, format: .currency(code: "USD"))
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct QuickStatsCard: View {
    let openPositions: Int
    let todaysTrades: Int
    let winRate: Double
    
    var body: some View {
        HStack(spacing: 20) {
            StatItem(title: "Positions", value: "\(openPositions)", color: .blue)
            StatItem(title: "Trades Today", value: "\(todaysTrades)", color: .orange)
            StatItem(title: "Win Rate", value: winRate > 0 ? "\(Int(winRate * 100))%" : "--", color: .green)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct StatItem: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct SignalsView: View {
    @EnvironmentObject var tradingData: TradingDataManager
    @State private var selectedSignal: TradingSignal?
    @State private var showingSignalDetail = false
    
    var body: some View {
        NavigationView {
            List {
                ForEach(tradingData.signals) { signal in
                    SignalCard(signal: signal)
                        .onTapGesture {
                            selectedSignal = signal
                            showingSignalDetail = true
                        }
                        .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                        .listRowSeparator(.hidden)
                }
            }
            .listStyle(PlainListStyle())
            .navigationTitle("Trading Signals")
            .refreshable {
                await tradingData.refreshSignals()
            }
            .sheet(isPresented: $showingSignalDetail) {
                if let signal = selectedSignal {
                    SignalDetailView(signal: signal)
                }
            }
        }
    }
}

struct SignalCard: View {
    let signal: TradingSignal
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading) {
                    Text(signal.symbol)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(signal.signalType.replacingOccurrences(of: "_", with: " ").capitalized)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.2))
                        .cornerRadius(8)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("\(Int(signal.confidence * 100))%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text("Confidence")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            HStack {
                SentimentIndicator(sentiment: signal.sentiment, score: signal.sentimentScore)
                
                Spacer()
                
                Text(signal.createdAt, style: .relative)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack(spacing: 16) {
                PriceInfo(label: "Entry", price: signal.entryPrice)
                PriceInfo(label: "Target", price: signal.targetPrice)
                PriceInfo(label: "Stop", price: signal.stopLossPrice)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct TradingView: View {
    @EnvironmentObject var tradingData: TradingDataManager
    @State private var showingEmergencyAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Engine Status Card
                EngineStatusCard(
                    status: tradingData.engineStatus,
                    isConnected: tradingData.isConnected
                )
                
                // Emergency Stop Button
                Button(action: {
                    showingEmergencyAlert = true
                }) {
                    HStack {
                        Image(systemName: "stop.circle.fill")
                        Text("EMERGENCY STOP")
                            .fontWeight(.bold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.red)
                    .cornerRadius(12)
                }
                .alert("Emergency Stop", isPresented: $showingEmergencyAlert) {
                    Button("Cancel", role: .cancel) { }
                    Button("STOP ALL TRADING", role: .destructive) {
                        Task {
                            await tradingData.emergencyStop()
                        }
                    }
                } message: {
                    Text("This will immediately stop all trading activity and cancel pending orders. Are you sure?")
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Trading Controls")
        }
    }
}

struct SettingsView: View {
    var body: some View {
        NavigationView {
            List {
                Section("Trading") {
                    HStack {
                        Text("Trading Mode")
                        Spacer()
                        Text("Paper Trading")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Risk Level")
                        Spacer()
                        Text("Conservative")
                            .foregroundColor(.secondary)
                    }
                }
                
                Section("Notifications") {
                    Toggle("Signal Alerts", isOn: .constant(true))
                    Toggle("Portfolio Alerts", isOn: .constant(true))
                    Toggle("Risk Alerts", isOn: .constant(true))
                }
                
                Section("About") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Settings")
        }
    }
}
