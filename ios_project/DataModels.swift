import Foundation
import Combine

// MARK: - Data Models

struct Portfolio: Codable {
    let portfolioValue: Double
    let dailyPnL: Double
    let dailyPnLPercent: Double
    let availableCash: Double
    let openPositions: Int
    let todaysTrades: Int
    let winRate: Double?
    let totalReturn: Double
    let totalReturnPercent: Double
    let lastUpdated: String
    
    private enum CodingKeys: String, CodingKey {
        case portfolioValue = "portfolio_value"
        case dailyPnL = "daily_pnl"
        case dailyPnLPercent = "daily_pnl_percent"
        case availableCash = "available_cash"
        case openPositions = "open_positions"
        case todaysTrades = "todays_trades"
        case winRate = "win_rate"
        case totalReturn = "total_return"
        case totalReturnPercent = "total_return_percent"
        case lastUpdated = "last_updated"
    }
    
    init() {
        portfolioValue = 50000
        dailyPnL = 0
        dailyPnLPercent = 0
        availableCash = 50000
        openPositions = 0
        todaysTrades = 0
        winRate = nil
        totalReturn = 0
        totalReturnPercent = 0
        lastUpdated = Date().ISO8601String()
    }
}

struct Position: Codable, Identifiable {
    let id = UUID()
    let symbol: String
    let quantity: Int
    let avgPrice: Double
    let currentPrice: Double
    let marketValue: Double
    let unrealizedPnL: Double
    let unrealizedPnLPercent: Double
    let dayChange: Double
    let dayChangePercent: Double
    
    private enum CodingKeys: String, CodingKey {
        case symbol, quantity
        case avgPrice = "avg_price"
        case currentPrice = "current_price"
        case marketValue = "market_value"
        case unrealizedPnL = "unrealized_pnl"
        case unrealizedPnLPercent = "unrealized_pnl_percent"
        case dayChange = "day_change"
        case dayChangePercent = "day_change_percent"
    }
}

struct TradingSignal: Codable, Identifiable {
    let id: String
    let symbol: String
    let signalType: String
    let strength: String
    let confidence: Double
    let entryPrice: Double
    let targetPrice: Double
    let stopLossPrice: Double
    let sentiment: String
    let sentimentScore: Double
    let reasoning: String
    let createdAt: String
    let expiresAt: String
    let riskRewardRatio: Double
    
    private enum CodingKeys: String, CodingKey {
        case id, symbol, strength, confidence, sentiment, reasoning
        case signalType = "signal_type"
        case entryPrice = "entry_price"
        case targetPrice = "target_price"
        case stopLossPrice = "stop_loss_price"
        case sentimentScore = "sentiment_score"
        case createdAt = "created_at"
        case expiresAt = "expires_at"
        case riskRewardRatio = "risk_reward_ratio"
    }
}

struct OrderRequest: Codable {
    let symbol: String
    let quantity: Int
    let side: String
    let orderType: String
    let price: Double?
    
    private enum CodingKeys: String, CodingKey {
        case symbol, quantity, side, price
        case orderType = "order_type"
    }
}

struct OrderResponse: Codable {
    let success: Bool
    let message: String
    let orderId: String?
    
    private enum CodingKeys: String, CodingKey {
        case success, message
        case orderId = "order_id"
    }
}

struct EngineStatus: Codable {
    let state: String
    let tradingMode: String
    let activeStrategies: [String]
    let uptime: String
    let lastSignalTime: String?
    let ordersToday: Int
    let performanceToday: Double
    
    private enum CodingKeys: String, CodingKey {
        case state, uptime
        case tradingMode = "trading_mode"
        case activeStrategies = "active_strategies"
        case lastSignalTime = "last_signal_time"
        case ordersToday = "orders_today"
        case performanceToday = "performance_today"
    }
}

// MARK: - API Service

class APIService: ObservableObject {
    // IMPORTANT: Change this to your server's IP address
    // For local testing: "http://192.168.1.XXX:8080/mobile"
    // For production: "https://your-domain.com/mobile"
    private let baseURL = "http://localhost:8080/mobile"
    
    private let session = URLSession.shared
    
    func getPortfolio() async throws -> Portfolio {
        let url = URL(string: "\(baseURL)/portfolio")!
        let (data, response) = try await session.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONDecoder().decode(Portfolio.self, from: data)
    }
    
    func getPositions() async throws -> [Position] {
        let url = URL(string: "\(baseURL)/positions")!
        let (data, response) = try await session.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONDecoder().decode([Position].self, from: data)
    }
    
    func getSignals() async throws -> [TradingSignal] {
        let url = URL(string: "\(baseURL)/signals")!
        let (data, response) = try await session.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONDecoder().decode([TradingSignal].self, from: data)
    }
    
    func getEngineStatus() async throws -> EngineStatus {
        let url = URL(string: "\(baseURL)/engine-status")!
        let (data, response) = try await session.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONDecoder().decode(EngineStatus.self, from: data)
    }
    
    func placeOrder(_ order: OrderRequest) async throws -> OrderResponse {
        let url = URL(string: "\(baseURL)/order")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let jsonData = try JSONEncoder().encode(order)
        request.httpBody = jsonData
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        return try JSONDecoder().decode(OrderResponse.self, from: data)
    }
    
    func emergencyStop() async throws -> Bool {
        let url = URL(string: "\(baseURL)/emergency-stop")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        let result = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        return result?["success"] as? Bool ?? false
    }
    
    func healthCheck() async throws -> Bool {
        let url = URL(string: "\(baseURL)/health")!
        let (_, response) = try await session.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            return false
        }
        
        return httpResponse.statusCode == 200
    }
}

// MARK: - WebSocket Service

class WebSocketService: ObservableObject {
    @Published var isConnected = false
    
    let portfolioUpdates = PassthroughSubject<Portfolio, Never>()
    let signalUpdates = PassthroughSubject<[TradingSignal], Never>()
    let connectionStatus = PassthroughSubject<Bool, Never>()
    
    private var webSocketTask: URLSessionWebSocketTask?
    
    // IMPORTANT: Change this to your server's WebSocket URL
    private let wsURL = URL(string: "ws://localhost:8080/mobile/ws")!
    
    func connect() {
        disconnect() // Ensure clean state
        
        webSocketTask = URLSession.shared.webSocketTask(with: wsURL)
        webSocketTask?.resume()
        
        isConnected = true
        connectionStatus.send(true)
        
        receiveMessage()
    }
    
    func disconnect() {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        isConnected = false
        connectionStatus.send(false)
    }
    
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self?.handleMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        self?.handleMessage(text)
                    }
                @unknown default:
                    break
                }
                
                // Continue receiving messages
                self?.receiveMessage()
                
            case .failure(let error):
                print("WebSocket error: \(error)")
                DispatchQueue.main.async {
                    self?.isConnected = false
                    self?.connectionStatus.send(false)
                }
                
                // Attempt to reconnect after delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                    self?.connect()
                }
            }
        }
    }
    
    private func handleMessage(_ text: String) {
        guard let data = text.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let type = json["type"] as? String else {
            return
        }
        
        DispatchQueue.main.async {
            switch type {
            case "portfolio_update":
                if let portfolioData = json["data"] as? [String: Any],
                   let jsonData = try? JSONSerialization.data(withJSONObject: portfolioData),
                   let portfolio = try? JSONDecoder().decode(Portfolio.self, from: jsonData) {
                    self.portfolioUpdates.send(portfolio)
                }
                
            case "signal_update":
                if let signalsData = json["data"] as? [[String: Any]],
                   let jsonData = try? JSONSerialization.data(withJSONObject: signalsData),
                   let signals = try? JSONDecoder().decode([TradingSignal].self, from: jsonData) {
                    self.signalUpdates.send(signals)
                }
                
            default:
                break
            }
        }
    }
}

// MARK: - Error Types

enum APIError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case networkError
    case decodingError
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .invalidResponse:
            return "Invalid response from server"
        case .networkError:
            return "Network connection error"
        case .decodingError:
            return "Failed to decode response"
        }
    }
}

// MARK: - Extensions

extension Date {
    func ISO8601String() -> String {
        let formatter = ISO8601DateFormatter()
        return formatter.string(from: self)
    }
}

extension String {
    func toDate() -> Date? {
        let formatter = ISO8601DateFormatter()
        return formatter.date(from: self)
    }
}
