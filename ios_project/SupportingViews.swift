import SwiftUI

// MARK: - Supporting Views and Components

struct PriceInfo: View {
    let label: String
    let price: Double
    
    var body: some View {
        VStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(price, format: .currency(code: "USD"))
                .font(.caption)
                .fontWeight(.semibold)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct SentimentIndicator: View {
    let sentiment: String
    let score: Double
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: sentimentIcon)
                .foregroundColor(sentimentColor)
            
            Text(sentiment.capitalized)
                .font(.caption)
                .foregroundColor(sentimentColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(sentimentColor.opacity(0.2))
        .cornerRadius(8)
    }
    
    private var sentimentIcon: String {
        switch sentiment.lowercased() {
        case "very_positive": return "arrow.up.circle.fill"
        case "positive": return "arrow.up.circle"
        case "negative": return "arrow.down.circle"
        case "very_negative": return "arrow.down.circle.fill"
        default: return "minus.circle"
        }
    }
    
    private var sentimentColor: Color {
        switch sentiment.lowercased() {
        case "very_positive", "positive": return .green
        case "negative", "very_negative": return .red
        default: return .gray
        }
    }
}

struct EngineStatusCard: View {
    let status: String
    let isConnected: Bool
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Trading Engine")
                    .font(.headline)
                
                Spacer()
                
                Circle()
                    .fill(isConnected ? Color.green : Color.red)
                    .frame(width: 12, height: 12)
            }
            
            HStack {
                Text("Status:")
                    .foregroundColor(.secondary)
                
                Text(status.capitalized)
                    .fontWeight(.semibold)
                    .foregroundColor(status == "running" ? .green : .orange)
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct PositionsListCard: View {
    let positions: [Position]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Positions")
                .font(.headline)
            
            ForEach(positions) { position in
                PositionRow(position: position)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct PositionRow: View {
    let position: Position
    
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text(position.symbol)
                    .fontWeight(.semibold)
                
                Text("\(position.quantity) shares")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing) {
                Text(position.unrealizedPnL, format: .currency(code: "USD"))
                    .fontWeight(.semibold)
                    .foregroundColor(position.unrealizedPnL >= 0 ? .green : .red)
                
                Text("\(position.unrealizedPnLPercent, specifier: "%.1f")%")
                    .font(.caption)
                    .foregroundColor(position.unrealizedPnL >= 0 ? .green : .red)
            }
        }
        .padding(.vertical, 4)
    }
}

struct SignalDetailView: View {
    let signal: TradingSignal
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Signal Header
                    VStack(alignment: .leading, spacing: 8) {
                        Text(signal.symbol)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text(signal.signalType.replacingOccurrences(of: "_", with: " ").capitalized)
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                    
                    // Confidence and Sentiment
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Confidence")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text("\(Int(signal.confidence * 100))%")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                        }
                        
                        Spacer()
                        
                        SentimentIndicator(sentiment: signal.sentiment, score: signal.sentimentScore)
                    }
                    
                    // Price Levels
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Price Levels")
                            .font(.headline)
                        
                        PriceLevelRow(label: "Entry Price", price: signal.entryPrice, color: .blue)
                        PriceLevelRow(label: "Target Price", price: signal.targetPrice, color: .green)
                        PriceLevelRow(label: "Stop Loss", price: signal.stopLossPrice, color: .red)
                    }
                    
                    // Risk/Reward
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Risk/Reward Analysis")
                            .font(.headline)
                        
                        let risk = abs(signal.entryPrice - signal.stopLossPrice)
                        let reward = abs(signal.targetPrice - signal.entryPrice)
                        let ratio = reward / risk
                        
                        Text("Risk/Reward Ratio: \(ratio, specifier: "%.2f"):1")
                            .font(.subheadline)
                            .foregroundColor(ratio >= 1.5 ? .green : .orange)
                    }
                    
                    // Reasoning
                    if !signal.reasoning.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Analysis")
                                .font(.headline)
                            
                            Text(signal.reasoning)
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Signal Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct PriceLevelRow: View {
    let label: String
    let price: Double
    let color: Color
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(price, format: .currency(code: "USD"))
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Loading and Error Views

struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

struct ErrorView: View {
    let message: String
    let retryAction: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("Error")
                .font(.title)
                .fontWeight(.bold)
            
            Text(message)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Retry") {
                retryAction()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

struct EmptyStateView: View {
    let title: String
    let message: String
    let systemImage: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: systemImage)
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text(title)
                .font(.title2)
                .fontWeight(.semibold)
            
            Text(message)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Custom Button Styles

struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.blue)
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.blue)
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct DangerButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.red)
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Extensions

extension View {
    func cardStyle() -> some View {
        self
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(radius: 2)
    }
}
