import SwiftUI
import UserNotifications
import Foundation
import Combine

// MARK: - Data Models
struct Portfolio: Codable {
    let portfolioValue: Double
    let dailyPnL: Double
    let dailyPnLPercent: Double
    let availableCash: Double
    let openPositions: Int
    let todaysTrades: Int
    let winRate: Double?
    let totalReturn: Double
    let totalReturnPercent: Double
    let lastUpdated: String

    private enum CodingKeys: String, CodingKey {
        case portfolioValue = "portfolio_value"
        case dailyPnL = "daily_pnl"
        case dailyPnLPercent = "daily_pnl_percent"
        case availableCash = "available_cash"
        case openPositions = "open_positions"
        case todaysTrades = "todays_trades"
        case winRate = "win_rate"
        case totalReturn = "total_return"
        case totalReturnPercent = "total_return_percent"
        case lastUpdated = "last_updated"
    }

    init() {
        portfolioValue = 50000
        dailyPnL = 0
        dailyPnLPercent = 0
        availableCash = 50000
        openPositions = 0
        todaysTrades = 0
        winRate = nil
        totalReturn = 0
        totalReturnPercent = 0
        lastUpdated = Date().ISO8601String()
    }
}

struct Position: Codable, Identifiable {
    let id = UUID()
    let symbol: String
    let quantity: Int
    let avgPrice: Double
    let currentPrice: Double
    let marketValue: Double
    let unrealizedPnL: Double
    let unrealizedPnLPercent: Double
    let dayChange: Double
    let dayChangePercent: Double

    private enum CodingKeys: String, CodingKey {
        case symbol, quantity
        case avgPrice = "avg_price"
        case currentPrice = "current_price"
        case marketValue = "market_value"
        case unrealizedPnL = "unrealized_pnl"
        case unrealizedPnLPercent = "unrealized_pnl_percent"
        case dayChange = "day_change"
        case dayChangePercent = "day_change_percent"
    }
}

struct TradingSignal: Codable, Identifiable {
    let id: String
    let symbol: String
    let signalType: String
    let strength: String
    let confidence: Double
    let entryPrice: Double
    let targetPrice: Double
    let stopLossPrice: Double
    let sentiment: String
    let sentimentScore: Double
    let reasoning: String
    let createdAt: String
    let expiresAt: String
    let riskRewardRatio: Double

    private enum CodingKeys: String, CodingKey {
        case id, symbol, strength, confidence, sentiment, reasoning
        case signalType = "signal_type"
        case entryPrice = "entry_price"
        case targetPrice = "target_price"
        case stopLossPrice = "stop_loss_price"
        case sentimentScore = "sentiment_score"
        case createdAt = "created_at"
        case expiresAt = "expires_at"
        case riskRewardRatio = "risk_reward_ratio"
    }
}

// MARK: - API Service
class APIService: ObservableObject {
    private let baseURL = "http://localhost:8080/mobile"
    private let session = URLSession.shared

    func getPortfolio() async throws -> Portfolio {
        let url = URL(string: "\(baseURL)/portfolio")!
        let (data, _) = try await session.data(from: url)
        return try JSONDecoder().decode(Portfolio.self, from: data)
    }

    func getPositions() async throws -> [Position] {
        let url = URL(string: "\(baseURL)/positions")!
        let (data, _) = try await session.data(from: url)
        return try JSONDecoder().decode([Position].self, from: data)
    }

    func getSignals() async throws -> [TradingSignal] {
        let url = URL(string: "\(baseURL)/signals")!
        let (data, _) = try await session.data(from: url)
        return try JSONDecoder().decode([TradingSignal].self, from: data)
    }

    func emergencyStop() async throws -> Bool {
        let url = URL(string: "\(baseURL)/emergency-stop")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"

        let (data, _) = try await session.data(for: request)
        let response = try JSONDecoder().decode([String: Any].self, from: data) as? [String: Bool]
        return response?["success"] ?? false
    }
}

// MARK: - WebSocket Service
class WebSocketService: ObservableObject {
    @Published var isConnected = false
    @Published var portfolioUpdates = PassthroughSubject<Portfolio, Never>()
    @Published var connectionStatus = PassthroughSubject<Bool, Never>()

    private var webSocketTask: URLSessionWebSocketTask?
    private let url = URL(string: "ws://localhost:8080/mobile/ws")!

    func connect() {
        webSocketTask = URLSession.shared.webSocketTask(with: url)
        webSocketTask?.resume()

        isConnected = true
        connectionStatus.send(true)

        receiveMessage()
    }

    func disconnect() {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        isConnected = false
        connectionStatus.send(false)
    }

    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self?.handleMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        self?.handleMessage(text)
                    }
                @unknown default:
                    break
                }

                // Continue receiving messages
                self?.receiveMessage()

            case .failure(let error):
                print("WebSocket error: \(error)")
                DispatchQueue.main.async {
                    self?.isConnected = false
                    self?.connectionStatus.send(false)
                }
            }
        }
    }

    private func handleMessage(_ text: String) {
        guard let data = text.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let type = json["type"] as? String else {
            return
        }

        DispatchQueue.main.async {
            switch type {
            case "portfolio_update":
                if let portfolioData = json["data"] as? [String: Any],
                   let jsonData = try? JSONSerialization.data(withJSONObject: portfolioData),
                   let portfolio = try? JSONDecoder().decode(Portfolio.self, from: jsonData) {
                    self.portfolioUpdates.send(portfolio)
                }
            default:
                break
            }
        }
    }
}

// MARK: - Trading Data Manager
@MainActor
class TradingDataManager: ObservableObject {
    @Published var portfolio = Portfolio()
    @Published var positions: [Position] = []
    @Published var signals: [TradingSignal] = []
    @Published var isConnected = false
    @Published var newSignalsCount = 0
    @Published var todaysTrades = 0
    @Published var winRate: Double = 0
    @Published var engineStatus = "stopped"

    private let apiService = APIService()
    private let webSocketService = WebSocketService()
    private var cancellables = Set<AnyCancellable>()

    init() {
        setupWebSocketSubscriptions()
    }

    func connect() {
        Task {
            await refreshAll()
            webSocketService.connect()
        }
    }

    func refreshAll() async {
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.refreshPortfolio() }
            group.addTask { await self.refreshSignals() }
            group.addTask { await self.refreshPositions() }
        }
    }

    func refreshPortfolio() async {
        do {
            portfolio = try await apiService.getPortfolio()
            todaysTrades = portfolio.todaysTrades
            winRate = portfolio.winRate ?? 0
        } catch {
            print("Error refreshing portfolio: \(error)")
        }
    }

    func refreshSignals() async {
        do {
            let newSignals = try await apiService.getSignals()
            let previousCount = signals.count
            signals = newSignals

            if newSignals.count > previousCount {
                newSignalsCount = newSignals.count - previousCount
            }
        } catch {
            print("Error refreshing signals: \(error)")
        }
    }

    func refreshPositions() async {
        do {
            positions = try await apiService.getPositions()
        } catch {
            print("Error refreshing positions: \(error)")
        }
    }

    func emergencyStop() async {
        do {
            let success = try await apiService.emergencyStop()
            if success {
                await refreshAll()
            }
        } catch {
            print("Error executing emergency stop: \(error)")
        }
    }

    private func setupWebSocketSubscriptions() {
        webSocketService.portfolioUpdates
            .receive(on: DispatchQueue.main)
            .sink { [weak self] portfolio in
                self?.portfolio = portfolio
            }
            .store(in: &cancellables)

        webSocketService.connectionStatus
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isConnected in
                self?.isConnected = isConnected
            }
            .store(in: &cancellables)
    }
}

// MARK: - Extensions
extension Date {
    func ISO8601String() -> String {
        let formatter = ISO8601DateFormatter()
        return formatter.string(from: self)
    }
}

@main
struct AWOTApp: App {
    @StateObject private var tradingData = TradingDataManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(tradingData)
                .onAppear {
                    requestNotificationPermission()
                    tradingData.connect()
                }
        }
    }
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            }
        }
    }
}

struct ContentView: View {
    @EnvironmentObject var tradingData: TradingDataManager
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            PortfolioView()
                .tabItem {
                    Image(systemName: "chart.pie.fill")
                    Text("Portfolio")
                }
                .tag(0)
            
            SignalsView()
                .tabItem {
                    Image(systemName: "bolt.fill")
                    Text("Signals")
                }
                .tag(1)
                .badge(tradingData.newSignalsCount > 0 ? tradingData.newSignalsCount : nil)
            
            TradingView()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("Trading")
                }
                .tag(2)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(3)
        }
        .accentColor(.blue)
    }
}

struct PortfolioView: View {
    @EnvironmentObject var tradingData: TradingDataManager
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Portfolio Summary Card
                    PortfolioSummaryCard(portfolio: tradingData.portfolio)
                    
                    // Quick Stats
                    QuickStatsCard(
                        openPositions: tradingData.positions.count,
                        todaysTrades: tradingData.todaysTrades,
                        winRate: tradingData.winRate
                    )
                    
                    // Positions List
                    if !tradingData.positions.isEmpty {
                        PositionsListCard(positions: tradingData.positions)
                    }
                }
                .padding()
            }
            .navigationTitle("Portfolio")
            .refreshable {
                await tradingData.refreshPortfolio()
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        Task { await tradingData.refreshAll() }
                    }) {
                        Image(systemName: "arrow.clockwise")
                    }
                }
            }
        }
    }
}

struct PortfolioSummaryCard: View {
    let portfolio: Portfolio
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Portfolio Value")
                    .font(.headline)
                    .foregroundColor(.secondary)
                Spacer()
                Text(portfolio.lastUpdated, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(portfolio.portfolioValue, format: .currency(code: "USD"))
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Today's P&L")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack {
                        Text(portfolio.dailyPnL, format: .currency(code: "USD"))
                            .fontWeight(.semibold)
                        
                        Text("(\(portfolio.dailyPnLPercent, specifier: "%.2f")%)")
                            .font(.caption)
                    }
                    .foregroundColor(portfolio.dailyPnL >= 0 ? .green : .red)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Available Cash")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(portfolio.availableCash, format: .currency(code: "USD"))
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct QuickStatsCard: View {
    let openPositions: Int
    let todaysTrades: Int
    let winRate: Double
    
    var body: some View {
        HStack(spacing: 20) {
            StatItem(title: "Positions", value: "\(openPositions)", color: .blue)
            StatItem(title: "Trades Today", value: "\(todaysTrades)", color: .orange)
            StatItem(title: "Win Rate", value: winRate > 0 ? "\(Int(winRate * 100))%" : "--", color: .green)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct StatItem: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct SignalsView: View {
    @EnvironmentObject var tradingData: TradingDataManager
    @State private var selectedSignal: TradingSignal?
    @State private var showingSignalDetail = false
    
    var body: some View {
        NavigationView {
            List {
                ForEach(tradingData.signals) { signal in
                    SignalCard(signal: signal)
                        .onTapGesture {
                            selectedSignal = signal
                            showingSignalDetail = true
                        }
                        .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                        .listRowSeparator(.hidden)
                }
            }
            .listStyle(PlainListStyle())
            .navigationTitle("Trading Signals")
            .refreshable {
                await tradingData.refreshSignals()
            }
            .sheet(isPresented: $showingSignalDetail) {
                if let signal = selectedSignal {
                    SignalDetailView(signal: signal)
                }
            }
        }
    }
}

struct SignalCard: View {
    let signal: TradingSignal
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading) {
                    Text(signal.symbol)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(signal.signalType.replacingOccurrences(of: "_", with: " ").capitalized)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.2))
                        .cornerRadius(8)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("\(Int(signal.confidence * 100))%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text("Confidence")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            HStack {
                SentimentIndicator(sentiment: signal.sentiment, score: signal.sentimentScore)
                
                Spacer()
                
                Text(signal.createdAt, style: .relative)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack(spacing: 16) {
                PriceInfo(label: "Entry", price: signal.entryPrice)
                PriceInfo(label: "Target", price: signal.targetPrice)
                PriceInfo(label: "Stop", price: signal.stopLossPrice)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct PriceInfo: View {
    let label: String
    let price: Double
    
    var body: some View {
        VStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(price, format: .currency(code: "USD"))
                .font(.caption)
                .fontWeight(.semibold)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct SentimentIndicator: View {
    let sentiment: String
    let score: Double
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: sentimentIcon)
                .foregroundColor(sentimentColor)
            
            Text(sentiment.capitalized)
                .font(.caption)
                .foregroundColor(sentimentColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(sentimentColor.opacity(0.2))
        .cornerRadius(8)
    }
    
    private var sentimentIcon: String {
        switch sentiment.lowercased() {
        case "very_positive": return "arrow.up.circle.fill"
        case "positive": return "arrow.up.circle"
        case "negative": return "arrow.down.circle"
        case "very_negative": return "arrow.down.circle.fill"
        default: return "minus.circle"
        }
    }
    
    private var sentimentColor: Color {
        switch sentiment.lowercased() {
        case "very_positive", "positive": return .green
        case "negative", "very_negative": return .red
        default: return .gray
        }
    }
}

struct TradingView: View {
    @EnvironmentObject var tradingData: TradingDataManager
    @State private var showingEmergencyAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Engine Status Card
                EngineStatusCard(
                    status: tradingData.engineStatus,
                    isConnected: tradingData.isConnected
                )
                
                // Emergency Stop Button
                Button(action: {
                    showingEmergencyAlert = true
                }) {
                    HStack {
                        Image(systemName: "stop.circle.fill")
                        Text("EMERGENCY STOP")
                            .fontWeight(.bold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.red)
                    .cornerRadius(12)
                }
                .alert("Emergency Stop", isPresented: $showingEmergencyAlert) {
                    Button("Cancel", role: .cancel) { }
                    Button("STOP ALL TRADING", role: .destructive) {
                        Task {
                            await tradingData.emergencyStop()
                        }
                    }
                } message: {
                    Text("This will immediately stop all trading activity and cancel pending orders. Are you sure?")
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Trading Controls")
        }
    }
}

struct EngineStatusCard: View {
    let status: String
    let isConnected: Bool
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Trading Engine")
                    .font(.headline)
                
                Spacer()
                
                Circle()
                    .fill(isConnected ? Color.green : Color.red)
                    .frame(width: 12, height: 12)
            }
            
            HStack {
                Text("Status:")
                    .foregroundColor(.secondary)
                
                Text(status.capitalized)
                    .fontWeight(.semibold)
                    .foregroundColor(status == "running" ? .green : .orange)
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct SettingsView: View {
    var body: some View {
        NavigationView {
            List {
                Section("Trading") {
                    HStack {
                        Text("Trading Mode")
                        Spacer()
                        Text("Paper Trading")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Risk Level")
                        Spacer()
                        Text("Conservative")
                            .foregroundColor(.secondary)
                    }
                }
                
                Section("Notifications") {
                    Toggle("Signal Alerts", isOn: .constant(true))
                    Toggle("Portfolio Alerts", isOn: .constant(true))
                    Toggle("Risk Alerts", isOn: .constant(true))
                }
                
                Section("About") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Settings")
        }
    }
}

struct PositionsListCard: View {
    let positions: [Position]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Positions")
                .font(.headline)
            
            ForEach(positions) { position in
                PositionRow(position: position)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct PositionRow: View {
    let position: Position
    
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text(position.symbol)
                    .fontWeight(.semibold)
                
                Text("\(position.quantity) shares")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing) {
                Text(position.unrealizedPnL, format: .currency(code: "USD"))
                    .fontWeight(.semibold)
                    .foregroundColor(position.unrealizedPnL >= 0 ? .green : .red)
                
                Text("\(position.unrealizedPnLPercent, specifier: "%.1f")%")
                    .font(.caption)
                    .foregroundColor(position.unrealizedPnL >= 0 ? .green : .red)
            }
        }
        .padding(.vertical, 4)
    }
}

struct SignalDetailView: View {
    let signal: TradingSignal
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Signal Header
                    VStack(alignment: .leading, spacing: 8) {
                        Text(signal.symbol)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text(signal.signalType.replacingOccurrences(of: "_", with: " ").capitalized)
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                    
                    // Confidence and Sentiment
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Confidence")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text("\(Int(signal.confidence * 100))%")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                        }
                        
                        Spacer()
                        
                        SentimentIndicator(sentiment: signal.sentiment, score: signal.sentimentScore)
                    }
                    
                    // Price Levels
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Price Levels")
                            .font(.headline)
                        
                        PriceLevelRow(label: "Entry Price", price: signal.entryPrice, color: .blue)
                        PriceLevelRow(label: "Target Price", price: signal.targetPrice, color: .green)
                        PriceLevelRow(label: "Stop Loss", price: signal.stopLossPrice, color: .red)
                    }
                    
                    // Risk/Reward
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Risk/Reward Analysis")
                            .font(.headline)
                        
                        let risk = abs(signal.entryPrice - signal.stopLossPrice)
                        let reward = abs(signal.targetPrice - signal.entryPrice)
                        let ratio = reward / risk
                        
                        Text("Risk/Reward Ratio: \(ratio, specifier: "%.2f"):1")
                            .font(.subheadline)
                            .foregroundColor(ratio >= 1.5 ? .green : .orange)
                    }
                    
                    // Reasoning
                    if !signal.reasoning.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Analysis")
                                .font(.headline)
                            
                            Text(signal.reasoning)
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Signal Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct PriceLevelRow: View {
    let label: String
    let price: Double
    let color: Color
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(price, format: .currency(code: "USD"))
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .padding(.vertical, 4)
    }
}
