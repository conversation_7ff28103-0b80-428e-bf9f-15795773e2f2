#!/usr/bin/env python3
"""
Debug and Fix Automated Trading System
This script will diagnose and fix why automated trading isn't working
"""

import sys
import os
import subprocess
from datetime import datetime
import json

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_signal_generation():
    """Test if signals are being generated properly"""
    print("🔍 Testing Signal Generation...")
    print("=" * 50)
    
    try:
        from signals.enhanced_signal_generator import EnhancedSignalGenerator
        from signals.signal_generator import SignalGenerator
        
        # Test basic signal generator
        print("📊 Testing Basic Signal Generator:")
        basic_generator = SignalGenerator()
        
        # Test enhanced signal generator
        print("🚀 Testing Enhanced Signal Generator:")
        enhanced_generator = EnhancedSignalGenerator()
        
        # Test signal generation for your current holdings
        test_symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL']
        
        print(f"\n🎯 Generating signals for: {', '.join(test_symbols)}")
        
        # Generate enhanced signals
        signals = enhanced_generator.generate_signals(test_symbols)
        
        if signals:
            print(f"✅ Generated {len(signals)} enhanced signals:")
            
            for signal in signals:
                print(f"  📈 {signal.symbol}:")
                print(f"     Type: {signal.signal_type}")
                print(f"     Confidence: {signal.confidence:.2%}")
                print(f"     Strength: {signal.strength}")
                print(f"     Action: {signal.action if hasattr(signal, 'action') else 'N/A'}")
                
                # Check if signal would trigger trade
                if signal.confidence > 0.75:
                    print(f"     🎯 WOULD TRIGGER TRADE (confidence > 75%)")
                elif signal.confidence > 0.65:
                    print(f"     ⚠️ BORDERLINE (confidence 65-75%)")
                else:
                    print(f"     ❌ BELOW THRESHOLD (confidence < 65%)")
                print()
        else:
            print("❌ No signals generated - this is the problem!")
            
        return len(signals) if signals else 0
        
    except Exception as e:
        print(f"❌ Error testing signal generation: {e}")
        return 0

def test_trading_engine_config():
    """Test trading engine configuration"""
    print("⚙️ Testing Trading Engine Configuration...")
    print("=" * 50)
    
    try:
        from trading.live_trading_engine import LiveTradingEngine, TradingMode
        
        # Test engine initialization
        print("🤖 Testing Live Trading Engine:")
        
        # Check if engine can be created
        engine = LiveTradingEngine()
        print("✅ Live Trading Engine created successfully")
        
        # Check trading mode
        print(f"📊 Current trading mode: {engine.trading_mode if hasattr(engine, 'trading_mode') else 'Unknown'}")
        
        # Check if engine has signal processing
        if hasattr(engine, 'process_signals'):
            print("✅ Signal processing method available")
        else:
            print("❌ Signal processing method missing")
            
        # Check if engine has order execution
        if hasattr(engine, 'execute_order'):
            print("✅ Order execution method available")
        else:
            print("❌ Order execution method missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing trading engine: {e}")
        return False

def test_paper_trading_integration():
    """Test paper trading integration"""
    print("📊 Testing Paper Trading Integration...")
    print("=" * 50)
    
    try:
        from trading.paper_trading import PaperTradingEngine
        
        # Load existing paper trading state
        paper_engine = PaperTradingEngine()
        
        # Try to load current state
        if os.path.exists('current_portfolio_state.json'):
            paper_engine.load_state('current_portfolio_state.json')
            print("✅ Loaded existing paper trading portfolio")
        else:
            print("⚠️ No existing portfolio state found")
        
        # Get portfolio summary
        portfolio = paper_engine.get_portfolio_summary()
        
        print(f"💰 Portfolio Value: ${portfolio['portfolio_value']:,.2f}")
        print(f"💵 Available Cash: ${portfolio['cash']:,.2f}")
        print(f"📈 Total Return: {portfolio['total_return']:.2%}")
        print(f"📊 Open Positions: {portfolio['open_positions']}")
        
        # Test if we can place a small test order
        print("\n🧪 Testing Order Placement:")
        
        # Don't actually place the order, just test the method
        if hasattr(paper_engine, 'place_order'):
            print("✅ place_order method available")
            
            # Test order validation
            from trading.robinhood_client import OrderSide
            
            test_result = paper_engine.place_order(
                symbol='AAPL',
                quantity=1,
                side=OrderSide.BUY,
                price=200.00,
                order_type='limit',
                dry_run=True  # Don't actually execute
            )
            
            if test_result.get('success'):
                print("✅ Order placement test successful")
            else:
                print(f"❌ Order placement test failed: {test_result.get('error')}")
        else:
            print("❌ place_order method missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing paper trading: {e}")
        return False

def check_environment_config():
    """Check environment configuration"""
    print("🔧 Checking Environment Configuration...")
    print("=" * 50)
    
    # Check .env file
    env_issues = []
    
    if os.path.exists('.env'):
        print("✅ .env file found")
        
        with open('.env', 'r') as f:
            env_content = f.read()
            
        # Check for required settings
        required_settings = [
            'TRADING_MODE',
            'PAPER_TRADING_ENABLED',
            'SIGNAL_CONFIDENCE_THRESHOLD',
            'MAX_POSITION_SIZE',
            'RISK_PER_TRADE'
        ]
        
        for setting in required_settings:
            if setting in env_content:
                print(f"✅ {setting} configured")
            else:
                print(f"❌ {setting} missing")
                env_issues.append(setting)
    else:
        print("❌ .env file not found")
        env_issues.extend(['TRADING_MODE', 'PAPER_TRADING_ENABLED'])
    
    return env_issues

def fix_configuration_issues(env_issues):
    """Fix identified configuration issues"""
    print("🔧 Fixing Configuration Issues...")
    print("=" * 50)
    
    if env_issues:
        print("📝 Adding missing environment variables:")
        
        # Create or append to .env file
        env_additions = {
            'TRADING_MODE': 'paper',
            'PAPER_TRADING_ENABLED': 'true',
            'SIGNAL_CONFIDENCE_THRESHOLD': '0.70',
            'MAX_POSITION_SIZE': '0.10',  # 10% of portfolio per position
            'RISK_PER_TRADE': '0.02',     # 2% risk per trade
            'AUTO_TRADING_ENABLED': 'true',
            'MARKET_HOURS_ONLY': 'true',
            'MIN_SIGNAL_STRENGTH': 'medium'
        }
        
        with open('.env', 'a') as f:
            f.write('\n# Automated Trading Configuration\n')
            for key, value in env_additions.items():
                if key in env_issues or key.startswith('AUTO_') or key.startswith('MIN_'):
                    f.write(f'{key}={value}\n')
                    print(f"✅ Added {key}={value}")
        
        print("✅ Configuration updated")
    else:
        print("✅ No configuration issues found")

def test_signal_to_trade_pipeline():
    """Test the complete signal-to-trade pipeline"""
    print("🔄 Testing Signal-to-Trade Pipeline...")
    print("=" * 50)
    
    try:
        from signals.enhanced_signal_generator import EnhancedSignalGenerator
        from trading.paper_trading import PaperTradingEngine
        from trading.robinhood_client import OrderSide
        
        # Generate signals
        generator = EnhancedSignalGenerator()
        signals = generator.generate_signals(['AAPL', 'TSLA', 'MSFT'])
        
        if not signals:
            print("❌ No signals generated for testing")
            return False
        
        # Test paper trading engine
        paper_engine = PaperTradingEngine()
        
        # Load existing state
        if os.path.exists('current_portfolio_state.json'):
            paper_engine.load_state('current_portfolio_state.json')
        
        print(f"📊 Testing with {len(signals)} signals:")
        
        trades_that_would_execute = 0
        
        for signal in signals:
            print(f"\n🎯 Signal: {signal.symbol}")
            print(f"   Confidence: {signal.confidence:.2%}")
            print(f"   Type: {signal.signal_type}")
            
            # Check if signal meets trading criteria
            confidence_threshold = 0.70  # 70% threshold
            
            if signal.confidence >= confidence_threshold:
                print(f"   ✅ MEETS THRESHOLD (>= {confidence_threshold:.0%})")
                
                # Determine trade action
                if 'bullish' in signal.signal_type.lower() or 'buy' in signal.signal_type.lower():
                    action = 'BUY'
                    side = OrderSide.BUY
                elif 'bearish' in signal.signal_type.lower() or 'sell' in signal.signal_type.lower():
                    action = 'SELL'
                    side = OrderSide.SELL
                else:
                    action = 'HOLD'
                    print(f"   📊 Action: HOLD (neutral signal)")
                    continue
                
                print(f"   🎯 Action: {action}")
                
                # Test order placement (dry run)
                try:
                    result = paper_engine.place_order(
                        symbol=signal.symbol,
                        quantity=10,  # Test with 10 shares
                        side=side,
                        price=200.00,  # Test price
                        order_type='market',
                        dry_run=True
                    )
                    
                    if result.get('success'):
                        print(f"   ✅ WOULD EXECUTE TRADE")
                        trades_that_would_execute += 1
                    else:
                        print(f"   ❌ Trade would fail: {result.get('error')}")
                        
                except Exception as e:
                    print(f"   ❌ Error testing trade: {e}")
            else:
                print(f"   ❌ BELOW THRESHOLD ({signal.confidence:.2%} < {confidence_threshold:.0%})")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Signals generated: {len(signals)}")
        print(f"   Trades that would execute: {trades_that_would_execute}")
        
        if trades_that_would_execute > 0:
            print(f"   ✅ Pipeline working - signals would trigger trades")
            return True
        else:
            print(f"   ⚠️ No trades would execute - check thresholds")
            return False
            
    except Exception as e:
        print(f"❌ Error testing pipeline: {e}")
        return False

def create_automated_trading_fix():
    """Create a script to fix automated trading"""
    print("🔧 Creating Automated Trading Fix...")
    print("=" * 50)
    
    fix_script = """#!/usr/bin/env python3
'''
Fixed Automated Trading Script
This integrates signal generation with paper trading execution
'''

import sys
import os
import time
import logging
from datetime import datetime, time as dt_time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from signals.enhanced_signal_generator import EnhancedSignalGenerator
from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automated_trading_fixed.log'),
        logging.StreamHandler()
    ]
)

class FixedAutomatedTrader:
    def __init__(self):
        self.signal_generator = EnhancedSignalGenerator()
        self.paper_engine = PaperTradingEngine()
        self.confidence_threshold = 0.70
        self.max_position_size = 0.10  # 10% of portfolio
        self.symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL', 'AMZN', 'META']
        
        # Load existing portfolio state
        if os.path.exists('current_portfolio_state.json'):
            self.paper_engine.load_state('current_portfolio_state.json')
            logging.info("Loaded existing portfolio state")
    
    def is_market_hours(self):
        '''Check if market is open'''
        now = datetime.now()
        current_time = now.time()
        market_open = dt_time(9, 30)
        market_close = dt_time(16, 0)
        is_weekday = now.weekday() < 5
        
        return market_open <= current_time <= market_close and is_weekday
    
    def process_signals(self):
        '''Generate and process signals for trading'''
        try:
            # Generate signals
            signals = self.signal_generator.generate_signals(self.symbols)
            
            if not signals:
                logging.info("No signals generated")
                return 0
            
            logging.info(f"Generated {len(signals)} signals")
            trades_executed = 0
            
            for signal in signals:
                if signal.confidence >= self.confidence_threshold:
                    if self.execute_signal(signal):
                        trades_executed += 1
            
            return trades_executed
            
        except Exception as e:
            logging.error(f"Error processing signals: {e}")
            return 0
    
    def execute_signal(self, signal):
        '''Execute a trading signal'''
        try:
            # Determine action
            if 'bullish' in signal.signal_type.lower():
                side = OrderSide.BUY
                action = 'BUY'
            elif 'bearish' in signal.signal_type.lower():
                side = OrderSide.SELL
                action = 'SELL'
            else:
                logging.info(f"Neutral signal for {signal.symbol}, no action")
                return False
            
            # Calculate position size
            portfolio = self.paper_engine.get_portfolio_summary()
            max_value = portfolio['portfolio_value'] * self.max_position_size
            
            # Estimate price (in real implementation, get current price)
            estimated_price = 200.00  # Placeholder
            quantity = int(max_value / estimated_price)
            
            if quantity < 1:
                logging.info(f"Position size too small for {signal.symbol}")
                return False
            
            # Execute trade
            result = self.paper_engine.place_order(
                symbol=signal.symbol,
                quantity=quantity,
                side=side,
                price=estimated_price,
                order_type='market'
            )
            
            if result.get('success'):
                logging.info(f"✅ Executed {action} {quantity} {signal.symbol} @ ${estimated_price}")
                
                # Save state
                self.paper_engine.save_state('current_portfolio_state.json')
                return True
            else:
                logging.error(f"❌ Failed to execute {action} {signal.symbol}: {result.get('error')}")
                return False
                
        except Exception as e:
            logging.error(f"Error executing signal for {signal.symbol}: {e}")
            return False
    
    def run(self):
        '''Main trading loop'''
        logging.info("🚀 Starting Fixed Automated Trading System")
        
        while True:
            try:
                if self.is_market_hours():
                    logging.info("📊 Market is open, processing signals...")
                    trades = self.process_signals()
                    
                    if trades > 0:
                        logging.info(f"✅ Executed {trades} trades")
                    else:
                        logging.info("📊 No trades executed this cycle")
                else:
                    logging.info("🕐 Market closed, waiting...")
                
                # Wait 5 minutes before next cycle
                time.sleep(300)
                
            except KeyboardInterrupt:
                logging.info("🛑 Stopping automated trading")
                break
            except Exception as e:
                logging.error(f"Error in main loop: {e}")
                time.sleep(60)  # Wait 1 minute on error

if __name__ == "__main__":
    trader = FixedAutomatedTrader()
    trader.run()
"""
    
    with open('run_fixed_automated_trading.py', 'w') as f:
        f.write(fix_script)
    
    # Make executable
    os.chmod('run_fixed_automated_trading.py', 0o755)
    
    print("✅ Created run_fixed_automated_trading.py")
    print("   This script integrates signal generation with trade execution")

def main():
    """Main debugging and fixing function"""
    print("🔍 AWOT Automated Trading Debug & Fix")
    print("=" * 60)
    print(f"Analysis time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Test signal generation
    signal_count = test_signal_generation()
    
    # Step 2: Test trading engine
    engine_working = test_trading_engine_config()
    
    # Step 3: Test paper trading integration
    paper_working = test_paper_trading_integration()
    
    # Step 4: Check environment configuration
    env_issues = check_environment_config()
    
    # Step 5: Fix configuration issues
    fix_configuration_issues(env_issues)
    
    # Step 6: Test complete pipeline
    pipeline_working = test_signal_to_trade_pipeline()
    
    # Step 7: Create fixed automated trading script
    create_automated_trading_fix()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS SUMMARY:")
    
    if signal_count > 0:
        print(f"✅ Signal Generation: Working ({signal_count} signals)")
    else:
        print("❌ Signal Generation: Not working")
    
    if engine_working:
        print("✅ Trading Engine: Available")
    else:
        print("❌ Trading Engine: Issues found")
    
    if paper_working:
        print("✅ Paper Trading: Working")
    else:
        print("❌ Paper Trading: Issues found")
    
    if not env_issues:
        print("✅ Configuration: Fixed")
    else:
        print(f"⚠️ Configuration: {len(env_issues)} issues addressed")
    
    if pipeline_working:
        print("✅ Signal-to-Trade Pipeline: Working")
    else:
        print("❌ Signal-to-Trade Pipeline: Needs attention")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Run the fixed automated trading script:")
    print("   python run_fixed_automated_trading.py")
    print("2. Monitor logs/automated_trading_fixed.log")
    print("3. Check dashboard for new trades")
    print("4. Test during market hours for live execution")

if __name__ == "__main__":
    main()
