# AWOT Trading Platform Configuration Template
# Copy this file to .env and fill in your actual credentials

# =============================================================================
# ROBINHOOD API CREDENTIALS
# =============================================================================
# Your Robinhood login credentials
ROBINHOOD_USERNAME=your_username_here
ROBINHOOD_PASSWORD=your_password_here

# Two-Factor Authentication (2FA) Secret
# Get this from your authenticator app settings
ROBINHOOD_TOTP_SECRET=your_totp_secret_here

# Trading Mode (set to 'false' for live trading, 'true' for paper trading)
PAPER_TRADING=true

# =============================================================================
# EMAIL NOTIFICATIONS
# =============================================================================
# SMTP Email Configuration
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>

# =============================================================================
# TELEGRAM NOTIFICATIONS
# =============================================================================
# Telegram Bot Configuration
# Create a bot via @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# =============================================================================
# SMS NOTIFICATIONS (TWILIO)
# =============================================================================
# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********
SMS_TO_NUMBER=+**********

# =============================================================================
# ALPHA VANTAGE API (FOR MARKET DATA)
# =============================================================================
# Get free API key from https://www.alphavantage.co/support/#api-key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
# Initial capital for paper trading
INITIAL_CAPITAL=50000

# Risk management settings
RISK_PER_TRADE=0.10
MAX_POSITIONS=3
MAX_DAILY_TRADES=10

# Technical indicator settings
RSI_OVERSOLD=30
RSI_OVERBOUGHT=70
BREAKOUT_VOLUME_THRESHOLD=2.0

# Profit targets and stop losses
PROFIT_TARGET=0.20
STOP_LOSS=0.10

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================
# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Enable/disable specific features
ENABLE_NOTIFICATIONS=true
ENABLE_RISK_MANAGEMENT=true
ENABLE_BACKTESTING=true

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env in the project root
# 2. Fill in your actual credentials and API keys
# 3. Set PAPER_TRADING=true for safe testing
# 4. Test notifications and API connections
# 5. Start with small position sizes
# 6. Monitor performance carefully
#
# SECURITY NOTES:
# - Never commit .env file to version control
# - Use app passwords for email (not your main password)
# - Enable 2FA on all accounts
# - Regularly rotate API keys and passwords
# - Monitor account activity for unauthorized access
#
# ROBINHOOD SETUP:
# 1. Enable 2FA in your Robinhood account
# 2. Get TOTP secret from authenticator app
# 3. Test login with credentials first
# 4. Start with paper trading mode
#
# TELEGRAM SETUP:
# 1. Message @BotFather on Telegram
# 2. Create new bot with /newbot command
# 3. Get bot token from BotFather
# 4. Start conversation with your bot
# 5. Get chat ID from bot messages
#
# EMAIL SETUP:
# 1. Enable 2FA on your email account
# 2. Generate app-specific password
# 3. Use app password, not main password
# 4. Test SMTP connection first
