#!/usr/bin/env python3
'''
Debug Signal Generation - Find Why No Real Signals Are Created
This comprehensively debugs both basic and enhanced signal generators
'''

import sys
import os
import logging
from datetime import datetime
import pandas as pd

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Set environment variable to force signal generation
os.environ['FORCE_SIGNAL_GENERATION'] = 'true'

from signals.signal_generator import SignalGenerator
from signals.enhanced_signal_generator import EnhancedSignalGenerator
from data.market_data import MarketDataProvider

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/signal_generation_debug.log'),
        logging.StreamHandler()
    ]
)

class SignalGenerationDebugger:
    """Comprehensive signal generation debugger"""
    
    def __init__(self):
        self.market_data_provider = MarketDataProvider()
        self.basic_generator = SignalGenerator()
        self.enhanced_generator = EnhancedSignalGenerator()
        self.test_symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL']
    
    def debug_market_data(self, symbol):
        """Debug market data availability and quality"""
        logging.info(f"\n🔍 DEBUGGING MARKET DATA FOR {symbol}")
        logging.info("=" * 50)
        
        try:
            # Test basic stock data
            basic_data = self.market_data_provider.get_stock_data(symbol)
            logging.info(f"✅ Basic stock data: {basic_data.shape if not basic_data.empty else 'EMPTY'}")
            
            if not basic_data.empty:
                logging.info(f"   Date range: {basic_data.index[0]} to {basic_data.index[-1]}")
                logging.info(f"   Columns: {list(basic_data.columns)}")
                logging.info(f"   Last close: ${basic_data['Close'].iloc[-1]:.2f}")
                logging.info(f"   Volume: {basic_data['Volume'].iloc[-1]:,}")
            
            # Test comprehensive data
            comprehensive_data = self.market_data_provider.get_comprehensive_market_data(symbol)
            logging.info(f"✅ Comprehensive data keys: {list(comprehensive_data.keys()) if comprehensive_data else 'NONE'}")
            
            if comprehensive_data and 'historical_data' in comprehensive_data:
                hist_data = comprehensive_data['historical_data']
                logging.info(f"   Historical data length: {len(hist_data)}")
                
                if len(hist_data) > 0:
                    logging.info(f"   Sample data point: {hist_data[0]}")
                
                # Check data quality
                data_quality = comprehensive_data.get('data_quality', {})
                logging.info(f"   Data quality score: {data_quality.get('quality_score', 'N/A')}")
                logging.info(f"   Data issues: {data_quality.get('issues', [])}")
            
            # Test real-time data
            real_time_data = self.market_data_provider.get_real_time_data(symbol)
            logging.info(f"✅ Real-time data: {bool(real_time_data)}")
            
            if real_time_data:
                logging.info(f"   Current price: ${real_time_data.get('current_price', 'N/A')}")
                logging.info(f"   Volume: {real_time_data.get('volume', 'N/A'):,}")
                logging.info(f"   Beta: {real_time_data.get('beta', 'N/A')}")
            
            return basic_data, comprehensive_data, real_time_data
            
        except Exception as e:
            logging.error(f"❌ Error getting market data for {symbol}: {e}")
            return None, None, None
    
    def debug_technical_indicators(self, symbol, data):
        """Debug technical indicator calculations"""
        logging.info(f"\n🔍 DEBUGGING TECHNICAL INDICATORS FOR {symbol}")
        logging.info("=" * 50)
        
        try:
            if data is None or data.empty:
                logging.error("❌ No data available for technical indicators")
                return None
            
            # Test technical indicators
            indicators = self.basic_generator.technical_indicators.calculate_all_indicators(data)
            logging.info(f"✅ Technical indicators shape: {indicators.shape if not indicators.empty else 'EMPTY'}")
            
            if not indicators.empty:
                logging.info(f"   Columns: {list(indicators.columns)}")
                
                # Check key indicators
                latest = indicators.iloc[-1]
                logging.info(f"   RSI: {latest.get('RSI', 'N/A')}")
                logging.info(f"   MACD: {latest.get('MACD', 'N/A')}")
                logging.info(f"   BB_upper: {latest.get('BB_upper', 'N/A')}")
                logging.info(f"   SMA_20: {latest.get('SMA_20', 'N/A')}")
                logging.info(f"   Volume_SMA: {latest.get('Volume_SMA', 'N/A')}")
            
            return indicators
            
        except Exception as e:
            logging.error(f"❌ Error calculating technical indicators for {symbol}: {e}")
            return None
    
    def debug_basic_signal_generation(self, symbol):
        """Debug basic signal generation step by step"""
        logging.info(f"\n🔍 DEBUGGING BASIC SIGNAL GENERATION FOR {symbol}")
        logging.info("=" * 50)
        
        try:
            # Get market data
            basic_data, comprehensive_data, real_time_data = self.debug_market_data(symbol)
            
            if not comprehensive_data or not comprehensive_data.get('historical_data'):
                logging.error("❌ Insufficient data for basic signal generation")
                return []
            
            # Convert historical data to DataFrame
            historical_df = pd.DataFrame(comprehensive_data['historical_data'])
            logging.info(f"✅ Historical DataFrame: {historical_df.shape}")
            
            # Debug technical indicators
            indicators = self.debug_technical_indicators(symbol, historical_df)
            
            if indicators is None or indicators.empty:
                logging.error("❌ No technical indicators available")
                return []
            
            # Test signal generation methods
            logging.info(f"\n🎯 Testing signal generation methods...")
            
            # Test momentum signals
            try:
                momentum_signals = self.basic_generator._generate_momentum_signals(symbol, indicators, comprehensive_data)
                logging.info(f"✅ Momentum signals: {len(momentum_signals)}")
                for signal in momentum_signals:
                    logging.info(f"   {signal.symbol}: {signal.signal_type} - {signal.confidence:.2%}")
            except Exception as e:
                logging.error(f"❌ Error generating momentum signals: {e}")
                momentum_signals = []
            
            # Test breakout signals
            try:
                breakout_signals = self.basic_generator._generate_breakout_signals(symbol, indicators, comprehensive_data)
                logging.info(f"✅ Breakout signals: {len(breakout_signals)}")
                for signal in breakout_signals:
                    logging.info(f"   {signal.symbol}: {signal.signal_type} - {signal.confidence:.2%}")
            except Exception as e:
                logging.error(f"❌ Error generating breakout signals: {e}")
                breakout_signals = []
            
            # Test reversal signals
            try:
                reversal_signals = self.basic_generator._generate_reversal_signals(symbol, indicators, comprehensive_data)
                logging.info(f"✅ Reversal signals: {len(reversal_signals)}")
                for signal in reversal_signals:
                    logging.info(f"   {signal.symbol}: {signal.signal_type} - {signal.confidence:.2%}")
            except Exception as e:
                logging.error(f"❌ Error generating reversal signals: {e}")
                reversal_signals = []
            
            all_signals = momentum_signals + breakout_signals + reversal_signals
            logging.info(f"\n📊 Total basic signals generated: {len(all_signals)}")
            
            return all_signals
            
        except Exception as e:
            logging.error(f"❌ Error in basic signal generation for {symbol}: {e}")
            return []
    
    def debug_enhanced_signal_generation(self, symbol):
        """Debug enhanced signal generation step by step"""
        logging.info(f"\n🔍 DEBUGGING ENHANCED SIGNAL GENERATION FOR {symbol}")
        logging.info("=" * 50)
        
        try:
            # Test enhanced signal generation
            signals = self.enhanced_generator.generate_signals(symbol)
            logging.info(f"✅ Enhanced signals generated: {len(signals)}")
            
            for signal in signals:
                logging.info(f"   {signal.symbol}: {signal.signal_type} - {signal.confidence:.2%}")
            
            return signals
            
        except Exception as e:
            logging.error(f"❌ Error in enhanced signal generation for {symbol}: {e}")
            return []
    
    def run_comprehensive_debug(self):
        """Run comprehensive signal generation debug"""
        logging.info("🔍 COMPREHENSIVE SIGNAL GENERATION DEBUG")
        logging.info("=" * 60)
        
        results = {
            'basic_signals': {},
            'enhanced_signals': {},
            'market_data_issues': [],
            'technical_indicator_issues': []
        }
        
        for symbol in self.test_symbols:
            logging.info(f"\n{'='*60}")
            logging.info(f"DEBUGGING {symbol}")
            logging.info(f"{'='*60}")
            
            # Debug basic signal generation
            basic_signals = self.debug_basic_signal_generation(symbol)
            results['basic_signals'][symbol] = basic_signals
            
            # Debug enhanced signal generation
            enhanced_signals = self.debug_enhanced_signal_generation(symbol)
            results['enhanced_signals'][symbol] = enhanced_signals
        
        # Summary
        logging.info(f"\n{'='*60}")
        logging.info("SUMMARY")
        logging.info(f"{'='*60}")
        
        total_basic = sum(len(signals) for signals in results['basic_signals'].values())
        total_enhanced = sum(len(signals) for signals in results['enhanced_signals'].values())
        
        logging.info(f"📊 Total basic signals: {total_basic}")
        logging.info(f"📊 Total enhanced signals: {total_enhanced}")
        
        if total_basic == 0 and total_enhanced == 0:
            logging.error("❌ NO SIGNALS GENERATED AT ALL")
            logging.info("\n🔍 POSSIBLE REASONS:")
            logging.info("1. Market data quality issues")
            logging.info("2. Technical indicators not meeting thresholds")
            logging.info("3. Signal confidence thresholds too high")
            logging.info("4. Market conditions not suitable for signals")
            logging.info("5. Time-based filtering (market hours, optimal times)")
        else:
            logging.info("✅ Some signals were generated")
        
        return results

def main():
    """Main debug function"""
    print("🔍 SIGNAL GENERATION COMPREHENSIVE DEBUG")
    print("=" * 60)
    print("This will debug why real signal generators aren't producing signals")
    print()
    
    debugger = SignalGenerationDebugger()
    results = debugger.run_comprehensive_debug()
    
    print("\n" + "=" * 60)
    print("🎯 DEBUG RESULTS:")
    
    total_basic = sum(len(signals) for signals in results['basic_signals'].values())
    total_enhanced = sum(len(signals) for signals in results['enhanced_signals'].values())
    
    if total_basic > 0 or total_enhanced > 0:
        print(f"✅ SUCCESS: Found signals!")
        print(f"   Basic signals: {total_basic}")
        print(f"   Enhanced signals: {total_enhanced}")
        print()
        print("🎯 NEXT STEPS:")
        print("1. Check why these signals aren't reaching the trading pipeline")
        print("2. Verify confidence thresholds in automated trading")
        print("3. Test with lower confidence thresholds")
    else:
        print("❌ NO SIGNALS FOUND")
        print()
        print("🔍 CHECK:")
        print("1. logs/signal_generation_debug.log for detailed analysis")
        print("2. Market data quality and availability")
        print("3. Technical indicator calculations")
        print("4. Signal generation thresholds and filters")
    
    print("\n💡 DETAILED LOGS:")
    print("Check logs/signal_generation_debug.log for complete analysis")

if __name__ == "__main__":
    main()
