#!/usr/bin/env python3
"""
AWOT Dashboard Launcher
Launches the Streamlit dashboard for the AWOT trading platform
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'streamlit',
        'plotly',
        'pandas',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print("🚀 AWOT Trading Dashboard Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("dashboard/main_dashboard.py"):
        print("❌ Dashboard files not found!")
        print("Please run this script from the AWOT root directory.")
        return 1
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        return 1
    
    print("✅ All dependencies found!")
    
    # Launch Streamlit
    print("\n🌐 Launching AWOT Trading Dashboard...")
    print("📍 Dashboard will open in your default browser")
    print("🔗 URL: http://localhost:8502")
    print("\n⚠️  Press Ctrl+C to stop the dashboard")
    print("=" * 50)
    
    try:
        # Launch streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            "dashboard/main_dashboard.py",
            "--server.port=8502",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ]
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n\n🛑 Dashboard stopped by user")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Error launching dashboard: {e}")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
