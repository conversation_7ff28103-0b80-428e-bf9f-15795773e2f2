#!/usr/bin/env python3
"""Debug the paper trading engine calculation bug"""

import sys
import os
sys.path.append('src')

from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide

def debug_calculation():
    engine = PaperTradingEngine()
    
    # Test the calculation step by step
    price = 213.25
    quantity = 5
    side = OrderSide.BUY
    
    print(f'Input: {quantity} shares @ ${price}')
    print(f'Expected cost: ${quantity * price}')
    
    # Test execution price calculation
    execution_price = engine._calculate_execution_price(price, side, quantity)
    print(f'Execution price: ${execution_price}')
    
    order_value = execution_price * quantity
    print(f'Order value: ${order_value}')
    
    total_cost = order_value + engine.commission_per_trade
    print(f'Total cost: ${total_cost}')
    
    # Check slippage factors
    print(f'Slippage factor: {engine.slippage_factor}')
    print(f'Market impact factor: {engine.market_impact_factor}')
    
    # Manual calculation
    slippage = price * engine.slippage_factor
    market_impact = price * engine.market_impact_factor * min(quantity / 100, 1.0)
    manual_execution_price = price + slippage + market_impact
    
    print(f'Manual slippage: ${slippage}')
    print(f'Manual market impact: ${market_impact}')
    print(f'Manual execution price: ${manual_execution_price}')
    
    # Test with order_type parameter
    print('\nTesting actual order placement:')
    result = engine.place_order(
        symbol='TEST',
        quantity=quantity,
        side=side,
        price=price,
        order_type='stock'
    )
    
    print(f'Result: {result}')

if __name__ == "__main__":
    debug_calculation()
