#!/usr/bin/env python3
"""
Simple HTTP server to serve AWOT Trading PWA
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from urllib.parse import urlparse

class PWAHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler for PWA with proper MIME types"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        # Add PWA-specific headers
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        # CORS headers for API access
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        
        super().end_headers()
    
    def guess_type(self, path):
        """Override to set correct MIME types"""
        mimetype, encoding = super().guess_type(path)
        
        # Set correct MIME types for PWA files
        if path.endswith('.webmanifest') or path.endswith('manifest.json'):
            return 'application/manifest+json', encoding
        elif path.endswith('.js'):
            return 'application/javascript', encoding
        elif path.endswith('.css'):
            return 'text/css', encoding
        elif path.endswith('.png'):
            return 'image/png', encoding
        elif path.endswith('.svg'):
            return 'image/svg+xml', encoding
        
        return mimetype, encoding
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        
        # Serve index.html for root path
        if parsed_path.path == '/':
            self.path = '/index.html'
        
        # Handle service worker with correct headers
        if parsed_path.path == '/sw.js':
            self.send_response(200)
            self.send_header('Content-Type', 'application/javascript')
            self.send_header('Service-Worker-Allowed', '/')
            self.end_headers()
            
            try:
                with open('sw.js', 'rb') as f:
                    self.wfile.write(f.read())
            except FileNotFoundError:
                self.send_error(404, "Service Worker not found")
            return
        
        # Handle manifest.json
        if parsed_path.path == '/manifest.json':
            self.send_response(200)
            self.send_header('Content-Type', 'application/manifest+json')
            self.end_headers()
            
            try:
                with open('manifest.json', 'rb') as f:
                    self.wfile.write(f.read())
            except FileNotFoundError:
                self.send_error(404, "Manifest not found")
            return
        
        # Default handling
        super().do_GET()
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """Custom log format"""
        print(f"📱 PWA: {format % args}")

def check_files():
    """Check if required PWA files exist"""
    required_files = ['index.html', 'app.js', 'sw.js', 'manifest.json']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required PWA files found")
    return True

def create_simple_icons():
    """Create simple placeholder icons if they don't exist"""
    icons_dir = 'icons'
    os.makedirs(icons_dir, exist_ok=True)
    
    # Create simple SVG icon if PIL is not available
    icon_sizes = [72, 96, 128, 144, 152, 192, 384, 512]
    
    for size in icon_sizes:
        icon_path = f"{icons_dir}/icon-{size}.png"
        if not os.path.exists(icon_path):
            # Create a simple colored square as placeholder
            try:
                from PIL import Image, ImageDraw
                
                img = Image.new('RGB', (size, size), '#007AFF')
                draw = ImageDraw.Draw(img)
                
                # Draw simple chart symbol
                draw.rectangle([size//4, size//2, size//2, size*3//4], fill='white')
                draw.rectangle([size//2, size//3, size*3//4, size*3//4], fill='white')
                
                img.save(icon_path)
                print(f"✅ Created placeholder icon: {icon_path}")
                
            except ImportError:
                # Create SVG fallback
                svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{size}" height="{size}" viewBox="0 0 {size} {size}" xmlns="http://www.w3.org/2000/svg">
    <rect width="{size}" height="{size}" fill="#007AFF"/>
    <rect x="{size//4}" y="{size//2}" width="{size//4}" height="{size//4}" fill="white"/>
    <rect x="{size//2}" y="{size//3}" width="{size//4}" height="{size*5//12}" fill="white"/>
    <text x="{size//2}" y="{size*7//8}" text-anchor="middle" fill="white" font-family="Arial" font-size="{size//8}">AWOT</text>
</svg>'''
                
                svg_path = f"{icons_dir}/icon-{size}.svg"
                with open(svg_path, 'w') as f:
                    f.write(svg_content)
                print(f"✅ Created SVG icon: {svg_path}")

def main():
    """Start the PWA server"""
    
    print("🌐 AWOT Trading PWA Server")
    print("=" * 50)
    
    # Change to PWA directory
    pwa_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(pwa_dir)
    
    # Check required files
    if not check_files():
        print("\n❌ Cannot start server - missing required files")
        return
    
    # Create icons if they don't exist
    create_simple_icons()
    
    # Server configuration
    PORT = 3000
    HOST = '0.0.0.0'
    
    try:
        # Create server
        with socketserver.TCPServer((HOST, PORT), PWAHTTPRequestHandler) as httpd:
            print(f"\n🚀 PWA Server starting...")
            print(f"📱 Local URL: http://localhost:{PORT}")
            print(f"🌐 Network URL: http://YOUR_IP:{PORT}")
            print(f"📂 Serving from: {pwa_dir}")
            print("\n🎯 Instructions:")
            print("1. Open the URL in your browser")
            print("2. On mobile: Add to Home Screen")
            print("3. On desktop: Install when prompted")
            print("\n📱 Mobile Installation:")
            print("• iPhone: Safari → Share → Add to Home Screen")
            print("• Android: Chrome → Menu → Add to Home Screen")
            print("\n⚠️  Make sure your AWOT API is running on port 8080")
            print("🔄 Press Ctrl+C to stop the server")
            print("\n" + "=" * 50)
            
            # Try to open browser automatically
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 Opened browser automatically")
            except:
                print("💡 Please open http://localhost:3000 in your browser")
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"\n❌ Port {PORT} is already in use")
            print("💡 Try a different port or stop the other service")
        else:
            print(f"\n❌ Server error: {e}")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
