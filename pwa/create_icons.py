#!/usr/bin/env python3
"""
Create simple app icons for AWOT Trading PWA
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    """Create a simple trading app icon"""
    
    # Create image with blue background
    img = Image.new('RGB', (size, size), '#007AFF')
    draw = ImageDraw.Draw(img)
    
    # Calculate sizes based on icon size
    font_size = max(size // 4, 12)
    
    try:
        # Try to use a system font
        font = ImageFont.truetype('/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', font_size)
    except:
        try:
            font = ImageFont.truetype('/System/Library/Fonts/Arial.ttf', font_size)
        except:
            # Fallback to default font
            font = ImageFont.load_default()
    
    # Draw chart-like elements
    chart_color = '#FFFFFF'
    
    # Draw simple chart bars
    bar_width = size // 8
    bar_spacing = size // 12
    start_x = size // 4
    
    for i in range(3):
        bar_height = (size // 3) + (i * size // 8)
        x = start_x + i * (bar_width + bar_spacing)
        y = size - size // 6 - bar_height
        
        draw.rectangle([x, y, x + bar_width, size - size // 6], fill=chart_color)
    
    # Draw trend line
    points = [
        (size // 6, size * 2 // 3),
        (size // 3, size // 2),
        (size * 2 // 3, size // 3),
        (size * 5 // 6, size // 4)
    ]
    
    for i in range(len(points) - 1):
        draw.line([points[i], points[i + 1]], fill=chart_color, width=max(2, size // 64))
    
    # Add small circles at data points
    for point in points:
        radius = max(2, size // 32)
        draw.ellipse([point[0] - radius, point[1] - radius, 
                     point[0] + radius, point[1] + radius], fill=chart_color)
    
    # Add text if icon is large enough
    if size >= 128:
        text = "AWOT"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        text_x = (size - text_width) // 2
        text_y = size - text_height - size // 12
        
        draw.text((text_x, text_y), text, fill=chart_color, font=font)
    
    # Save the icon
    img.save(filename, 'PNG')
    print(f"✅ Created icon: {filename} ({size}x{size})")

def main():
    """Create all required icon sizes"""
    
    print("🎨 Creating AWOT Trading PWA Icons")
    print("=" * 50)
    
    # Create icons directory
    icons_dir = 'icons'
    os.makedirs(icons_dir, exist_ok=True)
    
    # Icon sizes for PWA
    icon_sizes = [72, 96, 128, 144, 152, 192, 384, 512]
    
    for size in icon_sizes:
        filename = f"{icons_dir}/icon-{size}.png"
        create_icon(size, filename)
    
    print("\n✅ All icons created successfully!")
    print("\n📱 PWA Icons Ready:")
    for size in icon_sizes:
        print(f"   📄 icon-{size}.png")
    
    print("\n🎯 Next Steps:")
    print("1. Start your AWOT API server")
    print("2. Serve the PWA files")
    print("3. Open in browser and install")

if __name__ == "__main__":
    try:
        main()
    except ImportError:
        print("❌ PIL (Pillow) not installed")
        print("📦 Install with: pip install Pillow")
        print("🔄 Or create icons manually")
    except Exception as e:
        print(f"❌ Error creating icons: {e}")
        print("🔄 You can create icons manually or use online generators")
