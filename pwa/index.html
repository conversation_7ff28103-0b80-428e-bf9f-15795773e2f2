<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>AWOT Trading</title>
    
    <!-- PWA Configuration -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#007AFF">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="AWOT Trading">
    
    <!-- Icons -->
    <link rel="apple-touch-icon" href="icons/icon-192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="icons/icon-192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="icons/icon-512.png">
    
    <!-- Preload critical resources -->
    <link rel="preconnect" href="http://localhost:8080">
    
    <style>
        /* CSS Variables for theming */
        :root {
            --primary-color: #007AFF;
            --success-color: #34C759;
            --danger-color: #FF3B30;
            --warning-color: #FF9500;
            --secondary-color: #8E8E93;
            --background-color: #F2F2F7;
            --card-background: #FFFFFF;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --border-color: #C6C6C8;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background-color: #000000;
                --card-background: #1C1C1E;
                --text-primary: #FFFFFF;
                --text-secondary: #8E8E93;
                --border-color: #38383A;
            }
        }
        
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* Header */
        .header {
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: env(safe-area-inset-top) 0 0 0;
        }
        
        .header-content {
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }
        
        .status-dot.disconnected {
            background: var(--danger-color);
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* Main container */
        .container {
            max-width: 500px;
            margin: 0 auto;
            padding: 0 16px 100px;
            min-height: 100vh;
        }
        
        /* Cards */
        .card {
            background: var(--card-background);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }
        
        /* Portfolio section */
        .portfolio-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 12px;
        }
        
        .pnl-container {
            text-align: center;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .pnl-positive { color: var(--success-color); }
        .pnl-negative { color: var(--danger-color); }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .metric {
            text-align: center;
            padding: 16px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 12px;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        /* Signals */
        .signal-card {
            background: var(--card-background);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid var(--primary-color);
            box-shadow: var(--shadow);
        }
        
        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .signal-symbol {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .signal-type {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            background: var(--primary-color);
            color: white;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .signal-confidence {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--success-color);
        }
        
        .signal-prices {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-top: 12px;
        }
        
        .price-item {
            text-align: center;
            padding: 8px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 8px;
        }
        
        .price-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .price-value {
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        /* Sentiment indicator */
        .sentiment-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            background: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }
        
        .sentiment-negative {
            background: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }
        
        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-danger {
            background: var(--danger-color);
            color: white;
        }
        
        .btn:active {
            transform: scale(0.95);
        }
        
        /* Emergency button */
        .emergency-button {
            background: linear-gradient(135deg, var(--danger-color), #FF6B6B);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 20px;
            font-size: 1.25rem;
            font-weight: 700;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(255, 59, 48, 0.3);
            transition: transform 0.2s;
        }
        
        .emergency-button:active {
            transform: scale(0.95);
        }
        
        /* Tab bar */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            display: flex;
            padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
            border-top: 1px solid var(--border-color);
            z-index: 100;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.75rem;
            transition: color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .tab-item.active {
            color: var(--primary-color);
        }
        
        .tab-icon {
            font-size: 1.5rem;
        }
        
        /* Loading states */
        .loading {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }
        
        .spinner {
            width: 32px;
            height: 32px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Hidden utility */
        .hidden {
            display: none !important;
        }

        /* Filter buttons */
        .filter-btn, .period-btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: var(--card-background);
            color: var(--text-primary);
            border-radius: 20px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn.active, .period-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .filter-btn:hover, .period-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Notification settings */
        .notification-setting {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .notification-setting:last-child {
            border-bottom: none;
        }

        .notification-setting label {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            width: 100%;
        }

        .notification-setting input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
        }

        /* News item */
        .news-item {
            padding: 12px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.05);
            margin-bottom: 8px;
        }

        .news-headline {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 0.875rem;
        }

        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .news-sentiment {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .news-sentiment.positive {
            background: rgba(52, 199, 89, 0.2);
            color: var(--success-color);
        }

        .news-sentiment.negative {
            background: rgba(255, 59, 48, 0.2);
            color: var(--danger-color);
        }

        .news-sentiment.neutral {
            background: rgba(142, 142, 147, 0.2);
            color: var(--text-secondary);
        }

        /* Performance chart placeholder */
        .chart-placeholder {
            height: 200px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            position: relative;
            overflow: hidden;
        }

        .chart-placeholder::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            background: linear-gradient(45deg, transparent 40%, var(--primary-color) 40%, var(--primary-color) 60%, transparent 60%);
            opacity: 0.1;
        }

        /* Algorithm status indicator */
        .algo-status {
            margin-top: 16px;
            padding: 12px;
            background: rgba(52, 199, 89, 0.1);
            border-radius: 8px;
        }

        .algo-status-title {
            font-size: 0.875rem;
            color: var(--success-color);
            font-weight: 600;
        }

        .algo-status-subtitle {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        /* Responsive adjustments */
        @media (max-width: 480px) {
            .container {
                padding: 0 12px 100px;
            }
            
            .card {
                padding: 16px;
                margin-bottom: 12px;
            }
            
            .portfolio-value {
                font-size: 2rem;
            }
            
            .metrics-grid {
                gap: 12px;
            }
        }
        
        /* iOS specific adjustments */
        @supports (-webkit-touch-callout: none) {
            .header {
                padding-top: env(safe-area-inset-top);
            }
            
            .tab-bar {
                padding-bottom: calc(8px + env(safe-area-inset-bottom));
            }
        }
        
        /* Install prompt */
        .install-prompt {
            background: var(--primary-color);
            color: white;
            padding: 12px 16px;
            text-align: center;
            font-size: 0.875rem;
            position: relative;
        }
        
        .install-prompt button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            margin-left: 12px;
            font-size: 0.875rem;
        }
        
        .close-install {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: white;
            font-size: 1.25rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Install prompt -->
    <div id="installPrompt" class="install-prompt hidden">
        📱 Install AWOT Trading for the best experience
        <button onclick="installApp()">Install</button>
        <button class="close-install" onclick="hideInstallPrompt()">×</button>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <h1>📈 AWOT Trading</h1>
            <div class="connection-status">
                <div id="statusDot" class="status-dot"></div>
                <span id="connectionStatus">Connecting...</span>
            </div>
        </div>
    </div>

    <!-- Main container -->
    <div class="container">
        <!-- Portfolio Tab -->
        <div id="portfolioTab" class="tab-content">
            <!-- Portfolio Summary -->
            <div class="card">
                <div class="portfolio-value" id="portfolioValue">$50,000.00</div>
                <div class="pnl-container">
                    <div id="dailyPnL" class="pnl-positive">+$0.00 (0.0%)</div>
                </div>

                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="openPositions">0</div>
                        <div class="metric-label">Open Positions</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="todayTrades">0</div>
                        <div class="metric-label">Today's Trades</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="winRate">--</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="availableCash">$50,000</div>
                        <div class="metric-label">Available Cash</div>
                    </div>
                </div>
            </div>

            <!-- Performance Chart -->
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">📈 Performance Chart</h3>
                <div id="performanceChart" style="height: 200px; background: rgba(0, 122, 255, 0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--text-secondary);">
                    Performance chart will load here
                </div>
            </div>

            <!-- Algorithm Performance -->
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">🤖 Algorithm Performance</h3>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="algoWinRate">--</div>
                        <div class="metric-label">Algorithm Win Rate</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="avgReturn">--</div>
                        <div class="metric-label">Avg Return</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="sharpeRatio">--</div>
                        <div class="metric-label">Sharpe Ratio</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="maxDrawdown">--</div>
                        <div class="metric-label">Max Drawdown</div>
                    </div>
                </div>
                <div style="margin-top: 16px; padding: 12px; background: rgba(52, 199, 89, 0.1); border-radius: 8px;">
                    <div style="font-size: 0.875rem; color: var(--success-color); font-weight: 600;">
                        📊 Enhanced Algorithm Active
                    </div>
                    <div style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 4px;">
                        Multi-timeframe + News Sentiment + Risk Management
                    </div>
                </div>
            </div>

            <!-- News & Sentiment -->
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">📰 Market Sentiment</h3>
                <div id="sentimentOverview">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>Loading market sentiment...</div>
                    </div>
                </div>
            </div>

            <!-- Positions list will be populated here -->
            <div id="positionsList"></div>
        </div>

        <!-- Signals Tab -->
        <div id="signalsTab" class="tab-content hidden">
            <!-- Signal Filters -->
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">🔍 Signal Filters</h3>
                <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-bottom: 16px;">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="bullish">Bullish</button>
                    <button class="filter-btn" data-filter="bearish">Bearish</button>
                    <button class="filter-btn" data-filter="high-confidence">High Confidence</button>
                    <button class="filter-btn" data-filter="sentiment-positive">Positive Sentiment</button>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-size: 0.875rem; color: var(--text-secondary);">
                        <span id="signalCount">0</span> signals found
                    </span>
                    <button onclick="refreshSignals()" style="background: none; border: none; color: var(--primary-color); font-size: 1.2rem;">🔄</button>
                </div>
            </div>

            <!-- Active Signals -->
            <div class="card">
                <h2 style="margin-bottom: 16px; color: var(--primary-color);">🎯 Active Signals</h2>
                <div id="signalsList">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>Loading signals...</div>
                    </div>
                </div>
            </div>

            <!-- Signal Performance -->
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">📊 Signal Performance</h3>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="signalsToday">0</div>
                        <div class="metric-label">Signals Today</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="signalAccuracy">--</div>
                        <div class="metric-label">Accuracy (7d)</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="avgSignalReturn">--</div>
                        <div class="metric-label">Avg Return</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="sentimentBoost">--</div>
                        <div class="metric-label">Sentiment Boost</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trading Tab -->
        <div id="tradingTab" class="tab-content hidden">
            <div class="card">
                <h2 style="margin-bottom: 16px; color: var(--primary-color);">🤖 Trading Engine</h2>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="engineStatus">Stopped</div>
                        <div class="metric-label">Status</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="tradingMode">Paper</div>
                        <div class="metric-label">Mode</div>
                    </div>
                </div>
                
                <button class="emergency-button" onclick="emergencyStop()">
                    🚨 EMERGENCY STOP
                </button>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div id="analyticsTab" class="tab-content hidden">
            <!-- Performance Analytics -->
            <div class="card">
                <h2 style="margin-bottom: 16px; color: var(--primary-color);">📊 Performance Analytics</h2>

                <!-- Time Period Selector -->
                <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                    <button class="period-btn active" data-period="1d">1D</button>
                    <button class="period-btn" data-period="1w">1W</button>
                    <button class="period-btn" data-period="1m">1M</button>
                    <button class="period-btn" data-period="3m">3M</button>
                    <button class="period-btn" data-period="1y">1Y</button>
                </div>

                <!-- Performance Metrics -->
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="totalReturn">+$0</div>
                        <div class="metric-label">Total Return</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="totalReturnPercent">0.0%</div>
                        <div class="metric-label">Return %</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="volatility">--</div>
                        <div class="metric-label">Volatility</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="beta">--</div>
                        <div class="metric-label">Beta</div>
                    </div>
                </div>
            </div>

            <!-- News Sentiment Analysis -->
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">📰 News & Sentiment Analysis</h3>
                <div id="newsAnalysis">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>Loading news analysis...</div>
                    </div>
                </div>
            </div>

            <!-- Risk Metrics -->
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">⚠️ Risk Analysis</h3>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="portfolioRisk">Low</div>
                        <div class="metric-label">Risk Level</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="concentration">--</div>
                        <div class="metric-label">Concentration</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="correlation">--</div>
                        <div class="metric-label">Correlation</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="exposure">--</div>
                        <div class="metric-label">Market Exposure</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settingsTab" class="tab-content hidden">
            <!-- Notification Settings -->
            <div class="card">
                <h2 style="margin-bottom: 16px; color: var(--primary-color);">🔔 Push Notifications</h2>

                <div style="margin-bottom: 16px;">
                    <button id="enableNotifications" class="btn btn-primary" onclick="enablePushNotifications()">
                        Enable Push Notifications
                    </button>
                </div>

                <div style="margin-bottom: 24px;">
                    <h3 style="margin-bottom: 12px;">Notification Types</h3>
                    <div class="notification-setting">
                        <label>
                            <input type="checkbox" id="signalNotifications" checked>
                            <span>New Trading Signals</span>
                        </label>
                    </div>
                    <div class="notification-setting">
                        <label>
                            <input type="checkbox" id="portfolioNotifications" checked>
                            <span>Portfolio Milestones</span>
                        </label>
                    </div>
                    <div class="notification-setting">
                        <label>
                            <input type="checkbox" id="riskNotifications" checked>
                            <span>Risk Alerts</span>
                        </label>
                    </div>
                    <div class="notification-setting">
                        <label>
                            <input type="checkbox" id="newsNotifications">
                            <span>Important News</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Trading Settings -->
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">⚙️ Trading Settings</h3>

                <div style="margin-bottom: 24px;">
                    <h4 style="margin-bottom: 12px;">Trading</h4>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Trading Mode</span>
                        <span style="color: var(--text-secondary);" id="tradingModeDisplay">Paper Trading</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Risk Level</span>
                        <span style="color: var(--text-secondary);">Conservative</span>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="margin-bottom: 12px;">Algorithm</h4>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Enhanced Algorithm</span>
                        <span style="color: var(--success-color);">✅ Active</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>News Sentiment</span>
                        <span style="color: var(--success-color);">✅ Active</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Multi-timeframe</span>
                        <span style="color: var(--success-color);">✅ Active</span>
                    </div>
                </div>

                <div>
                    <h4 style="margin-bottom: 12px;">About</h4>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Version</span>
                        <span style="color: var(--text-secondary);">2.0.0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Last Updated</span>
                        <span style="color: var(--text-secondary);" id="lastUpdateTime">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <a href="#" class="tab-item active" onclick="showTab('portfolio', this)">
            <span class="tab-icon">💰</span>
            Portfolio
        </a>
        <a href="#" class="tab-item" onclick="showTab('signals', this)">
            <span class="tab-icon">🎯</span>
            Signals
        </a>
        <a href="#" class="tab-item" onclick="showTab('analytics', this)">
            <span class="tab-icon">📊</span>
            Analytics
        </a>
        <a href="#" class="tab-item" onclick="showTab('trading', this)">
            <span class="tab-icon">🤖</span>
            Trading
        </a>
        <a href="#" class="tab-item" onclick="showTab('settings', this)">
            <span class="tab-icon">⚙️</span>
            Settings
        </a>
    </div>

    <!-- JavaScript will be loaded from external file -->
    <script src="app.js"></script>
</body>
</html>
