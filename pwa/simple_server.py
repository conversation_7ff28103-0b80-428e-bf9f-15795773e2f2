#!/usr/bin/env python3
"""
Simple HTTP server for AWOT PWA - Minimal version
"""

import http.server
import socketserver
import os
import sys

class SimpleHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def main():
    PORT = 8001
    
    print(f"🌐 Starting simple PWA server on port {PORT}")
    print(f"📱 Access at: http://localhost:{PORT}")
    print(f"🔄 Press Ctrl+C to stop")
    
    try:
        with socketserver.TCPServer(("", PORT), SimpleHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
    except OSError as e:
        if e.errno == 98:
            print(f"❌ Port {PORT} is already in use")
            print("💡 Try: sudo lsof -i :3000 to see what's using it")
        else:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
