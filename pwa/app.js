// AWOT Trading PWA - Main Application Logic

class AWOTApp {
    constructor() {
        // Configuration - UPDATE THESE URLs TO YOUR SERVER
        this.API_BASE = 'http://localhost:8080/mobile';
        this.WS_URL = 'ws://localhost:8080/mobile/ws';
        
        // State
        this.isConnected = false;
        this.ws = null;
        this.reconnectInterval = null;
        this.currentTab = 'portfolio';
        this.data = {
            portfolio: null,
            positions: [],
            signals: [],
            engineStatus: 'stopped'
        };
        
        // Initialize app
        this.init();
    }
    
    async init() {
        console.log('🚀 Initializing AWOT Trading PWA');
        
        // Register service worker
        await this.registerServiceWorker();
        
        // Setup PWA install prompt
        this.setupInstallPrompt();
        
        // Setup push notifications
        await this.setupNotifications();
        
        // Connect to API and WebSocket
        await this.connectToAPI();
        this.connectWebSocket();
        
        // Setup periodic updates
        this.setupPeriodicUpdates();
        
        // Load initial data
        await this.loadAllData();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('✅ AWOT Trading PWA initialized successfully');
    }
    
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('sw.js');
                console.log('✅ Service Worker registered:', registration);
            } catch (error) {
                console.error('❌ Service Worker registration failed:', error);
            }
        }
    }
    
    setupInstallPrompt() {
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // Show install prompt
            const installPrompt = document.getElementById('installPrompt');
            installPrompt.classList.remove('hidden');
        });
        
        // Install app function
        window.installApp = async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to install prompt: ${outcome}`);
                deferredPrompt = null;
                this.hideInstallPrompt();
            }
        };
        
        // Hide install prompt function
        window.hideInstallPrompt = () => {
            const installPrompt = document.getElementById('installPrompt');
            installPrompt.classList.add('hidden');
        };
    }
    
    async setupNotifications() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                console.log('✅ Notification permission granted');
            }
        }
    }
    
    async connectToAPI() {
        try {
            const response = await fetch(`${this.API_BASE}/health`);
            if (response.ok) {
                this.updateConnectionStatus(true);
                console.log('✅ Connected to AWOT API');
            } else {
                throw new Error('API health check failed');
            }
        } catch (error) {
            console.error('❌ Failed to connect to API:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    connectWebSocket() {
        try {
            this.ws = new WebSocket(this.WS_URL);
            
            this.ws.onopen = () => {
                console.log('✅ WebSocket connected');
                this.updateConnectionStatus(true);
                if (this.reconnectInterval) {
                    clearInterval(this.reconnectInterval);
                    this.reconnectInterval = null;
                }
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('❌ Error parsing WebSocket message:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('🔌 WebSocket disconnected');
                this.updateConnectionStatus(false);
                this.scheduleReconnect();
            };
            
            this.ws.onerror = (error) => {
                console.error('❌ WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('❌ Failed to create WebSocket:', error);
            this.updateConnectionStatus(false);
            this.scheduleReconnect();
        }
    }
    
    scheduleReconnect() {
        if (!this.reconnectInterval) {
            this.reconnectInterval = setInterval(() => {
                console.log('🔄 Attempting to reconnect WebSocket...');
                this.connectWebSocket();
            }, 5000);
        }
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'portfolio_update':
                this.data.portfolio = data.data;
                this.updatePortfolioDisplay();
                break;
                
            case 'signal_update':
                this.data.signals = data.data;
                this.updateSignalsDisplay();
                this.showNotification('New Trading Signals', `${data.data.length} signals available`);
                break;
                
            case 'price_update':
                this.updatePrices(data.data);
                break;
                
            case 'notification':
                this.showNotification(data.data.title, data.data.message);
                break;
        }
    }
    
    updateConnectionStatus(connected) {
        this.isConnected = connected;
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('connectionStatus');
        
        if (connected) {
            statusDot.classList.remove('disconnected');
            statusText.textContent = 'Connected';
        } else {
            statusDot.classList.add('disconnected');
            statusText.textContent = 'Disconnected';
        }
    }
    
    async loadAllData() {
        await Promise.all([
            this.loadPortfolio(),
            this.loadSignals(),
            this.loadPositions(),
            this.loadEngineStatus()
        ]);
    }
    
    async loadPortfolio() {
        try {
            const response = await fetch(`${this.API_BASE}/portfolio`);
            if (response.ok) {
                this.data.portfolio = await response.json();
                this.updatePortfolioDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading portfolio:', error);
        }
    }
    
    async loadSignals() {
        try {
            const response = await fetch(`${this.API_BASE}/signals`);
            if (response.ok) {
                this.data.signals = await response.json();
                this.updateSignalsDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading signals:', error);
        }
    }
    
    async loadPositions() {
        try {
            const response = await fetch(`${this.API_BASE}/positions`);
            if (response.ok) {
                this.data.positions = await response.json();
                this.updatePositionsDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading positions:', error);
        }
    }
    
    async loadEngineStatus() {
        try {
            const response = await fetch(`${this.API_BASE}/engine-status`);
            if (response.ok) {
                const status = await response.json();
                this.data.engineStatus = status.state;
                this.updateEngineStatusDisplay(status);
            }
        } catch (error) {
            console.error('❌ Error loading engine status:', error);
        }
    }
    
    updatePortfolioDisplay() {
        if (!this.data.portfolio) return;
        
        const portfolio = this.data.portfolio;
        
        // Update portfolio value
        document.getElementById('portfolioValue').textContent = 
            this.formatCurrency(portfolio.portfolio_value);
        
        // Update daily P&L
        const pnlElement = document.getElementById('dailyPnL');
        const pnlText = `${portfolio.daily_pnl >= 0 ? '+' : ''}${this.formatCurrency(portfolio.daily_pnl)} (${portfolio.daily_pnl_percent >= 0 ? '+' : ''}${portfolio.daily_pnl_percent.toFixed(2)}%)`;
        pnlElement.textContent = pnlText;
        pnlElement.className = portfolio.daily_pnl >= 0 ? 'pnl-positive' : 'pnl-negative';
        
        // Update metrics
        document.getElementById('openPositions').textContent = portfolio.open_positions;
        document.getElementById('todayTrades').textContent = portfolio.todays_trades;
        document.getElementById('winRate').textContent = portfolio.win_rate ? 
            `${(portfolio.win_rate * 100).toFixed(1)}%` : '--';
        document.getElementById('availableCash').textContent = 
            this.formatCurrency(portfolio.available_cash);
    }
    
    updateSignalsDisplay() {
        const signalsList = document.getElementById('signalsList');
        
        if (!this.data.signals || this.data.signals.length === 0) {
            signalsList.innerHTML = `
                <div class="loading">
                    <div style="font-size: 2rem; margin-bottom: 16px;">📊</div>
                    <div>No active signals</div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 8px;">
                        Signals will appear when market conditions are favorable
                    </div>
                </div>
            `;
            return;
        }
        
        signalsList.innerHTML = this.data.signals.map(signal => `
            <div class="signal-card">
                <div class="signal-header">
                    <div>
                        <div class="signal-symbol">${signal.symbol}</div>
                        <div class="signal-type">${signal.signal_type.replace('_', ' ')}</div>
                    </div>
                    <div class="signal-confidence">${(signal.confidence * 100).toFixed(0)}%</div>
                </div>
                
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <div class="sentiment-indicator ${signal.sentiment.includes('negative') ? 'sentiment-negative' : ''}">
                        ${this.getSentimentIcon(signal.sentiment)} ${signal.sentiment.replace('_', ' ')}
                    </div>
                    <div style="font-size: 0.75rem; color: var(--text-secondary);">
                        ${this.formatTimeAgo(signal.created_at)}
                    </div>
                </div>
                
                <div class="signal-prices">
                    <div class="price-item">
                        <div class="price-label">Entry</div>
                        <div class="price-value">${this.formatCurrency(signal.entry_price)}</div>
                    </div>
                    <div class="price-item">
                        <div class="price-label">Target</div>
                        <div class="price-value">${this.formatCurrency(signal.target_price)}</div>
                    </div>
                    <div class="price-item">
                        <div class="price-label">Stop</div>
                        <div class="price-value">${this.formatCurrency(signal.stop_loss_price)}</div>
                    </div>
                </div>
                
                ${signal.reasoning ? `
                    <div style="margin-top: 12px; padding: 12px; background: rgba(0, 0, 0, 0.05); border-radius: 8px; font-size: 0.875rem;">
                        ${signal.reasoning}
                    </div>
                ` : ''}
            </div>
        `).join('');
    }
    
    updatePositionsDisplay() {
        const positionsList = document.getElementById('positionsList');
        
        if (!this.data.positions || this.data.positions.length === 0) {
            positionsList.innerHTML = '';
            return;
        }
        
        positionsList.innerHTML = `
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">📊 Open Positions</h3>
                ${this.data.positions.map(position => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                        <div>
                            <div style="font-weight: 600; margin-bottom: 4px;">${position.symbol}</div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary);">${position.quantity} shares</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-weight: 600; color: ${position.unrealized_pnl >= 0 ? 'var(--success-color)' : 'var(--danger-color)'};">
                                ${this.formatCurrency(position.unrealized_pnl)}
                            </div>
                            <div style="font-size: 0.875rem; color: ${position.unrealized_pnl >= 0 ? 'var(--success-color)' : 'var(--danger-color)'};">
                                ${position.unrealized_pnl_percent >= 0 ? '+' : ''}${position.unrealized_pnl_percent.toFixed(2)}%
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    updateEngineStatusDisplay(status) {
        document.getElementById('engineStatus').textContent = status.state.charAt(0).toUpperCase() + status.state.slice(1);
        document.getElementById('tradingMode').textContent = status.trading_mode.charAt(0).toUpperCase() + status.trading_mode.slice(1);
    }
    
    setupPeriodicUpdates() {
        // Update data every 30 seconds
        setInterval(() => {
            if (this.isConnected) {
                this.loadAllData();
            }
        }, 30000);
        
        // Check connection every 10 seconds
        setInterval(() => {
            this.connectToAPI();
        }, 10000);
    }
    
    setupEventListeners() {
        // Handle app lifecycle
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.loadAllData();
                if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                    this.connectWebSocket();
                }
            }
        });
        
        // Handle online/offline
        window.addEventListener('online', () => {
            console.log('📶 Back online');
            this.connectToAPI();
            this.connectWebSocket();
        });
        
        window.addEventListener('offline', () => {
            console.log('📵 Gone offline');
            this.updateConnectionStatus(false);
        });
    }
    
    // Utility functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }
    
    formatTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffMs = now - time;
        const diffMins = Math.floor(diffMs / 60000);
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        
        const diffHours = Math.floor(diffMins / 60);
        if (diffHours < 24) return `${diffHours}h ago`;
        
        const diffDays = Math.floor(diffHours / 24);
        return `${diffDays}d ago`;
    }
    
    getSentimentIcon(sentiment) {
        switch (sentiment.toLowerCase()) {
            case 'very_positive': return '🚀';
            case 'positive': return '📈';
            case 'negative': return '📉';
            case 'very_negative': return '⚠️';
            default: return '😐';
        }
    }
    
    showNotification(title, body) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: body,
                icon: 'icons/icon-192.png',
                badge: 'icons/icon-72.png',
                tag: 'awot-notification'
            });
        }
    }
    
    async emergencyStop() {
        if (confirm('⚠️ Are you sure you want to STOP ALL TRADING?\n\nThis will:\n• Stop the trading engine\n• Cancel pending orders\n• Prevent new trades')) {
            try {
                const response = await fetch(`${this.API_BASE}/emergency-stop`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        this.showNotification('Emergency Stop Activated', 'All trading has been halted');
                        await this.loadAllData();
                    } else {
                        alert('❌ Failed to activate emergency stop');
                    }
                } else {
                    alert('❌ Failed to activate emergency stop. Please check manually.');
                }
            } catch (error) {
                console.error('Emergency stop error:', error);
                alert('❌ Error activating emergency stop. Please check your connection.');
            }
        }
    }
}

// Tab management
function showTab(tabName, element) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Show selected tab
    document.getElementById(tabName + 'Tab').classList.remove('hidden');
    
    // Update tab bar
    document.querySelectorAll('.tab-item').forEach(item => {
        item.classList.remove('active');
    });
    element.classList.add('active');
    
    // Load data for specific tabs
    if (tabName === 'signals' && window.awotApp) {
        window.awotApp.loadSignals();
    }
}

// Emergency stop function (global)
function emergencyStop() {
    if (window.awotApp) {
        window.awotApp.emergencyStop();
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.awotApp = new AWOTApp();
});
