// AWOT Trading PWA - Main Application Logic

class AWOTApp {
    constructor() {
        // Configuration - UPDATE THESE URLs TO YOUR SERVER
        this.API_BASE = 'http://localhost:8080/mobile';
        this.WS_URL = 'ws://localhost:8080/mobile/ws';
        
        // State
        this.isConnected = false;
        this.ws = null;
        this.reconnectInterval = null;
        this.currentTab = 'portfolio';
        this.data = {
            portfolio: null,
            positions: [],
            signals: [],
            engineStatus: 'stopped',
            performance: null,
            newsAnalysis: null,
            riskMetrics: null,
            algorithmStats: null
        };

        // Performance tracking
        this.performanceHistory = [];
        this.signalPerformance = [];

        // Notification settings
        this.notificationSettings = {
            signals: true,
            portfolio: true,
            risk: true,
            news: false
        };

        // Filter states
        this.signalFilter = 'all';
        this.timePeriod = '1d';
        
        // Initialize app
        this.init();
    }
    
    async init() {
        console.log('🚀 Initializing AWOT Trading PWA');
        
        // Register service worker
        await this.registerServiceWorker();
        
        // Setup PWA install prompt
        this.setupInstallPrompt();
        
        // Setup push notifications
        await this.setupNotifications();
        
        // Connect to API and WebSocket
        await this.connectToAPI();
        this.connectWebSocket();
        
        // Setup periodic updates
        this.setupPeriodicUpdates();
        
        // Load initial data
        await this.loadAllData();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('✅ AWOT Trading PWA initialized successfully');
    }
    
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('sw.js');
                console.log('✅ Service Worker registered:', registration);
            } catch (error) {
                console.error('❌ Service Worker registration failed:', error);
            }
        }
    }
    
    setupInstallPrompt() {
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // Show install prompt
            const installPrompt = document.getElementById('installPrompt');
            installPrompt.classList.remove('hidden');
        });
        
        // Install app function
        window.installApp = async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to install prompt: ${outcome}`);
                deferredPrompt = null;
                this.hideInstallPrompt();
            }
        };
        
        // Hide install prompt function
        window.hideInstallPrompt = () => {
            const installPrompt = document.getElementById('installPrompt');
            installPrompt.classList.add('hidden');
        };
    }
    
    async setupNotifications() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                console.log('✅ Notification permission granted');

                // Register for push notifications if supported
                if ('serviceWorker' in navigator && 'PushManager' in window) {
                    try {
                        const registration = await navigator.serviceWorker.ready;
                        const subscription = await registration.pushManager.subscribe({
                            userVisibleOnly: true,
                            applicationServerKey: this.urlBase64ToUint8Array('YOUR_VAPID_PUBLIC_KEY') // You'll need to generate this
                        });

                        console.log('✅ Push subscription created');
                        // Send subscription to server
                        await this.sendSubscriptionToServer(subscription);

                    } catch (error) {
                        console.log('⚠️ Push notifications not available:', error);
                    }
                }

                // Update UI
                this.updateNotificationUI(true);
            } else {
                this.updateNotificationUI(false);
            }
        }
    }

    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/\-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    async sendSubscriptionToServer(subscription) {
        try {
            await fetch(`${this.API_BASE}/push-subscription`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(subscription)
            });
        } catch (error) {
            console.error('Failed to send subscription to server:', error);
        }
    }

    updateNotificationUI(enabled) {
        const button = document.getElementById('enableNotifications');
        if (button) {
            if (enabled) {
                button.textContent = '✅ Notifications Enabled';
                button.disabled = true;
                button.style.background = 'var(--success-color)';
            } else {
                button.textContent = 'Enable Push Notifications';
                button.disabled = false;
            }
        }
    }
    
    async connectToAPI() {
        try {
            const response = await fetch(`${this.API_BASE}/health`);
            if (response.ok) {
                this.updateConnectionStatus(true);
                console.log('✅ Connected to AWOT API');
            } else {
                throw new Error('API health check failed');
            }
        } catch (error) {
            console.error('❌ Failed to connect to API:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    connectWebSocket() {
        try {
            this.ws = new WebSocket(this.WS_URL);
            
            this.ws.onopen = () => {
                console.log('✅ WebSocket connected');
                this.updateConnectionStatus(true);
                if (this.reconnectInterval) {
                    clearInterval(this.reconnectInterval);
                    this.reconnectInterval = null;
                }
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('❌ Error parsing WebSocket message:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('🔌 WebSocket disconnected');
                this.updateConnectionStatus(false);
                this.scheduleReconnect();
            };
            
            this.ws.onerror = (error) => {
                console.error('❌ WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('❌ Failed to create WebSocket:', error);
            this.updateConnectionStatus(false);
            this.scheduleReconnect();
        }
    }
    
    scheduleReconnect() {
        if (!this.reconnectInterval) {
            this.reconnectInterval = setInterval(() => {
                console.log('🔄 Attempting to reconnect WebSocket...');
                this.connectWebSocket();
            }, 5000);
        }
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'portfolio_update':
                this.data.portfolio = data.data;
                this.updatePortfolioDisplay();
                break;
                
            case 'signal_update':
                this.data.signals = data.data;
                this.updateSignalsDisplay();
                this.showNotification('New Trading Signals', `${data.data.length} signals available`);
                break;
                
            case 'price_update':
                this.updatePrices(data.data);
                break;
                
            case 'notification':
                this.showNotification(data.data.title, data.data.message);
                break;
        }
    }
    
    updateConnectionStatus(connected) {
        this.isConnected = connected;
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('connectionStatus');
        
        if (connected) {
            statusDot.classList.remove('disconnected');
            statusText.textContent = 'Connected';
        } else {
            statusDot.classList.add('disconnected');
            statusText.textContent = 'Disconnected';
        }
    }
    
    async loadAllData() {
        await Promise.all([
            this.loadPortfolio(),
            this.loadSignals(),
            this.loadPositions(),
            this.loadEngineStatus(),
            this.loadPerformanceData(),
            this.loadNewsAnalysis(),
            this.loadRiskMetrics(),
            this.loadAlgorithmStats()
        ]);
    }
    
    async loadPortfolio() {
        try {
            const response = await fetch(`${this.API_BASE}/portfolio`);
            if (response.ok) {
                this.data.portfolio = await response.json();
                this.updatePortfolioDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading portfolio:', error);
        }
    }
    
    async loadSignals() {
        try {
            const response = await fetch(`${this.API_BASE}/signals`);
            if (response.ok) {
                this.data.signals = await response.json();
                this.updateSignalsDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading signals:', error);
        }
    }
    
    async loadPositions() {
        try {
            const response = await fetch(`${this.API_BASE}/positions`);
            if (response.ok) {
                this.data.positions = await response.json();
                this.updatePositionsDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading positions:', error);
        }
    }
    
    async loadEngineStatus() {
        try {
            const response = await fetch(`${this.API_BASE}/engine-status`);
            if (response.ok) {
                const status = await response.json();
                this.data.engineStatus = status.state;
                this.updateEngineStatusDisplay(status);
            }
        } catch (error) {
            console.error('❌ Error loading engine status:', error);
        }
    }

    async loadPerformanceData() {
        try {
            const response = await fetch(`${this.API_BASE}/performance?period=${this.timePeriod}`);
            if (response.ok) {
                this.data.performance = await response.json();
                this.updatePerformanceDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading performance data:', error);
        }
    }

    async loadNewsAnalysis() {
        try {
            const response = await fetch(`${this.API_BASE}/news-analysis`);
            if (response.ok) {
                this.data.newsAnalysis = await response.json();
                this.updateNewsDisplay();
                this.updateSentimentOverview();
            }
        } catch (error) {
            console.error('❌ Error loading news analysis:', error);
        }
    }

    async loadRiskMetrics() {
        try {
            const response = await fetch(`${this.API_BASE}/risk-metrics`);
            if (response.ok) {
                this.data.riskMetrics = await response.json();
                this.updateRiskDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading risk metrics:', error);
        }
    }

    async loadAlgorithmStats() {
        try {
            const response = await fetch(`${this.API_BASE}/algorithm-stats`);
            if (response.ok) {
                this.data.algorithmStats = await response.json();
                this.updateAlgorithmDisplay();
            }
        } catch (error) {
            console.error('❌ Error loading algorithm stats:', error);
        }
    }
    
    updatePortfolioDisplay() {
        if (!this.data.portfolio) return;
        
        const portfolio = this.data.portfolio;
        
        // Update portfolio value
        document.getElementById('portfolioValue').textContent = 
            this.formatCurrency(portfolio.portfolio_value);
        
        // Update daily P&L
        const pnlElement = document.getElementById('dailyPnL');
        const pnlText = `${portfolio.daily_pnl >= 0 ? '+' : ''}${this.formatCurrency(portfolio.daily_pnl)} (${portfolio.daily_pnl_percent >= 0 ? '+' : ''}${portfolio.daily_pnl_percent.toFixed(2)}%)`;
        pnlElement.textContent = pnlText;
        pnlElement.className = portfolio.daily_pnl >= 0 ? 'pnl-positive' : 'pnl-negative';
        
        // Update metrics
        document.getElementById('openPositions').textContent = portfolio.open_positions;
        document.getElementById('todayTrades').textContent = portfolio.todays_trades;
        document.getElementById('winRate').textContent = portfolio.win_rate ? 
            `${(portfolio.win_rate * 100).toFixed(1)}%` : '--';
        document.getElementById('availableCash').textContent = 
            this.formatCurrency(portfolio.available_cash);
    }
    
    updateSignalsDisplay() {
        const signalsList = document.getElementById('signalsList');
        
        if (!this.data.signals || this.data.signals.length === 0) {
            signalsList.innerHTML = `
                <div class="loading">
                    <div style="font-size: 2rem; margin-bottom: 16px;">📊</div>
                    <div>No active signals</div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 8px;">
                        Signals will appear when market conditions are favorable
                    </div>
                </div>
            `;
            return;
        }
        
        signalsList.innerHTML = this.data.signals.map(signal => `
            <div class="signal-card">
                <div class="signal-header">
                    <div>
                        <div class="signal-symbol">${signal.symbol}</div>
                        <div class="signal-type">${signal.signal_type.replace('_', ' ')}</div>
                    </div>
                    <div class="signal-confidence">${(signal.confidence * 100).toFixed(0)}%</div>
                </div>
                
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <div class="sentiment-indicator ${signal.sentiment.includes('negative') ? 'sentiment-negative' : ''}">
                        ${this.getSentimentIcon(signal.sentiment)} ${signal.sentiment.replace('_', ' ')}
                    </div>
                    <div style="font-size: 0.75rem; color: var(--text-secondary);">
                        ${this.formatTimeAgo(signal.created_at)}
                    </div>
                </div>
                
                <div class="signal-prices">
                    <div class="price-item">
                        <div class="price-label">Entry</div>
                        <div class="price-value">${this.formatCurrency(signal.entry_price)}</div>
                    </div>
                    <div class="price-item">
                        <div class="price-label">Target</div>
                        <div class="price-value">${this.formatCurrency(signal.target_price)}</div>
                    </div>
                    <div class="price-item">
                        <div class="price-label">Stop</div>
                        <div class="price-value">${this.formatCurrency(signal.stop_loss_price)}</div>
                    </div>
                </div>
                
                ${signal.reasoning ? `
                    <div style="margin-top: 12px; padding: 12px; background: rgba(0, 0, 0, 0.05); border-radius: 8px; font-size: 0.875rem;">
                        ${signal.reasoning}
                    </div>
                ` : ''}
            </div>
        `).join('');
    }
    
    updatePositionsDisplay() {
        const positionsList = document.getElementById('positionsList');
        
        if (!this.data.positions || this.data.positions.length === 0) {
            positionsList.innerHTML = '';
            return;
        }
        
        positionsList.innerHTML = `
            <div class="card">
                <h3 style="margin-bottom: 16px; color: var(--primary-color);">📊 Open Positions</h3>
                ${this.data.positions.map(position => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                        <div>
                            <div style="font-weight: 600; margin-bottom: 4px;">${position.symbol}</div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary);">${position.quantity} shares</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-weight: 600; color: ${position.unrealized_pnl >= 0 ? 'var(--success-color)' : 'var(--danger-color)'};">
                                ${this.formatCurrency(position.unrealized_pnl)}
                            </div>
                            <div style="font-size: 0.875rem; color: ${position.unrealized_pnl >= 0 ? 'var(--success-color)' : 'var(--danger-color)'};">
                                ${position.unrealized_pnl_percent >= 0 ? '+' : ''}${position.unrealized_pnl_percent.toFixed(2)}%
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    updateEngineStatusDisplay(status) {
        document.getElementById('engineStatus').textContent = status.state.charAt(0).toUpperCase() + status.state.slice(1);
        document.getElementById('tradingMode').textContent = status.trading_mode.charAt(0).toUpperCase() + status.trading_mode.slice(1);

        // Update trading mode in settings
        const tradingModeDisplay = document.getElementById('tradingModeDisplay');
        if (tradingModeDisplay) {
            tradingModeDisplay.textContent = status.trading_mode.charAt(0).toUpperCase() + status.trading_mode.slice(1);
        }
    }

    updatePerformanceDisplay() {
        if (!this.data.performance) return;

        const perf = this.data.performance;

        // Update analytics tab metrics
        document.getElementById('totalReturn').textContent = this.formatCurrency(perf.total_return || 0);
        document.getElementById('totalReturnPercent').textContent = `${(perf.total_return_percent || 0).toFixed(2)}%`;
        document.getElementById('volatility').textContent = perf.volatility ? `${(perf.volatility * 100).toFixed(1)}%` : '--';
        document.getElementById('beta').textContent = perf.beta ? perf.beta.toFixed(2) : '--';

        // Update algorithm performance in portfolio tab
        document.getElementById('algoWinRate').textContent = perf.algorithm_win_rate ? `${(perf.algorithm_win_rate * 100).toFixed(1)}%` : '--';
        document.getElementById('avgReturn').textContent = perf.avg_return ? `${(perf.avg_return * 100).toFixed(2)}%` : '--';
        document.getElementById('sharpeRatio').textContent = perf.sharpe_ratio ? perf.sharpe_ratio.toFixed(2) : '--';
        document.getElementById('maxDrawdown').textContent = perf.max_drawdown ? `${(perf.max_drawdown * 100).toFixed(1)}%` : '--';
    }

    updateNewsDisplay() {
        if (!this.data.newsAnalysis) return;

        const newsContainer = document.getElementById('newsAnalysis');
        const news = this.data.newsAnalysis.recent_news || [];

        if (news.length === 0) {
            newsContainer.innerHTML = `
                <div style="text-align: center; color: var(--text-secondary); padding: 20px;">
                    📰 No recent news analysis available
                </div>
            `;
            return;
        }

        newsContainer.innerHTML = news.slice(0, 5).map(item => `
            <div class="news-item">
                <div class="news-headline">${item.headline}</div>
                <div class="news-meta">
                    <span>${this.formatTimeAgo(item.published_at)}</span>
                    <span class="news-sentiment ${item.sentiment.toLowerCase()}">${item.sentiment}</span>
                </div>
            </div>
        `).join('');
    }

    updateSentimentOverview() {
        if (!this.data.newsAnalysis) return;

        const sentimentContainer = document.getElementById('sentimentOverview');
        const sentiment = this.data.newsAnalysis.overall_sentiment;

        if (!sentiment) {
            sentimentContainer.innerHTML = `
                <div style="text-align: center; color: var(--text-secondary); padding: 20px;">
                    📊 Loading sentiment analysis...
                </div>
            `;
            return;
        }

        const sentimentColor = sentiment.sentiment === 'positive' ? 'var(--success-color)' :
                              sentiment.sentiment === 'negative' ? 'var(--danger-color)' : 'var(--text-secondary)';

        sentimentContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: ${sentimentColor};">
                        ${sentiment.sentiment.charAt(0).toUpperCase() + sentiment.sentiment.slice(1)}
                    </div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary);">
                        Overall Market Sentiment
                    </div>
                </div>
                <div style="font-size: 2rem;">
                    ${this.getSentimentIcon(sentiment.sentiment)}
                </div>
            </div>
            <div style="background: rgba(0, 0, 0, 0.05); border-radius: 8px; padding: 12px;">
                <div style="font-size: 0.875rem; margin-bottom: 8px;">
                    <strong>Score:</strong> ${(sentiment.score * 100).toFixed(1)}%
                </div>
                <div style="font-size: 0.875rem;">
                    <strong>Sources:</strong> ${sentiment.sources_count || 0} news articles analyzed
                </div>
            </div>
        `;
    }

    updateRiskDisplay() {
        if (!this.data.riskMetrics) return;

        const risk = this.data.riskMetrics;

        document.getElementById('portfolioRisk').textContent = risk.risk_level || 'Low';
        document.getElementById('concentration').textContent = risk.concentration ? `${(risk.concentration * 100).toFixed(1)}%` : '--';
        document.getElementById('correlation').textContent = risk.correlation ? risk.correlation.toFixed(2) : '--';
        document.getElementById('exposure').textContent = risk.market_exposure ? `${(risk.market_exposure * 100).toFixed(1)}%` : '--';
    }

    updateAlgorithmDisplay() {
        if (!this.data.algorithmStats) return;

        const stats = this.data.algorithmStats;

        // Update signal performance metrics
        document.getElementById('signalsToday').textContent = stats.signals_today || 0;
        document.getElementById('signalAccuracy').textContent = stats.accuracy_7d ? `${(stats.accuracy_7d * 100).toFixed(1)}%` : '--';
        document.getElementById('avgSignalReturn').textContent = stats.avg_signal_return ? `${(stats.avg_signal_return * 100).toFixed(2)}%` : '--';
        document.getElementById('sentimentBoost').textContent = stats.sentiment_boost ? `+${(stats.sentiment_boost * 100).toFixed(1)}%` : '--';

        // Update last update time
        const lastUpdateElement = document.getElementById('lastUpdateTime');
        if (lastUpdateElement) {
            lastUpdateElement.textContent = this.formatTimeAgo(stats.last_updated || new Date().toISOString());
        }
    }
    
    setupPeriodicUpdates() {
        // Update data every 30 seconds
        setInterval(() => {
            if (this.isConnected) {
                this.loadAllData();
            }
        }, 30000);
        
        // Check connection every 10 seconds
        setInterval(() => {
            this.connectToAPI();
        }, 10000);
    }
    
    setupEventListeners() {
        // Handle app lifecycle
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.loadAllData();
                if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                    this.connectWebSocket();
                }
            }
        });
        
        // Handle online/offline
        window.addEventListener('online', () => {
            console.log('📶 Back online');
            this.connectToAPI();
            this.connectWebSocket();
        });
        
        window.addEventListener('offline', () => {
            console.log('📵 Gone offline');
            this.updateConnectionStatus(false);
        });
    }
    
    // Utility functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }
    
    formatTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffMs = now - time;
        const diffMins = Math.floor(diffMs / 60000);
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        
        const diffHours = Math.floor(diffMins / 60);
        if (diffHours < 24) return `${diffHours}h ago`;
        
        const diffDays = Math.floor(diffHours / 24);
        return `${diffDays}d ago`;
    }
    
    getSentimentIcon(sentiment) {
        switch (sentiment.toLowerCase()) {
            case 'very_positive': return '🚀';
            case 'positive': return '📈';
            case 'negative': return '📉';
            case 'very_negative': return '⚠️';
            default: return '😐';
        }
    }
    
    showNotification(title, body, type = 'general') {
        // Check if this type of notification is enabled
        if (!this.notificationSettings[type]) {
            return;
        }

        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: body,
                icon: 'icons/icon-192.png',
                badge: 'icons/icon-72.png',
                tag: `awot-${type}`,
                requireInteraction: type === 'risk',
                actions: type === 'signals' ? [
                    { action: 'view', title: 'View Signals' },
                    { action: 'dismiss', title: 'Dismiss' }
                ] : []
            });

            notification.onclick = () => {
                window.focus();
                if (type === 'signals') {
                    this.showTab('signals');
                } else if (type === 'portfolio') {
                    this.showTab('portfolio');
                }
                notification.close();
            };
        }
    }

    // Enhanced notification methods
    showSignalNotification(signal) {
        const title = `🎯 New ${signal.signal_type.replace('_', ' ')} Signal`;
        const body = `${signal.symbol}: ${(signal.confidence * 100).toFixed(0)}% confidence - $${signal.entry_price.toFixed(2)}`;
        this.showNotification(title, body, 'signals');
    }

    showPortfolioNotification(milestone) {
        const title = `📊 Portfolio Milestone`;
        const body = milestone;
        this.showNotification(title, body, 'portfolio');
    }

    showRiskNotification(alert) {
        const title = `⚠️ Risk Alert`;
        const body = alert;
        this.showNotification(title, body, 'risk');
    }

    showNewsNotification(news) {
        const title = `📰 Important Market News`;
        const body = news.headline;
        this.showNotification(title, body, 'news');
    }
    
    async emergencyStop() {
        if (confirm('⚠️ Are you sure you want to STOP ALL TRADING?\n\nThis will:\n• Stop the trading engine\n• Cancel pending orders\n• Prevent new trades')) {
            try {
                const response = await fetch(`${this.API_BASE}/emergency-stop`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        this.showNotification('Emergency Stop Activated', 'All trading has been halted');
                        await this.loadAllData();
                    } else {
                        alert('❌ Failed to activate emergency stop');
                    }
                } else {
                    alert('❌ Failed to activate emergency stop. Please check manually.');
                }
            } catch (error) {
                console.error('Emergency stop error:', error);
                alert('❌ Error activating emergency stop. Please check your connection.');
            }
        }
    }
}

// Tab management
function showTab(tabName, element) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Show selected tab
    document.getElementById(tabName + 'Tab').classList.remove('hidden');
    
    // Update tab bar
    document.querySelectorAll('.tab-item').forEach(item => {
        item.classList.remove('active');
    });
    element.classList.add('active');
    
    // Load data for specific tabs
    if (tabName === 'signals' && window.awotApp) {
        window.awotApp.loadSignals();
    }
}

// Emergency stop function (global)
function emergencyStop() {
    if (window.awotApp) {
        window.awotApp.emergencyStop();
    }
}

// Enhanced notification functions (global)
function enablePushNotifications() {
    if (window.awotApp) {
        window.awotApp.setupNotifications();
    }
}

function refreshSignals() {
    if (window.awotApp) {
        window.awotApp.loadSignals();
    }
}

// Filter functions
function setSignalFilter(filter) {
    if (window.awotApp) {
        window.awotApp.signalFilter = filter;
        window.awotApp.updateSignalsDisplay();

        // Update filter button states
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.filter === filter) {
                btn.classList.add('active');
            }
        });
    }
}

function setTimePeriod(period) {
    if (window.awotApp) {
        window.awotApp.timePeriod = period;
        window.awotApp.loadPerformanceData();

        // Update period button states
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.period === period) {
                btn.classList.add('active');
            }
        });
    }
}

// Setup event listeners for new features
function setupEnhancedEventListeners() {
    // Filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            setSignalFilter(btn.dataset.filter);
        });
    });

    // Period buttons
    document.querySelectorAll('.period-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            setTimePeriod(btn.dataset.period);
        });
    });

    // Notification checkboxes
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            if (window.awotApp) {
                const type = e.target.id.replace('Notifications', '').replace('Notification', '');
                window.awotApp.notificationSettings[type] = e.target.checked;

                // Save to localStorage
                localStorage.setItem('awot-notification-settings', JSON.stringify(window.awotApp.notificationSettings));
            }
        });
    });
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.awotApp = new AWOTApp();

    // Setup enhanced event listeners after a short delay
    setTimeout(() => {
        setupEnhancedEventListeners();

        // Load notification settings from localStorage
        const savedSettings = localStorage.getItem('awot-notification-settings');
        if (savedSettings && window.awotApp) {
            window.awotApp.notificationSettings = { ...window.awotApp.notificationSettings, ...JSON.parse(savedSettings) };

            // Update checkbox states
            Object.keys(window.awotApp.notificationSettings).forEach(key => {
                const checkbox = document.getElementById(key + 'Notifications') || document.getElementById(key + 'Notification');
                if (checkbox) {
                    checkbox.checked = window.awotApp.notificationSettings[key];
                }
            });
        }
    }, 1000);
});
