// AWOT Trading PWA - Service Worker
// Provides offline functionality and caching

const CACHE_NAME = 'awot-trading-v1.0.0';
const STATIC_CACHE_NAME = 'awot-static-v1.0.0';
const API_CACHE_NAME = 'awot-api-v1.0.0';

// Files to cache for offline use
const STATIC_FILES = [
    '/',
    '/index.html',
    '/app.js',
    '/manifest.json',
    '/icons/icon-192.png',
    '/icons/icon-512.png'
];

// API endpoints to cache
const API_ENDPOINTS = [
    '/mobile/portfolio',
    '/mobile/signals',
    '/mobile/positions',
    '/mobile/engine-status',
    '/mobile/health'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker installing...');
    
    event.waitUntil(
        Promise.all([
            // Cache static files
            caches.open(STATIC_CACHE_NAME).then((cache) => {
                console.log('📦 Caching static files');
                return cache.addAll(STATIC_FILES);
            }),
            
            // Skip waiting to activate immediately
            self.skipWaiting()
        ])
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('✅ Service Worker activating...');
    
    event.waitUntil(
        Promise.all([
            // Clean up old caches
            caches.keys().then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== API_CACHE_NAME &&
                            cacheName !== CACHE_NAME) {
                            console.log('🗑️ Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            
            // Take control of all clients
            self.clients.claim()
        ])
    );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle different types of requests
    if (request.method === 'GET') {
        if (url.pathname.startsWith('/mobile/')) {
            // API requests - network first, cache fallback
            event.respondWith(handleAPIRequest(request));
        } else {
            // Static files - cache first, network fallback
            event.respondWith(handleStaticRequest(request));
        }
    } else {
        // POST/PUT/DELETE requests - network only
        event.respondWith(fetch(request));
    }
});

// Handle API requests with network-first strategy
async function handleAPIRequest(request) {
    const cache = await caches.open(API_CACHE_NAME);
    
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            cache.put(request, networkResponse.clone());
            return networkResponse;
        } else {
            throw new Error('Network response not ok');
        }
    } catch (error) {
        console.log('📡 Network failed, trying cache for:', request.url);
        
        // Fallback to cache
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline response for API endpoints
        return createOfflineAPIResponse(request);
    }
}

// Handle static file requests with cache-first strategy
async function handleStaticRequest(request) {
    const cache = await caches.open(STATIC_CACHE_NAME);
    
    // Try cache first
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        // Fallback to network
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache the response
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('📡 Network failed for static file:', request.url);
        
        // Return offline page for navigation requests
        if (request.mode === 'navigate') {
            return cache.match('/index.html');
        }
        
        // Return empty response for other requests
        return new Response('', { status: 404 });
    }
}

// Create offline API responses
function createOfflineAPIResponse(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    let offlineData = {};
    
    switch (pathname) {
        case '/mobile/portfolio':
            offlineData = {
                portfolio_value: 50000.0,
                daily_pnl: 0.0,
                daily_pnl_percent: 0.0,
                available_cash: 50000.0,
                open_positions: 0,
                todays_trades: 0,
                win_rate: null,
                total_return: 0.0,
                total_return_percent: 0.0,
                last_updated: new Date().toISOString(),
                offline: true
            };
            break;
            
        case '/mobile/signals':
            offlineData = [];
            break;
            
        case '/mobile/positions':
            offlineData = [];
            break;
            
        case '/mobile/engine-status':
            offlineData = {
                state: 'offline',
                trading_mode: 'unknown',
                active_strategies: [],
                uptime: '0m',
                last_signal_time: null,
                orders_today: 0,
                performance_today: 0.0
            };
            break;
            
        case '/mobile/health':
            offlineData = {
                status: 'offline',
                timestamp: new Date().toISOString(),
                connected_clients: 0,
                version: '1.0.0'
            };
            break;
            
        default:
            offlineData = { error: 'Offline', message: 'No network connection' };
    }
    
    return new Response(JSON.stringify(offlineData), {
        status: 200,
        headers: {
            'Content-Type': 'application/json',
            'X-Offline': 'true'
        }
    });
}

// Handle push notifications
self.addEventListener('push', (event) => {
    console.log('📬 Push notification received');
    
    let notificationData = {
        title: 'AWOT Trading',
        body: 'New update available',
        icon: '/icons/icon-192.png',
        badge: '/icons/icon-72.png',
        tag: 'awot-notification',
        requireInteraction: false,
        actions: [
            {
                action: 'open',
                title: 'Open App'
            },
            {
                action: 'dismiss',
                title: 'Dismiss'
            }
        ]
    };
    
    if (event.data) {
        try {
            const data = event.data.json();
            notificationData = { ...notificationData, ...data };
        } catch (error) {
            console.error('Error parsing push data:', error);
        }
    }
    
    event.waitUntil(
        self.registration.showNotification(notificationData.title, notificationData)
    );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
    console.log('🔔 Notification clicked:', event.action);
    
    event.notification.close();
    
    if (event.action === 'open' || !event.action) {
        // Open or focus the app
        event.waitUntil(
            clients.matchAll({ type: 'window' }).then((clientList) => {
                // Check if app is already open
                for (const client of clientList) {
                    if (client.url.includes(self.location.origin) && 'focus' in client) {
                        return client.focus();
                    }
                }
                
                // Open new window if app is not open
                if (clients.openWindow) {
                    return clients.openWindow('/');
                }
            })
        );
    }
});

// Handle background sync
self.addEventListener('sync', (event) => {
    console.log('🔄 Background sync triggered:', event.tag);
    
    if (event.tag === 'portfolio-sync') {
        event.waitUntil(syncPortfolioData());
    }
});

// Sync portfolio data when back online
async function syncPortfolioData() {
    try {
        console.log('📊 Syncing portfolio data...');
        
        // Fetch latest data
        const portfolioResponse = await fetch('/mobile/portfolio');
        const signalsResponse = await fetch('/mobile/signals');
        
        if (portfolioResponse.ok && signalsResponse.ok) {
            // Cache the fresh data
            const cache = await caches.open(API_CACHE_NAME);
            cache.put('/mobile/portfolio', portfolioResponse.clone());
            cache.put('/mobile/signals', signalsResponse.clone());
            
            console.log('✅ Portfolio data synced');
            
            // Notify clients about the update
            const clients = await self.clients.matchAll();
            clients.forEach(client => {
                client.postMessage({
                    type: 'data-synced',
                    timestamp: new Date().toISOString()
                });
            });
        }
    } catch (error) {
        console.error('❌ Failed to sync portfolio data:', error);
    }
}

// Handle messages from the main app
self.addEventListener('message', (event) => {
    console.log('📨 Message received:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// Periodic background sync (if supported)
if ('periodicSync' in self.registration) {
    self.addEventListener('periodicsync', (event) => {
        if (event.tag === 'portfolio-update') {
            event.waitUntil(syncPortfolioData());
        }
    });
}

console.log('🚀 AWOT Trading Service Worker loaded');
