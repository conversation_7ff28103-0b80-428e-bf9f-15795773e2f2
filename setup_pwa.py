#!/usr/bin/env python3
"""
AWOT Trading PWA Setup Script
Complete setup and deployment of the Progressive Web App
"""

import os
import sys
import subprocess
import time
import threading
import webbrowser
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_step(step, description):
    """Print a formatted step"""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def run_command(command, description, cwd=None):
    """Run a command and return success status"""
    print(f"⚡ {description}...")
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} error: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    print_step(1, "Checking Dependencies")
    
    dependencies = {
        'python3': 'Python 3 interpreter',
        'pip': 'Python package installer'
    }
    
    missing = []
    for cmd, desc in dependencies.items():
        if not run_command(f"which {cmd}", f"Checking {desc}"):
            missing.append(desc)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        return False
    
    print("\n✅ All dependencies available")
    return True

def setup_mobile_api():
    """Setup and start the mobile API server"""
    print_step(2, "Setting up Mobile API Server")
    
    # Install FastAPI dependencies
    if not run_command("pip install fastapi uvicorn websockets", "Installing FastAPI dependencies"):
        print("⚠️  FastAPI installation failed, but continuing...")
    
    # Check if AWOT directory exists
    awot_dir = Path.cwd()
    if not (awot_dir / "src").exists():
        print("❌ AWOT source directory not found")
        print("💡 Make sure you're running this from the AWOT directory")
        return False
    
    print("✅ AWOT directory found")
    return True

def create_pwa_files():
    """Ensure all PWA files are in place"""
    print_step(3, "Setting up PWA Files")
    
    pwa_dir = Path("pwa")
    
    # Check if PWA files exist
    required_files = ["index.html", "app.js", "sw.js", "manifest.json"]
    missing_files = []
    
    for file in required_files:
        if not (pwa_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing PWA files: {', '.join(missing_files)}")
        print("💡 Make sure all PWA files are created")
        return False
    
    print("✅ All PWA files found")
    
    # Create icons directory
    icons_dir = pwa_dir / "icons"
    icons_dir.mkdir(exist_ok=True)
    
    # Create simple icons
    create_simple_icons(icons_dir)
    
    return True

def create_simple_icons(icons_dir):
    """Create simple placeholder icons"""
    print("🎨 Creating app icons...")
    
    icon_sizes = [72, 96, 128, 144, 152, 192, 384, 512]
    
    try:
        # Try to use PIL for better icons
        from PIL import Image, ImageDraw
        
        for size in icon_sizes:
            icon_path = icons_dir / f"icon-{size}.png"
            if not icon_path.exists():
                img = Image.new('RGB', (size, size), '#007AFF')
                draw = ImageDraw.Draw(img)
                
                # Draw simple chart bars
                bar_width = size // 8
                for i in range(3):
                    x = size // 4 + i * (bar_width + size // 12)
                    height = size // 3 + i * size // 8
                    y = size - size // 6 - height
                    draw.rectangle([x, y, x + bar_width, size - size // 6], fill='white')
                
                img.save(icon_path)
        
        print("✅ Created PNG icons with PIL")
        
    except ImportError:
        # Fallback to SVG icons
        for size in icon_sizes:
            icon_path = icons_dir / f"icon-{size}.svg"
            if not icon_path.exists():
                svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{size}" height="{size}" viewBox="0 0 {size} {size}" xmlns="http://www.w3.org/2000/svg">
    <rect width="{size}" height="{size}" fill="#007AFF"/>
    <rect x="{size//4}" y="{size//2}" width="{size//4}" height="{size//4}" fill="white"/>
    <rect x="{size//2}" y="{size//3}" width="{size//4}" height="{size*5//12}" fill="white"/>
    <text x="{size//2}" y="{size*7//8}" text-anchor="middle" fill="white" font-family="Arial" font-size="{size//8}">AWOT</text>
</svg>'''
                
                with open(icon_path, 'w') as f:
                    f.write(svg_content)
        
        print("✅ Created SVG icons as fallback")

def start_api_server():
    """Start the AWOT API server in background"""
    print_step(4, "Starting AWOT API Server")
    
    # Create API start script
    api_script = '''
import uvicorn
import sys
import os
sys.path.append('src')

try:
    from api.main import app
    print("🚀 Starting AWOT API with mobile endpoints...")
    print("📱 Mobile API: http://localhost:8080/mobile/")
    print("📊 API docs: http://localhost:8080/docs")
    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
except Exception as e:
    print(f"❌ Failed to start API: {e}")
    print("💡 Make sure you're in the AWOT directory")
'''
    
    # Write script to temporary file
    with open("start_api.py", "w") as f:
        f.write(api_script)
    
    # Start API server in background
    def run_api():
        subprocess.run([sys.executable, "start_api.py"], cwd=Path.cwd())
    
    api_thread = threading.Thread(target=run_api, daemon=True)
    api_thread.start()
    
    # Wait a moment for server to start
    print("⏳ Waiting for API server to start...")
    time.sleep(3)
    
    # Test API connection
    try:
        import urllib.request
        urllib.request.urlopen("http://localhost:8080/mobile/health", timeout=5)
        print("✅ API server started successfully")
        return True
    except:
        print("⚠️  API server may still be starting...")
        return True

def start_pwa_server():
    """Start the PWA server"""
    print_step(5, "Starting PWA Server")
    
    pwa_dir = Path("pwa")
    
    # Start PWA server
    def run_pwa():
        os.chdir(pwa_dir)
        subprocess.run([sys.executable, "serve_pwa.py"])
    
    pwa_thread = threading.Thread(target=run_pwa, daemon=True)
    pwa_thread.start()
    
    # Wait for PWA server to start
    print("⏳ Starting PWA server...")
    time.sleep(2)
    
    return True

def show_instructions():
    """Show final instructions"""
    print_step(6, "Setup Complete!")
    
    print("🎉 AWOT Trading PWA is ready!")
    print("\n📱 Access your app:")
    print("   🌐 Local: http://localhost:3000")
    print("   📱 Mobile: http://YOUR_IP:3000")
    print("\n📊 API endpoints:")
    print("   🔗 API: http://localhost:8080/mobile/")
    print("   📖 Docs: http://localhost:8080/docs")
    
    print("\n📱 Mobile Installation:")
    print("   📱 iPhone: Safari → Share → Add to Home Screen")
    print("   🤖 Android: Chrome → Menu → Add to Home Screen")
    print("   💻 Desktop: Install when prompted")
    
    print("\n🎯 Features Available:")
    print("   ✅ Real-time portfolio tracking")
    print("   ✅ AI trading signals with sentiment")
    print("   ✅ Emergency trading controls")
    print("   ✅ Offline functionality")
    print("   ✅ Push notifications")
    print("   ✅ Native app experience")
    
    print("\n🔧 Troubleshooting:")
    print("   🔄 Refresh browser if data doesn't load")
    print("   📡 Check network connection")
    print("   🔍 Check browser console for errors")
    
    # Try to open browser
    try:
        webbrowser.open("http://localhost:3000")
        print("\n🌐 Opened PWA in browser")
    except:
        print("\n💡 Please open http://localhost:3000 in your browser")

def main():
    """Main setup function"""
    print_header("AWOT Trading PWA Setup")
    
    print("🎯 This script will:")
    print("   1. Check dependencies")
    print("   2. Setup mobile API server")
    print("   3. Create PWA files and icons")
    print("   4. Start API server")
    print("   5. Start PWA server")
    print("   6. Open in browser")
    
    input("\n📱 Press Enter to continue...")
    
    # Run setup steps
    steps = [
        check_dependencies,
        setup_mobile_api,
        create_pwa_files,
        start_api_server,
        start_pwa_server
    ]
    
    for step_func in steps:
        if not step_func():
            print(f"\n❌ Setup failed at: {step_func.__name__}")
            print("💡 Please check the errors above and try again")
            return False
    
    # Show final instructions
    show_instructions()
    
    print("\n🎉 Setup complete! Your PWA is running.")
    print("🔄 Press Ctrl+C to stop the servers")
    
    # Keep script running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Servers stopped")
        
        # Cleanup
        if os.path.exists("start_api.py"):
            os.remove("start_api.py")
        
        print("✅ Cleanup complete")

if __name__ == "__main__":
    main()
