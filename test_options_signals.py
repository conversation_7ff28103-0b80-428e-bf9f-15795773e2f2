#!/usr/bin/env python3
'''
Test Options Signals Generation
This tests if the signal generator now creates proper options signals with strike prices
'''

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_options_signals():
    """Test if the signal generator creates proper options signals"""
    print("🎯 TESTING OPTIONS SIGNALS GENERATION")
    print("=" * 60)
    
    try:
        from signals.signal_generator import SignalGenerator
        
        # Force signal generation for testing
        os.environ['FORCE_SIGNAL_GENERATION'] = 'true'
        
        generator = SignalGenerator()
        
        # Test with a few symbols
        test_symbols = ['AAPL', 'TSLA', 'MSFT']
        
        for symbol in test_symbols:
            print(f"\n📊 Testing {symbol}:")
            
            try:
                signals = generator.generate_signals(symbol)
                
                if signals:
                    print(f"   ✅ Generated {len(signals)} signals")
                    
                    for i, signal in enumerate(signals[:3]):  # Show first 3 signals
                        print(f"\n   📈 Signal {i+1}:")
                        print(f"      Symbol: {signal.symbol}")
                        print(f"      Type: {signal.signal_type.value}")
                        print(f"      Option Type: {signal.option_type}")
                        print(f"      Strike Price: ${signal.strike_price}")
                        print(f"      Expiration: {signal.expiration_date}")
                        print(f"      Entry Price: ${signal.entry_price:.2f}")
                        print(f"      Confidence: {signal.confidence:.2%}")
                        
                        # Check if it's a proper options signal
                        if signal.option_type and signal.strike_price:
                            print(f"      ✅ PROPER OPTIONS SIGNAL!")
                        else:
                            print(f"      ❌ Missing options data")
                            print(f"         option_type: {signal.option_type}")
                            print(f"         strike_price: {signal.strike_price}")
                else:
                    print(f"   ⚠️ No signals generated for {symbol}")
                    
            except Exception as e:
                print(f"   ❌ Error generating signals for {symbol}: {e}")
        
        print("\n🎯 TESTING STRIKE PRICE CALCULATION:")
        
        # Test the strike price calculation method directly
        test_prices = [100, 200, 50, 500]
        
        for price in test_prices:
            call_strike = generator._calculate_weekly_strike_price(price, 'call')
            put_strike = generator._calculate_weekly_strike_price(price, 'put')
            
            print(f"   Stock @ ${price}: Call Strike ${call_strike}, Put Strike ${put_strike}")
        
        print("\n🎉 OPTIONS SIGNAL TESTING COMPLETE!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in options signal testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_options_signals()
    if success:
        print("\n✅ SUCCESS: Options signals are now properly configured!")
        print("🎯 Your AWOT system should now trade weekly options instead of stocks!")
    else:
        print("\n❌ FAILED: Options signals need more work")
