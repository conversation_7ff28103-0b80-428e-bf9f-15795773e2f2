"""
AWOT Trading Platform Production API
FastAPI-based REST API for production deployment
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
import uvicorn
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional
from pydantic import BaseModel
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from trading.live_trading_engine import LiveTradingEngine, TradingMode, TradingState
from trading.paper_trading import PaperTradingEngine
from notifications.notification_manager import NotificationManager
from risk.risk_manager import RiskManager

# Import mobile endpoints
try:
    from .mobile_endpoints import mobile_router
    MOBILE_ENDPOINTS_AVAILABLE = True
except ImportError:
    MOBILE_ENDPOINTS_AVAILABLE = False
    logger.warning("Mobile endpoints not available")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="AWOT Trading Platform API",
    description="Advanced Options Trading Platform REST API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include mobile router if available
if MOBILE_ENDPOINTS_AVAILABLE:
    app.include_router(mobile_router)
    logger.info("Mobile endpoints enabled")
else:
    logger.warning("Mobile endpoints disabled - check mobile_endpoints.py")

# Security
security = HTTPBearer()

# Global instances
trading_engine: Optional[LiveTradingEngine] = None
paper_engine: Optional[PaperTradingEngine] = None
notification_manager: Optional[NotificationManager] = None
risk_manager: Optional[RiskManager] = None

# Pydantic models
class TradingEngineStatus(BaseModel):
    state: str
    trading_mode: str
    active_strategies: List[str]
    current_positions: int
    daily_trades: int
    uptime: float
    stats: Dict

class OrderRequest(BaseModel):
    symbol: str
    quantity: int
    side: str
    order_type: str = "market"
    price: Optional[float] = None
    strategy_name: str = ""

class OptionOrderRequest(OrderRequest):
    strike_price: float
    expiration_date: str
    option_type: str

class ConfigUpdate(BaseModel):
    max_positions: Optional[int] = None
    risk_per_trade: Optional[float] = None
    max_daily_trades: Optional[int] = None

class NotificationRequest(BaseModel):
    title: str
    message: str
    priority: str = "medium"
    notification_type: str = "system_status"

# Dependency injection
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Simple authentication - replace with proper auth in production"""
    # In production, validate JWT token here
    return {"user_id": "admin", "permissions": ["all"]}

# Startup/shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize components on startup"""
    global trading_engine, paper_engine, notification_manager, risk_manager
    
    logger.info("🚀 Starting AWOT Trading Platform API...")
    
    # Initialize components
    notification_manager = NotificationManager()
    risk_manager = RiskManager()
    paper_engine = PaperTradingEngine()
    
    logger.info("✅ API components initialized")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global trading_engine
    
    logger.info("🛑 Shutting down AWOT Trading Platform API...")
    
    if trading_engine:
        trading_engine.stop()
    
    logger.info("✅ API shutdown complete")

# Health check endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    # Return Prometheus-formatted metrics
    metrics_data = []
    
    if trading_engine:
        status = trading_engine.get_status()
        metrics_data.extend([
            f"awot_trading_engine_uptime {status['stats']['uptime']}",
            f"awot_signals_generated_total {status['stats']['signals_generated']}",
            f"awot_signals_executed_total {status['stats']['signals_executed']}",
            f"awot_daily_trades {status['daily_trades']}",
            f"awot_current_positions {status['current_positions']}"
        ])
    
    if paper_engine:
        summary = paper_engine.get_portfolio_summary()
        metrics_data.extend([
            f"awot_portfolio_value {summary['portfolio_value']}",
            f"awot_total_pnl {summary['total_pnl']}",
            f"awot_open_positions {summary['open_positions']}"
        ])
    
    return "\n".join(metrics_data)

# Trading engine endpoints
@app.post("/trading/start")
async def start_trading_engine(
    trading_mode: str = "paper",
    current_user: dict = Depends(get_current_user)
):
    """Start the trading engine"""
    global trading_engine
    
    try:
        mode = TradingMode.PAPER if trading_mode.lower() == "paper" else TradingMode.LIVE
        trading_engine = LiveTradingEngine(mode)
        
        if trading_engine.start():
            logger.info(f"Trading engine started in {mode.value} mode")
            return {"status": "success", "message": f"Trading engine started in {mode.value} mode"}
        else:
            raise HTTPException(status_code=500, detail="Failed to start trading engine")
    
    except Exception as e:
        logger.error(f"Error starting trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/trading/stop")
async def stop_trading_engine(current_user: dict = Depends(get_current_user)):
    """Stop the trading engine"""
    global trading_engine
    
    try:
        if trading_engine:
            trading_engine.stop()
            logger.info("Trading engine stopped")
            return {"status": "success", "message": "Trading engine stopped"}
        else:
            raise HTTPException(status_code=400, detail="Trading engine not running")
    
    except Exception as e:
        logger.error(f"Error stopping trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/trading/pause")
async def pause_trading_engine(current_user: dict = Depends(get_current_user)):
    """Pause the trading engine"""
    global trading_engine
    
    try:
        if trading_engine:
            trading_engine.pause()
            logger.info("Trading engine paused")
            return {"status": "success", "message": "Trading engine paused"}
        else:
            raise HTTPException(status_code=400, detail="Trading engine not running")
    
    except Exception as e:
        logger.error(f"Error pausing trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/trading/resume")
async def resume_trading_engine(current_user: dict = Depends(get_current_user)):
    """Resume the trading engine"""
    global trading_engine
    
    try:
        if trading_engine:
            trading_engine.resume()
            logger.info("Trading engine resumed")
            return {"status": "success", "message": "Trading engine resumed"}
        else:
            raise HTTPException(status_code=400, detail="Trading engine not running")
    
    except Exception as e:
        logger.error(f"Error resuming trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/trading/emergency-stop")
async def emergency_stop(current_user: dict = Depends(get_current_user)):
    """Emergency stop - cancel all orders and close positions"""
    global trading_engine
    
    try:
        if trading_engine:
            trading_engine.emergency_stop()
            logger.warning("Emergency stop activated")
            return {"status": "success", "message": "Emergency stop activated"}
        else:
            raise HTTPException(status_code=400, detail="Trading engine not running")
    
    except Exception as e:
        logger.error(f"Error during emergency stop: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/trading/status", response_model=TradingEngineStatus)
async def get_trading_status():
    """Get trading engine status"""
    global trading_engine
    
    if trading_engine:
        status = trading_engine.get_status()
        return TradingEngineStatus(**status)
    else:
        return TradingEngineStatus(
            state="stopped",
            trading_mode="none",
            active_strategies=[],
            current_positions=0,
            daily_trades=0,
            uptime=0.0,
            stats={}
        )

@app.put("/trading/config")
async def update_trading_config(
    config: ConfigUpdate,
    current_user: dict = Depends(get_current_user)
):
    """Update trading configuration"""
    global trading_engine
    
    try:
        if trading_engine:
            config_dict = config.dict(exclude_unset=True)
            trading_engine.update_config(config_dict)
            logger.info(f"Trading config updated: {config_dict}")
            return {"status": "success", "message": "Configuration updated", "config": config_dict}
        else:
            raise HTTPException(status_code=400, detail="Trading engine not running")
    
    except Exception as e:
        logger.error(f"Error updating config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Strategy management endpoints
@app.post("/strategies/{strategy_name}/enable")
async def enable_strategy(
    strategy_name: str,
    current_user: dict = Depends(get_current_user)
):
    """Enable a trading strategy"""
    global trading_engine
    
    try:
        if trading_engine:
            trading_engine.enable_strategy(strategy_name)
            logger.info(f"Strategy enabled: {strategy_name}")
            return {"status": "success", "message": f"Strategy {strategy_name} enabled"}
        else:
            raise HTTPException(status_code=400, detail="Trading engine not running")
    
    except Exception as e:
        logger.error(f"Error enabling strategy: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/strategies/{strategy_name}/disable")
async def disable_strategy(
    strategy_name: str,
    current_user: dict = Depends(get_current_user)
):
    """Disable a trading strategy"""
    global trading_engine
    
    try:
        if trading_engine:
            trading_engine.disable_strategy(strategy_name)
            logger.info(f"Strategy disabled: {strategy_name}")
            return {"status": "success", "message": f"Strategy {strategy_name} disabled"}
        else:
            raise HTTPException(status_code=400, detail="Trading engine not running")
    
    except Exception as e:
        logger.error(f"Error disabling strategy: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Portfolio endpoints
@app.get("/portfolio/summary")
async def get_portfolio_summary():
    """Get portfolio summary"""
    global paper_engine
    
    try:
        if paper_engine:
            summary = paper_engine.get_portfolio_summary()
            return summary
        else:
            raise HTTPException(status_code=500, detail="Paper engine not available")
    
    except Exception as e:
        logger.error(f"Error getting portfolio summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/portfolio/positions")
async def get_positions():
    """Get current positions"""
    global paper_engine
    
    try:
        if paper_engine:
            positions = paper_engine.get_positions()
            return {"positions": positions}
        else:
            raise HTTPException(status_code=500, detail="Paper engine not available")
    
    except Exception as e:
        logger.error(f"Error getting positions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/portfolio/trades")
async def get_trade_history():
    """Get trade history"""
    global paper_engine
    
    try:
        if paper_engine:
            trades = paper_engine.get_trade_history()
            return {"trades": trades}
        else:
            raise HTTPException(status_code=500, detail="Paper engine not available")
    
    except Exception as e:
        logger.error(f"Error getting trade history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Notification endpoints
@app.post("/notifications/send")
async def send_notification(
    notification: NotificationRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Send a notification"""
    global notification_manager
    
    try:
        if notification_manager:
            # Send notification in background
            background_tasks.add_task(
                notification_manager.send_notification,
                notification.notification_type,
                notification.title,
                notification.message,
                notification.priority
            )
            return {"status": "success", "message": "Notification queued"}
        else:
            raise HTTPException(status_code=500, detail="Notification manager not available")
    
    except Exception as e:
        logger.error(f"Error sending notification: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/notifications/history")
async def get_notification_history():
    """Get notification history"""
    global notification_manager
    
    try:
        if notification_manager:
            history = notification_manager.get_notification_history()
            return {"notifications": history}
        else:
            raise HTTPException(status_code=500, detail="Notification manager not available")
    
    except Exception as e:
        logger.error(f"Error getting notification history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Alert webhook endpoint
@app.post("/alerts/webhook")
async def alert_webhook(alert_data: dict):
    """Webhook endpoint for external alerts"""
    logger.info(f"Received alert webhook: {alert_data}")
    
    # Process alert and potentially trigger actions
    if notification_manager:
        notification_manager.send_notification(
            "system_alert",
            "External Alert",
            f"Alert received: {alert_data.get('message', 'Unknown alert')}",
            "high"
        )
    
    return {"status": "received"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8080,
        reload=False,
        log_level="info"
    )
