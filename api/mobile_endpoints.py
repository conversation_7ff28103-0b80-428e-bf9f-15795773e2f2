#!/usr/bin/env python3
"""
AWOT Mobile API Endpoints
Optimized API endpoints for iOS app integration
"""

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import asyncio
import json
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from trading.live_trading_engine import LiveTradingEngine, TradingMode
from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide
from signals.enhanced_signal_generator import EnhancedSignalGenerator
from sentiment.news_sentiment_analyzer import NewsSentimentAnalyzer

# Create router for mobile endpoints
mobile_router = APIRouter(prefix="/mobile", tags=["mobile"])

# Pydantic models for mobile API
class MobilePortfolio(BaseModel):
    portfolio_value: float
    daily_pnl: float
    daily_pnl_percent: float
    available_cash: float
    open_positions: int
    todays_trades: int
    win_rate: Optional[float]
    total_return: float
    total_return_percent: float
    last_updated: str

class MobilePosition(BaseModel):
    symbol: str
    quantity: int
    avg_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    day_change: float
    day_change_percent: float

class MobileSignal(BaseModel):
    id: str
    symbol: str
    signal_type: str
    strength: str
    confidence: float
    entry_price: float
    target_price: float
    stop_loss_price: float
    sentiment: str
    sentiment_score: float
    reasoning: str
    created_at: str
    expires_at: str
    risk_reward_ratio: float

class MobileOrder(BaseModel):
    symbol: str
    quantity: int
    side: str  # "buy" or "sell"
    order_type: str  # "market" or "limit"
    price: Optional[float] = None

class MobileOrderResponse(BaseModel):
    success: bool
    message: str
    order_id: Optional[str] = None

class MobileEngineStatus(BaseModel):
    state: str
    trading_mode: str
    active_strategies: List[str]
    uptime: str
    last_signal_time: Optional[str]
    orders_today: int
    performance_today: float

# Global variables for WebSocket connections
connected_clients: List[WebSocket] = []

# Initialize services
signal_generator = EnhancedSignalGenerator()
sentiment_analyzer = NewsSentimentAnalyzer()

@mobile_router.get("/portfolio", response_model=MobilePortfolio)
async def get_mobile_portfolio():
    """Get optimized portfolio data for mobile app"""
    try:
        # Get data from paper trading engine (or live engine based on mode)
        paper_engine = PaperTradingEngine()
        portfolio_summary = paper_engine.get_portfolio_summary()
        
        # Calculate additional metrics
        total_return = portfolio_summary['portfolio_value'] - 50000  # Assuming $50k start
        total_return_percent = (total_return / 50000) * 100
        
        return MobilePortfolio(
            portfolio_value=portfolio_summary['portfolio_value'],
            daily_pnl=portfolio_summary.get('daily_pnl', 0),
            daily_pnl_percent=portfolio_summary.get('daily_pnl_percent', 0),
            available_cash=portfolio_summary['cash'],
            open_positions=portfolio_summary['open_positions'],
            todays_trades=portfolio_summary.get('todays_trades', 0),
            win_rate=portfolio_summary.get('win_rate'),
            total_return=total_return,
            total_return_percent=total_return_percent,
            last_updated=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching portfolio: {str(e)}")

@mobile_router.get("/positions", response_model=List[MobilePosition])
async def get_mobile_positions():
    """Get optimized positions data for mobile app"""
    try:
        paper_engine = PaperTradingEngine()
        positions = paper_engine.get_positions()
        
        mobile_positions = []
        for pos in positions:
            market_value = pos['quantity'] * pos['current_price']
            unrealized_pnl = pos['unrealized_pnl']
            unrealized_pnl_percent = (unrealized_pnl / (pos['quantity'] * pos['entry_price'])) * 100 if pos['entry_price'] > 0 else 0
            
            mobile_positions.append(MobilePosition(
                symbol=pos['symbol'],
                quantity=pos['quantity'],
                avg_price=pos['entry_price'],
                current_price=pos['current_price'],
                market_value=market_value,
                unrealized_pnl=unrealized_pnl,
                unrealized_pnl_percent=unrealized_pnl_percent,
                day_change=pos.get('day_change', 0),
                day_change_percent=pos.get('day_change_percent', 0)
            ))
        
        return mobile_positions
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching positions: {str(e)}")

@mobile_router.get("/signals", response_model=List[MobileSignal])
async def get_mobile_signals():
    """Get optimized signals data for mobile app"""
    try:
        # Get signals for popular symbols
        symbols = ['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'NVDA', 'SPY', 'QQQ']
        all_signals = []
        
        for symbol in symbols:
            try:
                # Generate signals
                signals = signal_generator.generate_signals(symbol)
                
                # Get sentiment for the symbol
                sentiment = sentiment_analyzer.analyze_sentiment(symbol, hours_back=12)
                
                for signal in signals:
                    # Calculate risk/reward ratio
                    risk = abs(signal.entry_price - signal.stop_loss_price)
                    reward = abs(signal.target_price - signal.entry_price)
                    risk_reward_ratio = reward / risk if risk > 0 else 0
                    
                    mobile_signal = MobileSignal(
                        id=f"{signal.symbol}_{signal.signal_type.value}_{int(datetime.now().timestamp())}",
                        symbol=signal.symbol,
                        signal_type=signal.signal_type.value,
                        strength=signal.strength.name,
                        confidence=signal.confidence,
                        entry_price=signal.entry_price,
                        target_price=signal.target_price,
                        stop_loss_price=signal.stop_loss_price,
                        sentiment=sentiment.overall_sentiment.name.lower(),
                        sentiment_score=sentiment.sentiment_score,
                        reasoning=signal.reasoning,
                        created_at=datetime.now().isoformat(),
                        expires_at=(datetime.now() + timedelta(hours=4)).isoformat(),
                        risk_reward_ratio=risk_reward_ratio
                    )
                    
                    all_signals.append(mobile_signal)
                    
            except Exception as e:
                print(f"Error generating signals for {symbol}: {e}")
                continue
        
        # Sort by confidence and return top 10
        sorted_signals = sorted(all_signals, key=lambda x: x.confidence, reverse=True)
        return sorted_signals[:10]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching signals: {str(e)}")

@mobile_router.post("/order", response_model=MobileOrderResponse)
async def place_mobile_order(order: MobileOrder):
    """Place order from mobile app"""
    try:
        paper_engine = PaperTradingEngine()
        
        # Convert side to OrderSide enum
        side = OrderSide.BUY if order.side.lower() == 'buy' else OrderSide.SELL
        
        # Place the order
        result = paper_engine.place_order(
            symbol=order.symbol,
            quantity=order.quantity,
            side=side,
            price=order.price or 0,  # Use market price if no price specified
            order_type='stock'
        )
        
        # Notify connected mobile clients
        if result.get('success'):
            await broadcast_to_mobile_clients({
                "type": "order_executed",
                "data": {
                    "symbol": order.symbol,
                    "quantity": order.quantity,
                    "side": order.side,
                    "success": True
                }
            })
        
        return MobileOrderResponse(
            success=result.get('success', False),
            message=result.get('message', 'Order processed'),
            order_id=result.get('trade_id')
        )
        
    except Exception as e:
        return MobileOrderResponse(
            success=False,
            message=f"Error placing order: {str(e)}"
        )

@mobile_router.post("/emergency-stop", response_model=Dict[str, Any])
async def mobile_emergency_stop():
    """Emergency stop from mobile app"""
    try:
        # Stop any running trading engines
        # This would integrate with your live trading engine
        
        # Notify all connected clients
        await broadcast_to_mobile_clients({
            "type": "emergency_stop",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "message": "Emergency stop activated from mobile app"
            }
        })
        
        return {
            "success": True,
            "message": "Emergency stop activated successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing emergency stop: {str(e)}")

@mobile_router.get("/engine-status", response_model=MobileEngineStatus)
async def get_mobile_engine_status():
    """Get trading engine status for mobile app"""
    try:
        # This would integrate with your live trading engine
        # For now, return paper trading status
        
        return MobileEngineStatus(
            state="running",
            trading_mode="paper",
            active_strategies=["momentum", "breakout", "sentiment"],
            uptime="2h 15m",
            last_signal_time=datetime.now().isoformat(),
            orders_today=5,
            performance_today=2.4
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching engine status: {str(e)}")

@mobile_router.websocket("/ws")
async def mobile_websocket(websocket: WebSocket):
    """WebSocket endpoint for real-time mobile updates"""
    await websocket.accept()
    connected_clients.append(websocket)
    
    try:
        # Send initial data
        portfolio = await get_mobile_portfolio()
        await websocket.send_json({
            "type": "portfolio_update",
            "data": portfolio.dict()
        })
        
        # Keep connection alive and send periodic updates
        while True:
            try:
                # Send portfolio updates every 30 seconds
                await asyncio.sleep(30)
                
                portfolio = await get_mobile_portfolio()
                await websocket.send_json({
                    "type": "portfolio_update",
                    "data": portfolio.dict()
                })
                
                # Send price updates (mock data for now)
                await websocket.send_json({
                    "type": "price_update",
                    "data": {
                        "AAPL": 211.45,
                        "TSLA": 330.12,
                        "MSFT": 410.33,
                        "timestamp": datetime.now().isoformat()
                    }
                })
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                print(f"WebSocket error: {e}")
                break
                
    except WebSocketDisconnect:
        pass
    finally:
        if websocket in connected_clients:
            connected_clients.remove(websocket)

async def broadcast_to_mobile_clients(message: Dict[str, Any]):
    """Broadcast message to all connected mobile clients"""
    if not connected_clients:
        return
    
    disconnected_clients = []
    
    for client in connected_clients:
        try:
            await client.send_json(message)
        except Exception:
            disconnected_clients.append(client)
    
    # Remove disconnected clients
    for client in disconnected_clients:
        connected_clients.remove(client)

# Health check endpoint
@mobile_router.get("/health")
async def mobile_health_check():
    """Health check for mobile API"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "connected_clients": len(connected_clients),
        "version": "1.0.0"
    }
