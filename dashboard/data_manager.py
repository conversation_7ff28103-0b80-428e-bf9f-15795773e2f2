"""
Dashboard Data Manager
Handles data fetching, caching, and processing for the dashboard
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import streamlit as st
from typing import Dict, List, Optional
import sys
import os
import json

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from indicators.technical_indicators import TechnicalIndicators
    from signals.signal_generator import SignalGenerator, TradingSignal
    from strategies.strategy import MomentumStrategy, BreakoutStrategy
    from risk.risk_manager import RiskManager
    from data.market_data import MarketDataProvider
except ImportError:
    # Fallback for when modules aren't available
    pass

try:
    from dashboard.config import *
except ImportError:
    # Fallback configuration values
    DEFAULT_INITIAL_CAPITAL = 50000
    DEMO_MODE = True
    DEMO_SYMBOLS = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL', 'AMZN', 'META', 'NFLX']
    DEFAULT_MAX_POSITIONS = 3
    RISK_THRESHOLDS = {
        'portfolio_risk': 0.25,
        'position_concentration': 0.30,
        'drawdown_warning': 0.10,
        'drawdown_critical': 0.20,
        'time_decay_warning': 3,
        'volume_unusual': 2.0
    }
    PORTFOLIO_DATA_CACHE_TTL = type('obj', (object,), {'total_seconds': lambda: 30})()
    SIGNALS_CACHE_TTL = type('obj', (object,), {'total_seconds': lambda: 300})()
    MARKET_DATA_CACHE_TTL = type('obj', (object,), {'total_seconds': lambda: 60})()


class DashboardDataManager:
    """Manages data for the dashboard with caching and real-time updates"""
    
    def __init__(self):
        self.technical_indicators = None
        self.signal_generator = None
        self.risk_manager = None
        self.market_data = None
        self.strategies = {}
        
        # Initialize components if available
        try:
            self.technical_indicators = TechnicalIndicators()
            self.signal_generator = SignalGenerator()
            self.risk_manager = RiskManager()
            self.market_data = MarketDataProvider()
            
            self.strategies = {
                'momentum': MomentumStrategy(),
                'breakout': BreakoutStrategy()
            }
        except:
            # Use demo mode if components not available
            pass
    
    @st.cache_data(ttl=PORTFOLIO_DATA_CACHE_TTL.total_seconds())
    def get_portfolio_data(_self) -> Dict:
        """Get current portfolio data with caching"""
        if DEMO_MODE:
            return _self._generate_demo_portfolio_data()
        else:
            # TODO: Implement real portfolio data fetching
            return _self._generate_demo_portfolio_data()
    
    @st.cache_data(ttl=SIGNALS_CACHE_TTL.total_seconds())
    def get_trading_signals(_self, symbols: List[str] = None) -> List[Dict]:
        """Get current trading signals with caching"""
        if symbols is None:
            symbols = DEMO_SYMBOLS[:5]  # Limit to 5 symbols for demo
        
        if DEMO_MODE or not _self.signal_generator:
            return _self._generate_demo_signals(symbols)
        else:
            # TODO: Implement real signal generation
            return _self._generate_demo_signals(symbols)
    
    @st.cache_data(ttl=MARKET_DATA_CACHE_TTL.total_seconds())
    def get_market_data(_self, symbols: List[str] = None) -> Dict:
        """Get market data with caching"""
        if symbols is None:
            symbols = DEMO_SYMBOLS
        
        if DEMO_MODE or not _self.market_data:
            return _self._generate_demo_market_data(symbols)
        else:
            # TODO: Implement real market data fetching
            return _self._generate_demo_market_data(symbols)
    
    def get_risk_assessment(self, portfolio_data: Dict) -> Dict:
        """Get risk assessment for current portfolio"""
        if DEMO_MODE or not self.risk_manager:
            return self._generate_demo_risk_assessment(portfolio_data)
        else:
            # TODO: Implement real risk assessment
            return self._generate_demo_risk_assessment(portfolio_data)
    
    def _generate_demo_portfolio_data(self) -> Dict:
        """Generate realistic demo portfolio data"""
        # Simulate portfolio performance
        base_value = DEFAULT_INITIAL_CAPITAL
        days = 30
        
        # Generate realistic returns
        np.random.seed(int(datetime.now().timestamp()) % 1000)
        daily_returns = np.random.normal(0.001, 0.02, days)  # 0.1% daily return, 2% volatility
        portfolio_values = [base_value]
        
        for ret in daily_returns:
            new_value = portfolio_values[-1] * (1 + ret)
            portfolio_values.append(new_value)
        
        current_value = portfolio_values[-1]
        total_return = (current_value - base_value) / base_value
        daily_pnl = current_value - portfolio_values[-2]
        
        # Generate sample positions
        positions = [
            {
                'symbol': 'AAPL',
                'type': 'Call',
                'strike': 150.0,
                'expiry': (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
                'quantity': 10,
                'entry_price': 5.50,
                'current_price': 5.50 + np.random.normal(0, 0.5),
                'entry_time': datetime.now() - timedelta(hours=np.random.randint(1, 48))
            },
            {
                'symbol': 'TSLA',
                'type': 'Put',
                'strike': 200.0,
                'expiry': (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d'),
                'quantity': 5,
                'entry_price': 8.00,
                'current_price': 8.00 + np.random.normal(0, 1.0),
                'entry_time': datetime.now() - timedelta(hours=np.random.randint(1, 72))
            },
            {
                'symbol': 'MSFT',
                'type': 'Call',
                'strike': 375.0,
                'expiry': (datetime.now() + timedelta(days=21)).strftime('%Y-%m-%d'),
                'quantity': 8,
                'entry_price': 12.50,
                'current_price': 12.50 + np.random.normal(0, 1.5),
                'entry_time': datetime.now() - timedelta(hours=np.random.randint(1, 96))
            }
        ]
        
        # Calculate P&L for positions
        for position in positions:
            position['pnl'] = (position['current_price'] - position['entry_price']) * position['quantity'] * 100
            position['pnl_pct'] = (position['current_price'] - position['entry_price']) / position['entry_price'] * 100
        
        # Get real trades from paper trading engine
        recent_trades = self._get_real_trades()
        
        return {
            'total_value': current_value,
            'initial_capital': base_value,
            'total_return': total_return,
            'daily_pnl': daily_pnl,
            'positions': positions,
            'recent_trades': recent_trades,
            'portfolio_history': {
                'dates': [datetime.now() - timedelta(days=days-i) for i in range(len(portfolio_values))],
                'values': portfolio_values
            }
        }
    
    def _generate_demo_signals(self, symbols: List[str]) -> List[Dict]:
        """Generate realistic demo trading signals"""
        signals = []
        
        for symbol in symbols[:3]:  # Limit to 3 signals for demo
            if np.random.random() > 0.6:  # 40% chance of signal per symbol
                signal_types = ['Momentum Bullish', 'Breakout Bullish', 'Volatility Expansion', 'Reversal Bullish']
                strengths = ['Weak', 'Moderate', 'Strong', 'Very Strong']
                
                entry_price = np.random.uniform(100, 500)
                confidence = np.random.uniform(0.5, 0.95)
                
                # Calculate target and stop based on signal strength
                strength = np.random.choice(strengths, p=[0.1, 0.3, 0.4, 0.2])
                if strength in ['Strong', 'Very Strong']:
                    target_multiplier = np.random.uniform(1.15, 1.30)
                    stop_multiplier = np.random.uniform(0.85, 0.92)
                else:
                    target_multiplier = np.random.uniform(1.08, 1.20)
                    stop_multiplier = np.random.uniform(0.88, 0.95)
                
                signals.append({
                    'symbol': symbol,
                    'type': np.random.choice(signal_types),
                    'strength': strength,
                    'confidence': confidence,
                    'entry_price': entry_price,
                    'target_price': entry_price * target_multiplier,
                    'stop_loss': entry_price * stop_multiplier,
                    'timestamp': datetime.now() - timedelta(minutes=np.random.randint(1, 60)),
                    'risk_reward': (entry_price * target_multiplier - entry_price) / (entry_price - entry_price * stop_multiplier)
                })
        
        # Sort by timestamp (newest first)
        signals.sort(key=lambda x: x['timestamp'], reverse=True)
        return signals
    
    def _generate_demo_market_data(self, symbols: List[str]) -> Dict:
        """Generate demo market data"""
        market_data = {}
        
        # Overall market sentiment
        market_data['sentiment'] = {
            'vix_level': np.random.uniform(15, 25),
            'fear_greed_score': np.random.randint(20, 80),
            'spy_change': np.random.uniform(-2, 2),
            'volume_ratio': np.random.uniform(0.8, 1.5)
        }
        
        # Individual symbol data
        market_data['symbols'] = {}
        for symbol in symbols:
            base_price = np.random.uniform(50, 500)
            change_pct = np.random.uniform(-5, 5)
            
            market_data['symbols'][symbol] = {
                'price': base_price,
                'change_pct': change_pct,
                'volume_ratio': np.random.uniform(0.5, 3.0),
                'rsi': np.random.uniform(20, 80),
                'macd_signal': np.random.choice(['Bullish', 'Bearish']),
                'trend': 'Up' if change_pct > 0 else 'Down'
            }
        
        return market_data
    
    def _generate_demo_risk_assessment(self, portfolio_data: Dict) -> Dict:
        """Generate demo risk assessment"""
        total_value = portfolio_data['total_value']
        positions = portfolio_data['positions']
        
        # Calculate portfolio risk metrics
        position_values = [abs(pos['pnl']) for pos in positions]
        total_risk = sum(position_values)
        portfolio_risk = total_risk / total_value if total_value > 0 else 0
        
        # Calculate max drawdown (simplified)
        portfolio_history = portfolio_data.get('portfolio_history', {})
        if portfolio_history.get('values'):
            values = portfolio_history['values']
            peak = max(values)
            current = values[-1]
            max_drawdown = (peak - current) / peak if peak > 0 else 0
        else:
            max_drawdown = 0
        
        # Time decay risk
        time_decay_risk = 0
        for pos in positions:
            expiry = datetime.strptime(pos['expiry'], '%Y-%m-%d')
            days_to_expiry = (expiry - datetime.now()).days
            if days_to_expiry <= 3:
                time_decay_risk += 1
        
        time_decay_risk_pct = time_decay_risk / len(positions) if positions else 0
        
        # Generate alerts
        alerts = []
        if portfolio_risk > RISK_THRESHOLDS['portfolio_risk']:
            alerts.append("⚠️ Portfolio risk exceeds threshold")
        
        if max_drawdown > RISK_THRESHOLDS['drawdown_warning']:
            alerts.append("📉 Drawdown warning level reached")
        
        for pos in positions:
            expiry = datetime.strptime(pos['expiry'], '%Y-%m-%d')
            days_to_expiry = (expiry - datetime.now()).days
            if days_to_expiry <= RISK_THRESHOLDS['time_decay_warning']:
                alerts.append(f"⏰ {pos['symbol']} expires in {days_to_expiry} days")
        
        if not alerts:
            alerts.append("✅ All risk metrics within acceptable limits")
        
        return {
            'portfolio_risk': portfolio_risk,
            'max_drawdown': max_drawdown,
            'position_concentration': len(positions) / DEFAULT_MAX_POSITIONS,
            'time_decay_risk': time_decay_risk_pct,
            'alerts': alerts,
            'risk_score': min(100, (portfolio_risk + max_drawdown + time_decay_risk_pct) * 100 / 3)
        }
    
    def get_performance_metrics(self, portfolio_data: Dict) -> Dict:
        """Calculate performance metrics"""
        total_return = portfolio_data['total_return']
        daily_pnl = portfolio_data['daily_pnl']
        
        # Calculate additional metrics
        portfolio_history = portfolio_data.get('portfolio_history', {})
        if portfolio_history.get('values'):
            values = portfolio_history['values']
            returns = pd.Series(values).pct_change().dropna()
            
            # Sharpe ratio (simplified)
            if len(returns) > 1:
                sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            else:
                sharpe_ratio = 0
            
            # Win rate (simplified)
            winning_days = len(returns[returns > 0])
            total_days = len(returns)
            win_rate = winning_days / total_days if total_days > 0 else 0
        else:
            sharpe_ratio = 0
            win_rate = 0
        
        return {
            'total_return': total_return,
            'daily_pnl': daily_pnl,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'total_trades': len(portfolio_data.get('recent_trades', [])),
            'active_positions': len(portfolio_data.get('positions', []))
        }

    def _get_real_trades(self) -> List[Dict]:
        """Get real trades from paper trading engine"""
        try:
            # Try to load the paper trading state file
            portfolio_file = os.path.join(os.path.dirname(__file__), '..', 'current_portfolio_state.json')

            if os.path.exists(portfolio_file):
                with open(portfolio_file, 'r') as f:
                    portfolio_state = json.load(f)

                trade_history = portfolio_state.get('trade_history', [])

                # Convert to dashboard format
                recent_trades = []
                for trade in trade_history[-20:]:  # Get last 20 trades
                    recent_trades.append({
                        'timestamp': datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')),
                        'symbol': trade['symbol'],
                        'action': trade['side'].upper(),
                        'type': 'Stock',  # All our trades are stocks
                        'quantity': trade['quantity'],
                        'price': round(trade['price'], 2),
                        'status': 'Filled',
                        'trade_id': trade['trade_id']
                    })

                # Sort by timestamp (newest first)
                recent_trades.sort(key=lambda x: x['timestamp'], reverse=True)
                return recent_trades

        except Exception as e:
            print(f"Error loading real trades: {e}")

        # Fallback to empty list if can't load real trades
        return []
