"""
Dashboard Configuration
Settings and constants for the AWOT Streamlit dashboard
"""

import os
from datetime import timedelta

# Dashboard Settings
DASHBOARD_TITLE = "AWOT Trading Dashboard"
DASHBOARD_ICON = "📈"
AUTO_REFRESH_INTERVAL = 30000  # milliseconds
PAGE_LAYOUT = "wide"

# Trading Settings
DEFAULT_MAX_POSITIONS = 3
DEFAULT_RISK_PER_TRADE = 0.10  # 10%
DEFAULT_INITIAL_CAPITAL = 50000

# Chart Settings
CHART_HEIGHT = 400
CHART_COLORS = {
    'primary': '#1f77b4',
    'secondary': '#ff7f0e',
    'success': '#2ca02c',
    'danger': '#d62728',
    'warning': '#ff7f0e',
    'info': '#17a2b8'
}

# Data Refresh Settings
MARKET_DATA_CACHE_TTL = timedelta(minutes=1)
PORTFOLIO_DATA_CACHE_TTL = timedelta(seconds=30)
SIGNALS_CACHE_TTL = timedelta(minutes=5)

# Notification Settings
NOTIFICATION_TYPES = {
    'trade_executed': '💰',
    'signal_generated': '🎯',
    'risk_alert': '⚠️',
    'system_status': '🔧',
    'error': '❌'
}

# Risk Thresholds
RISK_THRESHOLDS = {
    'portfolio_risk': 0.25,  # 25%
    'position_concentration': 0.30,  # 30%
    'drawdown_warning': 0.10,  # 10%
    'drawdown_critical': 0.20,  # 20%
    'time_decay_warning': 3,  # days
    'volume_unusual': 2.0  # 2x normal volume
}

# Sample Data Settings (for demo mode)
DEMO_MODE = True
DEMO_SYMBOLS = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL', 'AMZN', 'META', 'NFLX']
DEMO_UPDATE_FREQUENCY = 30  # seconds

# API Settings
API_TIMEOUT = 30  # seconds
MAX_RETRIES = 3
RATE_LIMIT_DELAY = 1  # seconds between API calls

# File Paths
LOG_DIR = "logs"
DATA_DIR = "data"
BACKUP_DIR = "backups"

# Ensure directories exist
for directory in [LOG_DIR, DATA_DIR, BACKUP_DIR]:
    os.makedirs(directory, exist_ok=True)
