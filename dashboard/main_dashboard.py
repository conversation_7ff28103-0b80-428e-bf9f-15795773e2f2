#!/usr/bin/env python3
"""
AWOT - Automated Weekly Options Trading Platform
Main Streamlit Dashboard

This dashboard provides real-time monitoring and control for the AWOT trading system.
Features include portfolio monitoring, strategy management, signal analysis, and system controls.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import sys
import os
from streamlit_autorefresh import st_autorefresh

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from indicators.technical_indicators import TechnicalIndicators
    from signals.signal_generator import SignalGenerator, SignalType, SignalStrength
    from strategies.strategy import MomentumStrategy, BreakoutStrategy
    from risk.risk_manager import RiskManager
    from data.market_data import MarketDataProvider
    from notifications.notification_manager import NotificationManager, NotificationType, NotificationPriority
except ImportError as e:
    st.error(f"Import error: {e}")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="AWOT Trading Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .status-active {
        color: #28a745;
        font-weight: bold;
    }
    .status-inactive {
        color: #dc3545;
        font-weight: bold;
    }
    .signal-strong {
        background-color: #d4edda;
        color: #155724;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }
    .signal-weak {
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'trading_active' not in st.session_state:
    st.session_state.trading_active = False
if 'portfolio_data' not in st.session_state:
    st.session_state.portfolio_data = None
if 'last_update' not in st.session_state:
    st.session_state.last_update = datetime.now()

# Initialize components
@st.cache_resource
def initialize_components():
    """Initialize trading components"""
    try:
        indicators = TechnicalIndicators()
        signal_generator = SignalGenerator()
        momentum_strategy = MomentumStrategy()
        breakout_strategy = BreakoutStrategy()
        risk_manager = RiskManager()
        market_data = MarketDataProvider()
        notification_manager = NotificationManager()

        return {
            'indicators': indicators,
            'signal_generator': signal_generator,
            'momentum_strategy': momentum_strategy,
            'breakout_strategy': breakout_strategy,
            'risk_manager': risk_manager,
            'market_data': market_data,
            'notification_manager': notification_manager
        }
    except Exception as e:
        st.error(f"Failed to initialize components: {e}")
        return None

def create_sample_portfolio_data():
    """Create sample portfolio data for demonstration"""
    return {
        'total_value': 52750.00,
        'initial_capital': 50000.00,
        'total_return': 0.055,
        'daily_pnl': 1250.00,
        'positions': [
            {
                'symbol': 'AAPL',
                'type': 'Call',
                'strike': 150.0,
                'expiry': '2024-01-19',
                'quantity': 10,
                'entry_price': 5.50,
                'current_price': 6.25,
                'pnl': 750.00,
                'pnl_pct': 13.6
            },
            {
                'symbol': 'TSLA',
                'type': 'Put',
                'strike': 200.0,
                'expiry': '2024-01-26',
                'quantity': 5,
                'entry_price': 8.00,
                'current_price': 9.00,
                'pnl': 500.00,
                'pnl_pct': 12.5
            }
        ],
        'recent_trades': [
            {
                'timestamp': datetime.now() - timedelta(hours=2),
                'symbol': 'MSFT',
                'action': 'BUY',
                'type': 'Call',
                'quantity': 8,
                'price': 4.75,
                'status': 'Filled'
            },
            {
                'timestamp': datetime.now() - timedelta(hours=5),
                'symbol': 'NVDA',
                'action': 'SELL',
                'type': 'Call',
                'quantity': 12,
                'price': 7.20,
                'status': 'Filled'
            }
        ]
    }

def create_sample_signals():
    """Create sample trading signals for demonstration"""
    return [
        {
            'symbol': 'AAPL',
            'type': 'Momentum Bullish',
            'strength': 'Strong',
            'confidence': 0.85,
            'entry_price': 150.25,
            'target_price': 165.00,
            'stop_loss': 142.50,
            'timestamp': datetime.now() - timedelta(minutes=15)
        },
        {
            'symbol': 'TSLA',
            'type': 'Breakout Bullish',
            'strength': 'Very Strong',
            'confidence': 0.92,
            'entry_price': 205.50,
            'target_price': 230.00,
            'stop_loss': 195.00,
            'timestamp': datetime.now() - timedelta(minutes=8)
        },
        {
            'symbol': 'MSFT',
            'type': 'Volatility Expansion',
            'strength': 'Moderate',
            'confidence': 0.68,
            'entry_price': 375.00,
            'target_price': 390.00,
            'stop_loss': 365.00,
            'timestamp': datetime.now() - timedelta(minutes=3)
        }
    ]

def main_dashboard():
    """Main dashboard function"""
    
    # Auto-refresh every 30 seconds
    st_autorefresh(interval=30000, key="dashboard_refresh")
    
    # Header
    st.markdown('<h1 class="main-header">🚀 AWOT Trading Dashboard</h1>', unsafe_allow_html=True)
    
    # Initialize components
    components = initialize_components()
    if not components:
        st.error("Failed to initialize trading components")
        return
    
    # Sidebar controls
    with st.sidebar:
        st.header("🎛️ System Controls")
        
        # Trading status toggle
        trading_status = st.toggle(
            "Trading Active", 
            value=st.session_state.trading_active,
            help="Enable/disable automated trading"
        )
        st.session_state.trading_active = trading_status
        
        if trading_status:
            st.markdown('<p class="status-active">🟢 TRADING ACTIVE</p>', unsafe_allow_html=True)
        else:
            st.markdown('<p class="status-inactive">🔴 TRADING INACTIVE</p>', unsafe_allow_html=True)
        
        st.divider()
        
        # Strategy controls
        st.subheader("📊 Strategy Settings")
        
        momentum_enabled = st.checkbox("Momentum Strategy", value=True, key="momentum_enabled")
        breakout_enabled = st.checkbox("Breakout Strategy", value=True, key="breakout_enabled")
        volatility_enabled = st.checkbox("Volatility Strategy", value=False, key="volatility_enabled")
        
        st.divider()
        
        # Risk settings
        st.subheader("⚖️ Risk Management")
        
        max_positions = st.slider("Max Positions", 1, 10, 3)
        risk_per_trade = st.slider("Risk per Trade (%)", 1, 20, 10)
        
        st.divider()
        
        # Emergency controls
        st.subheader("🚨 Emergency Controls")
        
        if st.button("🛑 STOP ALL TRADING", type="primary"):
            st.session_state.trading_active = False
            st.success("Trading stopped!")
        
        if st.button("💰 CLOSE ALL POSITIONS", type="secondary"):
            st.warning("Position closure initiated!")
    
    # Main content area
    col1, col2, col3, col4 = st.columns(4)
    
    # Portfolio metrics
    portfolio_data = create_sample_portfolio_data()
    
    with col1:
        st.metric(
            "Portfolio Value",
            f"${portfolio_data['total_value']:,.2f}",
            f"${portfolio_data['daily_pnl']:,.2f}"
        )
    
    with col2:
        st.metric(
            "Total Return",
            f"{portfolio_data['total_return']:.1%}",
            f"+{(portfolio_data['total_value'] - portfolio_data['initial_capital']):,.2f}"
        )
    
    with col3:
        st.metric(
            "Active Positions",
            len(portfolio_data['positions']),
            f"Max: {max_positions}"
        )
    
    with col4:
        st.metric(
            "Risk Exposure",
            f"{risk_per_trade}%",
            "Per Trade"
        )
    
    # Main content tabs
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📊 Portfolio", "🎯 Signals", "📈 Market Analysis", "⚖️ Risk Monitor", "📋 Trade Log", "📱 Notifications"
    ])
    
    with tab1:
        portfolio_tab(portfolio_data)
    
    with tab2:
        signals_tab()
    
    with tab3:
        market_analysis_tab(components)
    
    with tab4:
        risk_monitor_tab(portfolio_data, components['risk_manager'])
    
    with tab5:
        trade_log_tab(portfolio_data)

    with tab6:
        notifications_tab(components['notification_manager'])

    with tab6:
        notifications_tab(components['notification_manager'])

def portfolio_tab(portfolio_data):
    """Portfolio monitoring tab"""
    st.header("📊 Portfolio Overview")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Portfolio performance chart
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
        portfolio_values = np.cumsum(np.random.normal(50, 500, len(dates))) + 50000
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=portfolio_values,
            mode='lines',
            name='Portfolio Value',
            line=dict(color='#1f77b4', width=3)
        ))
        
        fig.update_layout(
            title="Portfolio Performance (30 Days)",
            xaxis_title="Date",
            yaxis_title="Portfolio Value ($)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Current positions
        st.subheader("Current Positions")
        
        for position in portfolio_data['positions']:
            with st.container():
                st.markdown(f"**{position['symbol']} {position['type']}**")
                st.write(f"Strike: ${position['strike']}")
                st.write(f"Expiry: {position['expiry']}")
                st.write(f"Qty: {position['quantity']}")
                
                pnl_color = "green" if position['pnl'] > 0 else "red"
                st.markdown(f"P&L: <span style='color: {pnl_color}'>${position['pnl']:.2f} ({position['pnl_pct']:+.1f}%)</span>", 
                           unsafe_allow_html=True)
                st.divider()

def signals_tab():
    """Trading signals tab"""
    st.header("🎯 Trading Signals")
    
    signals = create_sample_signals()
    
    for signal in signals:
        with st.container():
            col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
            
            with col1:
                st.markdown(f"**{signal['symbol']}** - {signal['type']}")
                st.write(f"Entry: ${signal['entry_price']:.2f} | Target: ${signal['target_price']:.2f}")
            
            with col2:
                strength_class = "signal-strong" if signal['strength'] in ['Strong', 'Very Strong'] else "signal-weak"
                st.markdown(f'<span class="{strength_class}">{signal["strength"]}</span>', unsafe_allow_html=True)
            
            with col3:
                st.metric("Confidence", f"{signal['confidence']:.0%}")
            
            with col4:
                risk_reward = (signal['target_price'] - signal['entry_price']) / (signal['entry_price'] - signal['stop_loss'])
                st.metric("R:R", f"{risk_reward:.1f}:1")
            
            st.write(f"Generated: {signal['timestamp'].strftime('%H:%M:%S')}")
            st.divider()

def market_analysis_tab(components):
    """Market analysis tab"""
    st.header("📈 Market Analysis")
    
    # Sample market data
    symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL']
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Market Sentiment")
        
        sentiment_data = {
            'VIX Level': 18.5,
            'Fear/Greed Score': 65,
            'SPY Change': 0.8,
            'Volume Ratio': 1.2
        }
        
        for metric, value in sentiment_data.items():
            st.metric(metric, f"{value}")
    
    with col2:
        st.subheader("Top Movers")
        
        movers_data = pd.DataFrame({
            'Symbol': symbols,
            'Price': [150.25, 205.50, 375.00, 485.75, 2750.00],
            'Change %': [2.3, -1.8, 0.9, 4.2, -0.5],
            'Volume Ratio': [1.8, 2.3, 1.1, 3.2, 0.9]
        })
        
        st.dataframe(movers_data, use_container_width=True)

def risk_monitor_tab(portfolio_data, risk_manager):
    """Risk monitoring tab"""
    st.header("⚖️ Risk Monitor")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Risk Metrics")
        
        # Sample risk metrics
        risk_metrics = {
            'Portfolio Risk': '12.5%',
            'Max Drawdown': '3.2%',
            'Position Concentration': '67%',
            'Time Decay Risk': 'Low'
        }
        
        for metric, value in risk_metrics.items():
            st.metric(metric, value)
    
    with col2:
        st.subheader("Risk Alerts")
        
        alerts = [
            "⚠️ TSLA position expires in 2 days",
            "✅ Portfolio risk within limits",
            "📊 High volume detected in AAPL"
        ]
        
        for alert in alerts:
            st.write(alert)

def trade_log_tab(portfolio_data):
    """Trade log tab"""
    st.header("📋 Trade Log")
    
    trades_df = pd.DataFrame(portfolio_data['recent_trades'])
    
    if not trades_df.empty:
        trades_df['timestamp'] = trades_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
        st.dataframe(trades_df, use_container_width=True)
    else:
        st.info("No recent trades")

def notifications_tab(notification_manager):
    """Notifications management tab"""
    st.header("📱 Notifications")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("Notification Settings")

        # Notification preferences
        email_enabled = st.checkbox("Email Notifications", value=notification_manager.email_enabled, key="email_enabled")
        telegram_enabled = st.checkbox("Telegram Notifications", value=notification_manager.telegram_enabled, key="telegram_enabled")
        sms_enabled = st.checkbox("SMS Notifications", value=notification_manager.sms_enabled, key="sms_enabled")

        st.divider()

        # Notification types
        st.subheader("Notification Types")

        trade_notifications = st.checkbox("Trade Executions", value=True, key="trade_notifications")
        signal_notifications = st.checkbox("New Signals", value=True, key="signal_notifications")
        risk_notifications = st.checkbox("Risk Alerts", value=True, key="risk_notifications")
        portfolio_notifications = st.checkbox("Portfolio Updates", value=False, key="portfolio_notifications")

        st.divider()

        # Test notifications
        st.subheader("Test Notifications")

        if st.button("🧪 Test All Channels"):
            with st.spinner("Testing notification channels..."):
                results = notification_manager.test_notifications()

                for channel, success in results.items():
                    if success:
                        st.success(f"✅ {channel.title()} test successful")
                    else:
                        st.error(f"❌ {channel.title()} test failed")

        # Send sample notifications
        col_a, col_b, col_c = st.columns(3)

        with col_a:
            if st.button("📊 Test Portfolio Update"):
                sample_portfolio = {
                    'total_value': 52750.00,
                    'daily_pnl': 1250.00,
                    'total_return': 0.055,
                    'positions': []
                }
                notification_manager.send_portfolio_update(sample_portfolio)
                st.success("Portfolio update sent!")

        with col_b:
            if st.button("🎯 Test Signal Alert"):
                sample_signal = {
                    'symbol': 'AAPL',
                    'type': 'Momentum Bullish',
                    'confidence': 0.85,
                    'entry_price': 150.25,
                    'target_price': 165.00
                }
                notification_manager.send_signal_notification(sample_signal)
                st.success("Signal notification sent!")

        with col_c:
            if st.button("⚠️ Test Risk Alert"):
                sample_risk = {
                    'alert_type': 'Portfolio Risk',
                    'message': 'Portfolio risk exceeds 20% threshold',
                    'risk_level': 'high'
                }
                notification_manager.send_risk_alert(sample_risk)
                st.success("Risk alert sent!")

    with col2:
        st.subheader("Notification Status")

        # Service status
        services = [
            ("Email", notification_manager.email_enabled),
            ("Telegram", notification_manager.telegram_enabled),
            ("SMS", notification_manager.sms_enabled)
        ]

        for service, enabled in services:
            status_color = "green" if enabled else "red"
            status_text = "Enabled" if enabled else "Disabled"
            st.markdown(f"**{service}**: <span style='color: {status_color}'>{status_text}</span>",
                       unsafe_allow_html=True)

        st.divider()

        # Recent notifications
        st.subheader("Recent Notifications")

        history = notification_manager.get_notification_history(10)

        if history:
            for notification in history:
                with st.container():
                    st.write(f"**{notification['title']}**")
                    st.write(f"Type: {notification['type']}")
                    st.write(f"Time: {notification['timestamp'].strftime('%H:%M:%S')}")

                    status_color = "green" if notification['success'] else "red"
                    status_text = "Sent" if notification['success'] else "Failed"
                    st.markdown(f"Status: <span style='color: {status_color}'>{status_text}</span>",
                               unsafe_allow_html=True)
                    st.divider()
        else:
            st.info("No recent notifications")

if __name__ == "__main__":
    main_dashboard()
