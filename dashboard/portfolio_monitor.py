"""
Advanced Portfolio Monitoring Interface
Real-time portfolio tracking with P&L, positions, and performance metrics
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
from typing import Dict, List
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from strategies.strategy import Position, PositionType
    from risk.risk_manager import RiskManager
except ImportError:
    pass


class PortfolioMonitor:
    """Advanced portfolio monitoring with real-time updates and analytics"""
    
    def __init__(self):
        self.risk_manager = RiskManager() if 'RiskManager' in globals() else None
    
    def render_portfolio_overview(self, portfolio_data: Dict):
        """Render comprehensive portfolio overview"""
        
        # Key metrics row
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            total_value = portfolio_data.get('total_value', 0)
            daily_pnl = portfolio_data.get('daily_pnl', 0)
            st.metric(
                "Portfolio Value",
                f"${total_value:,.2f}",
                f"${daily_pnl:+,.2f}",
                delta_color="normal"
            )
        
        with col2:
            total_return = portfolio_data.get('total_return', 0)
            initial_capital = portfolio_data.get('initial_capital', 50000)
            absolute_gain = total_value - initial_capital
            st.metric(
                "Total Return",
                f"{total_return:.2%}",
                f"${absolute_gain:+,.2f}",
                delta_color="normal"
            )
        
        with col3:
            positions = portfolio_data.get('positions', [])
            open_positions = len([p for p in positions if p.get('pnl', 0) != 0])
            total_pnl = sum(p.get('pnl', 0) for p in positions)
            st.metric(
                "Open Positions",
                open_positions,
                f"${total_pnl:+,.2f} P&L",
                delta_color="normal"
            )
        
        with col4:
            # Calculate win rate
            winning_positions = len([p for p in positions if p.get('pnl', 0) > 0])
            win_rate = winning_positions / len(positions) if positions else 0
            avg_return = np.mean([p.get('pnl_pct', 0) for p in positions]) if positions else 0
            st.metric(
                "Win Rate",
                f"{win_rate:.1%}",
                f"{avg_return:+.1f}% Avg",
                delta_color="normal"
            )
        
        with col5:
            # Risk metrics
            if self.risk_manager:
                risk_assessment = self.risk_manager.get_risk_summary([], total_value)
                risk_score = risk_assessment.get('overall_risk_score', 0)
                risk_level = risk_assessment.get('overall_risk_level', 'LOW')
            else:
                risk_score = 25
                risk_level = 'LOW'
            
            st.metric(
                "Risk Score",
                f"{risk_score:.0f}/100",
                risk_level,
                delta_color="inverse"
            )
    
    def render_performance_charts(self, portfolio_data: Dict):
        """Render performance charts and analytics"""
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Portfolio value chart
            portfolio_history = portfolio_data.get('portfolio_history', {})
            if portfolio_history.get('dates') and portfolio_history.get('values'):
                dates = portfolio_history['dates']
                values = portfolio_history['values']
                
                fig = go.Figure()
                
                # Portfolio value line
                fig.add_trace(go.Scatter(
                    x=dates,
                    y=values,
                    mode='lines',
                    name='Portfolio Value',
                    line=dict(color='#1f77b4', width=3),
                    fill='tonexty'
                ))
                
                # Add initial capital line
                initial_capital = portfolio_data.get('initial_capital', 50000)
                fig.add_hline(
                    y=initial_capital,
                    line_dash="dash",
                    line_color="gray",
                    annotation_text="Initial Capital"
                )
                
                fig.update_layout(
                    title="Portfolio Performance",
                    xaxis_title="Date",
                    yaxis_title="Value ($)",
                    height=400,
                    showlegend=True
                )
                
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No portfolio history available")
        
        with col2:
            # Daily returns distribution
            if portfolio_history.get('values') and len(portfolio_history['values']) > 1:
                values = portfolio_history['values']
                returns = pd.Series(values).pct_change().dropna() * 100
                
                fig = go.Figure()
                fig.add_trace(go.Histogram(
                    x=returns,
                    nbinsx=20,
                    name='Daily Returns',
                    marker_color='lightblue',
                    opacity=0.7
                ))
                
                # Add mean line
                mean_return = returns.mean()
                fig.add_vline(
                    x=mean_return,
                    line_dash="dash",
                    line_color="red",
                    annotation_text=f"Mean: {mean_return:.2f}%"
                )
                
                fig.update_layout(
                    title="Daily Returns Distribution",
                    xaxis_title="Daily Return (%)",
                    yaxis_title="Frequency",
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("Insufficient data for returns analysis")
    
    def render_positions_table(self, portfolio_data: Dict):
        """Render detailed positions table"""
        
        st.subheader("📋 Current Positions")
        
        positions = portfolio_data.get('positions', [])
        
        if not positions:
            st.info("No open positions")
            return
        
        # Create positions DataFrame
        positions_df = pd.DataFrame(positions)
        
        # Format the DataFrame for display
        display_df = positions_df.copy()
        
        # Format currency columns
        currency_cols = ['entry_price', 'current_price', 'pnl']
        for col in currency_cols:
            if col in display_df.columns:
                display_df[col] = display_df[col].apply(lambda x: f"${x:.2f}")
        
        # Format percentage columns
        if 'pnl_pct' in display_df.columns:
            display_df['pnl_pct'] = display_df['pnl_pct'].apply(lambda x: f"{x:+.1f}%")
        
        # Rename columns for display
        column_mapping = {
            'symbol': 'Symbol',
            'type': 'Type',
            'strike': 'Strike',
            'expiry': 'Expiry',
            'quantity': 'Qty',
            'entry_price': 'Entry',
            'current_price': 'Current',
            'pnl': 'P&L',
            'pnl_pct': 'P&L %'
        }
        
        display_df = display_df.rename(columns=column_mapping)
        
        # Select columns to display
        display_columns = [col for col in column_mapping.values() if col in display_df.columns]
        display_df = display_df[display_columns]
        
        # Style the dataframe
        def style_pnl(val):
            """Style P&L values with colors"""
            if isinstance(val, str) and ('$' in val or '%' in val):
                # Extract numeric value
                numeric_val = float(val.replace('$', '').replace('%', '').replace('+', ''))
                if numeric_val > 0:
                    return 'color: green'
                elif numeric_val < 0:
                    return 'color: red'
            return ''
        
        # Apply styling
        styled_df = display_df.style.applymap(style_pnl, subset=['P&L', 'P&L %'])
        
        st.dataframe(styled_df, use_container_width=True)
        
        # Position actions
        st.subheader("Position Actions")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📊 Analyze Positions"):
                self._analyze_positions(positions)
        
        with col2:
            if st.button("⚠️ Check Risk"):
                self._check_position_risk(positions, portfolio_data.get('total_value', 0))
        
        with col3:
            if st.button("🔄 Refresh Prices"):
                st.success("Prices refreshed!")
                st.rerun()
    
    def _analyze_positions(self, positions: List[Dict]):
        """Analyze current positions"""
        if not positions:
            st.warning("No positions to analyze")
            return
        
        st.subheader("📊 Position Analysis")
        
        # Position distribution by symbol
        symbols = [p['symbol'] for p in positions]
        symbol_counts = pd.Series(symbols).value_counts()
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Symbol distribution pie chart
            fig = px.pie(
                values=symbol_counts.values,
                names=symbol_counts.index,
                title="Position Distribution by Symbol"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # P&L by position
            pnl_data = [(p['symbol'], p.get('pnl', 0)) for p in positions]
            pnl_df = pd.DataFrame(pnl_data, columns=['Symbol', 'P&L'])
            
            fig = px.bar(
                pnl_df,
                x='Symbol',
                y='P&L',
                title="P&L by Position",
                color='P&L',
                color_continuous_scale=['red', 'gray', 'green']
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Position statistics
        total_pnl = sum(p.get('pnl', 0) for p in positions)
        winning_positions = len([p for p in positions if p.get('pnl', 0) > 0])
        losing_positions = len([p for p in positions if p.get('pnl', 0) < 0])
        
        st.write("**Position Statistics:**")
        st.write(f"- Total P&L: ${total_pnl:+,.2f}")
        st.write(f"- Winning Positions: {winning_positions}")
        st.write(f"- Losing Positions: {losing_positions}")
        st.write(f"- Win Rate: {winning_positions / len(positions):.1%}")
    
    def _check_position_risk(self, positions: List[Dict], portfolio_value: float):
        """Check risk for current positions"""
        st.subheader("⚠️ Risk Analysis")
        
        if not self.risk_manager:
            st.warning("Risk manager not available")
            return
        
        # Convert to Position objects (simplified)
        position_objects = []
        for pos in positions:
            # This is a simplified conversion - in real implementation,
            # you'd properly convert the position data
            pass
        
        # For demo, show sample risk metrics
        st.write("**Risk Metrics:**")
        
        # Position concentration
        total_exposure = sum(abs(p.get('pnl', 0)) for p in positions)
        concentration = total_exposure / portfolio_value if portfolio_value > 0 else 0
        
        st.metric("Position Concentration", f"{concentration:.1%}")
        
        # Time decay risk
        expiring_soon = 0
        for pos in positions:
            try:
                expiry = datetime.strptime(pos.get('expiry', '2024-12-31'), '%Y-%m-%d')
                days_to_expiry = (expiry - datetime.now()).days
                if days_to_expiry <= 7:
                    expiring_soon += 1
            except:
                continue
        
        if expiring_soon > 0:
            st.warning(f"⏰ {expiring_soon} position(s) expiring within 7 days")
        else:
            st.success("✅ No positions expiring soon")
        
        # Portfolio risk score
        risk_score = min(100, concentration * 100 + expiring_soon * 20)
        
        if risk_score > 70:
            st.error(f"🔴 High Risk: {risk_score:.0f}/100")
        elif risk_score > 40:
            st.warning(f"🟡 Medium Risk: {risk_score:.0f}/100")
        else:
            st.success(f"🟢 Low Risk: {risk_score:.0f}/100")
    
    def render_trade_history(self, portfolio_data: Dict):
        """Render trade history and analytics"""
        
        st.subheader("📈 Trade History")
        
        recent_trades = portfolio_data.get('recent_trades', [])
        
        if not recent_trades:
            st.info("No recent trades")
            return
        
        # Create trades DataFrame
        trades_df = pd.DataFrame(recent_trades)
        
        # Format timestamp
        if 'timestamp' in trades_df.columns:
            trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])
            trades_df['Date'] = trades_df['timestamp'].dt.strftime('%Y-%m-%d')
            trades_df['Time'] = trades_df['timestamp'].dt.strftime('%H:%M:%S')
        
        # Format price
        if 'price' in trades_df.columns:
            trades_df['Price'] = trades_df['price'].apply(lambda x: f"${x:.2f}")
        
        # Select display columns
        display_columns = ['Date', 'Time', 'symbol', 'action', 'type', 'quantity', 'Price', 'status']
        display_df = trades_df[[col for col in display_columns if col in trades_df.columns]]
        
        # Rename columns
        column_mapping = {
            'symbol': 'Symbol',
            'action': 'Action',
            'type': 'Type',
            'quantity': 'Qty',
            'status': 'Status'
        }
        display_df = display_df.rename(columns=column_mapping)
        
        st.dataframe(display_df, use_container_width=True)
        
        # Trade statistics
        if len(recent_trades) > 0:
            st.subheader("📊 Trade Statistics")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                total_trades = len(recent_trades)
                filled_trades = len([t for t in recent_trades if t.get('status') == 'Filled'])
                fill_rate = filled_trades / total_trades if total_trades > 0 else 0
                st.metric("Fill Rate", f"{fill_rate:.1%}", f"{filled_trades}/{total_trades}")
            
            with col2:
                buy_trades = len([t for t in recent_trades if t.get('action') == 'BUY'])
                sell_trades = len([t for t in recent_trades if t.get('action') == 'SELL'])
                st.metric("Buy/Sell Ratio", f"{buy_trades}:{sell_trades}")
            
            with col3:
                avg_trade_size = np.mean([t.get('quantity', 0) for t in recent_trades])
                st.metric("Avg Trade Size", f"{avg_trade_size:.1f} contracts")
