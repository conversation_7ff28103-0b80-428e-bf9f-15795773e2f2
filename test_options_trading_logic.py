#!/usr/bin/env python3
'''
Test Options Trading Logic
This tests if the live trading engine will now execute options trades instead of stock trades
'''

import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_options_trading_logic():
    """Test if the trading logic will execute options trades"""
    print("🎯 TESTING OPTIONS TRADING LOGIC")
    print("=" * 60)
    
    try:
        from signals.signal_generator import TradingSignal, SignalType, SignalStrength
        from trading.robinhood_client import OrderSide, OptionType
        
        # Create a mock options signal (what the signal generator should now produce)
        print("📊 Creating mock options signal...")
        
        options_signal = TradingSignal(
            symbol='AAPL',
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.75,
            entry_price=213.50,  # Current stock price
            target_price=220.00,
            stop_loss_price=210.00,
            expiration_date=(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
            option_type='call',
            strike_price=215.00,  # 3% OTM call
            reasoning="Test weekly options signal"
        )
        
        print("✅ Mock options signal created:")
        print(f"   Symbol: {options_signal.symbol}")
        print(f"   Option Type: {options_signal.option_type}")
        print(f"   Strike Price: ${options_signal.strike_price}")
        print(f"   Expiration: {options_signal.expiration_date}")
        print(f"   Entry Price: ${options_signal.entry_price}")
        
        # Test the trading engine logic
        print("\n🔍 Testing trading engine decision logic...")
        
        # This is the key condition from live_trading_engine.py line 393
        if options_signal.option_type and options_signal.strike_price:
            print("✅ CONDITION MET: Will execute OPTIONS trade")
            print(f"   Option Type: {options_signal.option_type}")
            print(f"   Strike Price: ${options_signal.strike_price}")
            
            # Convert to OptionType enum
            option_type = OptionType.CALL if options_signal.option_type == 'call' else OptionType.PUT
            print(f"   Converted to: {option_type}")
            
            print("\n📈 Would execute:")
            print(f"   submit_option_order(")
            print(f"       symbol='{options_signal.symbol}',")
            print(f"       strike_price={options_signal.strike_price},")
            print(f"       expiration_date='{options_signal.expiration_date}',")
            print(f"       option_type={option_type},")
            print(f"       quantity=1,")
            print(f"       side=OrderSide.BUY,")
            print(f"       price={options_signal.entry_price}")
            print(f"   )")
            
        else:
            print("❌ CONDITION FAILED: Would execute STOCK trade instead")
            print(f"   option_type: {options_signal.option_type}")
            print(f"   strike_price: {options_signal.strike_price}")
        
        # Test with a stock signal (old behavior)
        print("\n📊 Testing with stock signal (old behavior)...")
        
        stock_signal = TradingSignal(
            symbol='AAPL',
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.75,
            entry_price=213.50,
            target_price=220.00,
            stop_loss_price=210.00,
            expiration_date=(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
            option_type=None,  # No option type
            strike_price=None,  # No strike price
            reasoning="Test stock signal"
        )
        
        if stock_signal.option_type and stock_signal.strike_price:
            print("❌ UNEXPECTED: Would execute options trade")
        else:
            print("✅ EXPECTED: Would execute STOCK trade")
            print(f"   submit_stock_order(")
            print(f"       symbol='{stock_signal.symbol}',")
            print(f"       quantity=100,")
            print(f"       side=OrderSide.BUY,")
            print(f"       price={stock_signal.entry_price}")
            print(f"   )")
        
        print("\n🎉 TRADING LOGIC TEST COMPLETE!")
        return True
        
    except Exception as e:
        print(f"❌ Error in trading logic test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_options_trading_logic()
    if success:
        print("\n✅ SUCCESS: Trading logic is ready for options!")
        print("🎯 When signals have option_type and strike_price, they will trade options!")
        print("📈 Your AWOT system is now configured for weekly options trading!")
    else:
        print("\n❌ FAILED: Trading logic needs more work")
