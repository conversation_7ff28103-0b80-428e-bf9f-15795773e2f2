#!/usr/bin/env python3
"""
Load Paper Trading Portfolio State
Loads the most recent saved portfolio state
"""

import sys
import os
import glob
from datetime import datetime

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from trading.paper_trading import PaperTradingEngine
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the AWOT directory")
    sys.exit(1)

def find_latest_portfolio_file():
    """Find the most recent portfolio save file"""
    # Look for demo portfolio files
    demo_files = glob.glob("demo_portfolio_*.json")
    
    # Look for any other portfolio files
    other_files = glob.glob("paper_state_*.json")
    
    all_files = demo_files + other_files
    
    if not all_files:
        return None
    
    # Sort by modification time, newest first
    all_files.sort(key=os.path.getmtime, reverse=True)
    return all_files[0]

def load_portfolio_state():
    """Load the most recent portfolio state"""
    print("📂 Loading Paper Trading Portfolio State")
    print("=" * 60)
    
    try:
        # Find the latest portfolio file
        latest_file = find_latest_portfolio_file()
        
        if not latest_file:
            print("❌ No saved portfolio files found")
            print("\n💡 To create demo data:")
            print("  python add_demo_trades.py")
            return
        
        print(f"📁 Found latest portfolio file: {latest_file}")
        
        # Create paper trading engine
        paper_engine = PaperTradingEngine()
        
        print("📊 Before loading:")
        initial_portfolio = paper_engine.get_portfolio_summary()
        print(f"  Cash: ${initial_portfolio['cash']:,.2f}")
        print(f"  Positions: {initial_portfolio['open_positions']}")
        
        # Load the state
        print(f"\n🔄 Loading state from {latest_file}...")
        paper_engine.load_state(latest_file)
        
        # Show loaded portfolio
        print("📊 After loading:")
        loaded_portfolio = paper_engine.get_portfolio_summary()
        
        print(f"  Initial Capital: ${loaded_portfolio['initial_capital']:,.2f}")
        print(f"  Current Value:   ${loaded_portfolio['portfolio_value']:,.2f}")
        print(f"  Available Cash:  ${loaded_portfolio['cash']:,.2f}")
        print(f"  Total P&L:       ${loaded_portfolio['total_pnl']:,.2f}")
        print(f"  Unrealized P&L:  ${loaded_portfolio['unrealized_pnl']:,.2f}")
        print(f"  Open Positions:  {loaded_portfolio['open_positions']}")
        
        # Show positions
        positions = paper_engine.get_positions()
        open_positions = [p for p in positions if p['is_open']]
        
        if open_positions:
            print(f"\n📈 Current Positions:")
            print("  Symbol | Qty | Entry Price | Current Price | P&L")
            print("  " + "-" * 50)
            
            for pos in open_positions:
                pnl = pos['unrealized_pnl']
                pnl_sign = "+" if pnl >= 0 else ""
                print(f"  {pos['symbol']:6} | {pos['quantity']:3} | ${pos['entry_price']:>9.2f} | ${pos['current_price']:>12.2f} | {pnl_sign}${pnl:.2f}")
        
        # Show recent trades
        trade_history = paper_engine.get_trade_history()
        if trade_history:
            print(f"\n📋 Recent Trades:")
            print("  Time     | Symbol | Action | Qty | Price")
            print("  " + "-" * 45)
            
            for trade in trade_history[-5:]:  # Last 5 trades
                time_str = trade['timestamp'].strftime('%H:%M:%S')
                print(f"  {time_str} | {trade['symbol']:6} | {trade['action']:6} | {trade['quantity']:3} | ${trade['price']:>8.2f}")
        
        # Save as the current state for the dashboard
        current_state_file = "current_portfolio_state.json"
        paper_engine.save_state(current_state_file)
        print(f"\n💾 Saved current state to: {current_state_file}")
        print("   (This will be used by your dashboard)")
        
        print(f"\n🎉 Portfolio state loaded successfully!")
        print(f"\n📱 Next Steps:")
        print("  1. Open your dashboard: http://localhost:8502")
        print("  2. The Portfolio tab should now show your loaded data")
        print("  3. Refresh the page if needed to see updates")
        
    except Exception as e:
        print(f"❌ Error loading portfolio state: {e}")
        print("\n🔧 Troubleshooting:")
        print("  1. Make sure you're in the AWOT directory")
        print("  2. Check if the portfolio file exists and is valid")
        print("  3. Try creating new demo data: python add_demo_trades.py")

def main():
    """Main function"""
    load_portfolio_state()

if __name__ == "__main__":
    main()
