import yfinance as yf
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from loguru import logger
import requests
from datetime import datetime, timedelta
import time
from config.settings import ALPHA_VANTAGE_API_KEY, BREAKOUT_VOLUME_THRESHOLD

class MarketDataProvider:
    def __init__(self):
        self.av_api_key = ALPHA_VANTAGE_API_KEY
        
    def get_stock_data(self, symbol: str, period: str = "1mo") -> pd.DataFrame:
        """Get historical stock data from Yahoo Finance"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval="1d")
            return data
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_options_chain(self, symbol: str) -> Dict:
        """Get options chain for a symbol"""
        try:
            ticker = yf.Ticker(symbol)
            expirations = ticker.options
            
            # Get nearest weekly expiration
            nearest_exp = expirations[0] if expirations else None
            if nearest_exp:
                chain = ticker.option_chain(nearest_exp)
                return {
                    'calls': chain.calls,
                    'puts': chain.puts,
                    'expiration': nearest_exp
                }
        except Exception as e:
            logger.error(f"Error fetching options for {symbol}: {e}")
        return {}
    
    def get_earnings_calendar(self) -> List[Dict]:
        """Get upcoming earnings from Alpha Vantage"""
        if not self.av_api_key:
            logger.warning("Alpha Vantage API key not configured")
            return []
            
        url = f"https://www.alphavantage.co/query"
        params = {
            'function': 'EARNINGS_CALENDAR',
            'horizon': '3month',
            'apikey': self.av_api_key
        }
        
        try:
            response = requests.get(url, params=params)
            # Parse CSV response
            lines = response.text.strip().split('\n')
            if len(lines) > 1:
                headers = lines[0].split(',')
                earnings = []
                for line in lines[1:]:
                    values = line.split(',')
                    if len(values) == len(headers):
                        earnings.append(dict(zip(headers, values)))
                return earnings[:50]  # Limit to 50 upcoming
        except Exception as e:
            logger.error(f"Error fetching earnings calendar: {e}")
        
        return []

    def get_real_time_data(self, symbol: str) -> Dict:
        """Get real-time market data for a symbol"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info

            # Get current price and basic metrics
            current_price = info.get('currentPrice', 0)
            if not current_price:
                # Fallback to regularMarketPrice
                current_price = info.get('regularMarketPrice', 0)

            return {
                'symbol': symbol,
                'current_price': current_price,
                'previous_close': info.get('previousClose', 0),
                'open': info.get('open', 0),
                'day_high': info.get('dayHigh', 0),
                'day_low': info.get('dayLow', 0),
                'volume': info.get('volume', 0),
                'avg_volume': info.get('averageVolume', 0),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'beta': info.get('beta', 0),
                'fifty_two_week_high': info.get('fiftyTwoWeekHigh', 0),
                'fifty_two_week_low': info.get('fiftyTwoWeekLow', 0),
                'timestamp': datetime.now()
            }
        except Exception as e:
            logger.error(f"Error fetching real-time data for {symbol}: {e}")
            return {}

    def get_intraday_data(self, symbol: str, interval: str = "1m", period: str = "1d") -> pd.DataFrame:
        """Get intraday data for real-time analysis"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            return data
        except Exception as e:
            logger.error(f"Error fetching intraday data for {symbol}: {e}")
            return pd.DataFrame()

    def analyze_volume_profile(self, data: pd.DataFrame) -> Dict:
        """Analyze volume patterns and unusual activity"""
        if data.empty:
            return {}

        try:
            # Calculate volume statistics
            avg_volume_20 = data['Volume'].rolling(20).mean().iloc[-1]
            current_volume = data['Volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume_20 if avg_volume_20 > 0 else 0

            # Volume trend analysis
            volume_trend = data['Volume'].rolling(5).mean().pct_change(5).iloc[-1]

            # Price-volume relationship
            price_change = data['Close'].pct_change().iloc[-1]
            volume_price_correlation = data['Close'].pct_change().corr(data['Volume'].pct_change())

            return {
                'current_volume': current_volume,
                'avg_volume_20d': avg_volume_20,
                'volume_ratio': volume_ratio,
                'unusual_volume': volume_ratio > BREAKOUT_VOLUME_THRESHOLD,
                'volume_trend': volume_trend,
                'price_change': price_change,
                'volume_price_correlation': volume_price_correlation,
                'high_volume_breakout': volume_ratio > BREAKOUT_VOLUME_THRESHOLD and abs(price_change) > 0.02
            }
        except Exception as e:
            logger.error(f"Error analyzing volume profile: {e}")
            return {}

    def get_options_flow_data(self, symbol: str) -> Dict:
        """Analyze options flow and unusual options activity"""
        try:
            options_data = self.get_options_chain(symbol)
            if not options_data:
                return {}

            calls = options_data.get('calls', pd.DataFrame())
            puts = options_data.get('puts', pd.DataFrame())

            if calls.empty or puts.empty:
                return {}

            # Calculate put/call ratio by volume and open interest
            total_call_volume = calls['volume'].fillna(0).sum()
            total_put_volume = puts['volume'].fillna(0).sum()
            total_call_oi = calls['openInterest'].fillna(0).sum()
            total_put_oi = puts['openInterest'].fillna(0).sum()

            pc_ratio_volume = total_put_volume / total_call_volume if total_call_volume > 0 else 0
            pc_ratio_oi = total_put_oi / total_call_oi if total_call_oi > 0 else 0

            # Find unusual options activity
            calls['unusual_volume'] = calls['volume'] > calls['openInterest'] * 0.5
            puts['unusual_volume'] = puts['volume'] > puts['openInterest'] * 0.5

            unusual_calls = calls[calls['unusual_volume']].nlargest(5, 'volume')
            unusual_puts = puts[puts['unusual_volume']].nlargest(5, 'volume')

            return {
                'expiration': options_data.get('expiration'),
                'total_call_volume': total_call_volume,
                'total_put_volume': total_put_volume,
                'pc_ratio_volume': pc_ratio_volume,
                'pc_ratio_oi': pc_ratio_oi,
                'unusual_calls': unusual_calls.to_dict('records') if not unusual_calls.empty else [],
                'unusual_puts': unusual_puts.to_dict('records') if not unusual_puts.empty else [],
                'high_activity': pc_ratio_volume > 1.5 or pc_ratio_volume < 0.5
            }
        except Exception as e:
            logger.error(f"Error analyzing options flow for {symbol}: {e}")
            return {}

    def get_market_sentiment_indicators(self) -> Dict:
        """Get overall market sentiment indicators"""
        try:
            # VIX data
            vix = yf.Ticker("^VIX")
            vix_data = vix.history(period="5d")
            current_vix = vix_data['Close'].iloc[-1] if not vix_data.empty else 0
            vix_change = vix_data['Close'].pct_change().iloc[-1] if len(vix_data) > 1 else 0

            # SPY for market direction
            spy = yf.Ticker("SPY")
            spy_data = spy.history(period="5d")
            spy_change = spy_data['Close'].pct_change().iloc[-1] if not spy_data.empty else 0

            # Market sentiment interpretation
            fear_greed_score = self._calculate_fear_greed_score(current_vix, spy_change)

            return {
                'vix_level': current_vix,
                'vix_change': vix_change,
                'spy_change': spy_change,
                'fear_greed_score': fear_greed_score,
                'market_sentiment': self._interpret_sentiment(fear_greed_score),
                'volatility_regime': 'high' if current_vix > 25 else 'normal' if current_vix > 15 else 'low'
            }
        except Exception as e:
            logger.error(f"Error getting market sentiment: {e}")
            return {}

    def _calculate_fear_greed_score(self, vix: float, spy_change: float) -> float:
        """Calculate a simple fear/greed score (0-100, where 0 is extreme fear, 100 is extreme greed)"""
        # Normalize VIX (typical range 10-80)
        vix_normalized = max(0, min(100, (80 - vix) / 70 * 100))

        # Normalize SPY change (typical daily range -5% to +5%)
        spy_normalized = max(0, min(100, (spy_change + 0.05) / 0.1 * 100))

        # Weighted average (VIX has more weight)
        return vix_normalized * 0.7 + spy_normalized * 0.3

    def _interpret_sentiment(self, score: float) -> str:
        """Interpret fear/greed score"""
        if score < 20:
            return "extreme_fear"
        elif score < 40:
            return "fear"
        elif score < 60:
            return "neutral"
        elif score < 80:
            return "greed"
        else:
            return "extreme_greed"

    def scan_for_breakout_candidates(self, symbols: List[str], min_volume_ratio: float = 2.0) -> List[Dict]:
        """Scan multiple symbols for potential breakout candidates"""
        candidates = []

        for symbol in symbols:
            try:
                # Get recent data
                data = self.get_stock_data(symbol, period="3mo")
                if data.empty:
                    continue

                # Analyze volume and price action
                volume_analysis = self.analyze_volume_profile(data)
                real_time_data = self.get_real_time_data(symbol)

                # Check for breakout conditions
                if (volume_analysis.get('volume_ratio', 0) >= min_volume_ratio and
                    abs(volume_analysis.get('price_change', 0)) > 0.02):

                    # Calculate additional metrics
                    price_near_52w_high = (real_time_data.get('current_price', 0) /
                                         real_time_data.get('fifty_two_week_high', 1)) > 0.95

                    candidates.append({
                        'symbol': symbol,
                        'current_price': real_time_data.get('current_price', 0),
                        'price_change': volume_analysis.get('price_change', 0),
                        'volume_ratio': volume_analysis.get('volume_ratio', 0),
                        'near_52w_high': price_near_52w_high,
                        'market_cap': real_time_data.get('market_cap', 0),
                        'beta': real_time_data.get('beta', 0),
                        'breakout_score': self._calculate_breakout_score(volume_analysis, real_time_data)
                    })

                # Add small delay to avoid rate limiting
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error scanning {symbol}: {e}")
                continue

        # Sort by breakout score
        candidates.sort(key=lambda x: x['breakout_score'], reverse=True)
        return candidates

    def _calculate_breakout_score(self, volume_analysis: Dict, real_time_data: Dict) -> float:
        """Calculate a breakout score (0-100)"""
        score = 0

        # Volume component (40% weight)
        volume_ratio = volume_analysis.get('volume_ratio', 0)
        volume_score = min(40, volume_ratio * 10)  # Cap at 40
        score += volume_score

        # Price movement component (30% weight)
        price_change = abs(volume_analysis.get('price_change', 0))
        price_score = min(30, price_change * 1000)  # 3% move = 30 points
        score += price_score

        # Technical position component (20% weight)
        current_price = real_time_data.get('current_price', 0)
        fifty_two_week_high = real_time_data.get('fifty_two_week_high', 1)
        if fifty_two_week_high > 0:
            position_score = (current_price / fifty_two_week_high) * 20
            score += position_score

        # Market cap component (10% weight) - favor mid-cap stocks
        market_cap = real_time_data.get('market_cap', 0)
        if 1e9 <= market_cap <= 50e9:  # $1B to $50B
            score += 10
        elif 500e6 <= market_cap <= 100e9:  # $500M to $100B
            score += 5

        return min(100, score)

    def get_sector_performance(self) -> Dict[str, float]:
        """Get sector ETF performance for market context"""
        sector_etfs = {
            'Technology': 'XLK',
            'Healthcare': 'XLV',
            'Financials': 'XLF',
            'Energy': 'XLE',
            'Industrials': 'XLI',
            'Consumer Discretionary': 'XLY',
            'Consumer Staples': 'XLP',
            'Utilities': 'XLU',
            'Real Estate': 'XLRE',
            'Materials': 'XLB',
            'Communication': 'XLC'
        }

        performance = {}
        for sector, etf in sector_etfs.items():
            try:
                ticker = yf.Ticker(etf)
                data = ticker.history(period="5d")
                if not data.empty:
                    daily_return = data['Close'].pct_change().iloc[-1]
                    performance[sector] = daily_return
            except Exception as e:
                logger.error(f"Error getting {sector} performance: {e}")
                performance[sector] = 0

        return performance

    def get_economic_calendar_events(self) -> List[Dict]:
        """Get upcoming economic events that could impact markets"""
        # This is a simplified version - in production, you'd use a dedicated economic calendar API
        events = []

        try:
            # For now, we'll return a basic structure
            # In production, integrate with APIs like Trading Economics, Alpha Vantage, or others
            current_date = datetime.now()

            # Sample events structure
            sample_events = [
                {
                    'date': current_date + timedelta(days=1),
                    'event': 'FOMC Meeting',
                    'importance': 'high',
                    'currency': 'USD',
                    'impact': 'volatility'
                },
                {
                    'date': current_date + timedelta(days=2),
                    'event': 'Non-Farm Payrolls',
                    'importance': 'high',
                    'currency': 'USD',
                    'impact': 'directional'
                }
            ]

            return sample_events

        except Exception as e:
            logger.error(f"Error fetching economic calendar: {e}")
            return []

    def get_comprehensive_market_data(self, symbol: str) -> Dict:
        """Get comprehensive market data for a symbol including all analysis"""
        try:
            # Get all data types
            historical_data = self.get_stock_data(symbol, period="3mo")
            real_time_data = self.get_real_time_data(symbol)
            volume_analysis = self.analyze_volume_profile(historical_data)
            options_flow = self.get_options_flow_data(symbol)

            return {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'price_data': real_time_data,
                'volume_analysis': volume_analysis,
                'options_flow': options_flow,
                'historical_data': historical_data.tail(50).to_dict('records') if not historical_data.empty else [],
                'data_quality': self._assess_data_quality(historical_data, real_time_data)
            }
        except Exception as e:
            logger.error(f"Error getting comprehensive data for {symbol}: {e}")
            return {}

    def _assess_data_quality(self, historical_data: pd.DataFrame, real_time_data: Dict) -> Dict:
        """Assess the quality and completeness of market data"""
        quality_score = 100
        issues = []

        # Check historical data completeness
        if historical_data.empty:
            quality_score -= 50
            issues.append("No historical data available")
        elif len(historical_data) < 20:
            quality_score -= 20
            issues.append("Limited historical data")

        # Check for missing real-time data
        if not real_time_data.get('current_price'):
            quality_score -= 30
            issues.append("No current price data")

        if not real_time_data.get('volume'):
            quality_score -= 10
            issues.append("No volume data")

        # Check for data staleness
        if real_time_data.get('timestamp'):
            time_diff = datetime.now() - real_time_data['timestamp']
            if time_diff.total_seconds() > 300:  # 5 minutes
                quality_score -= 15
                issues.append("Stale data")

        return {
            'quality_score': max(0, quality_score),
            'issues': issues,
            'data_completeness': quality_score / 100
        }