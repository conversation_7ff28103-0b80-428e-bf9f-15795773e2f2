import yfinance as yf
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
from loguru import logger
import requests
from config.settings import ALPHA_VANTAGE_API_KEY

class MarketDataProvider:
    def __init__(self):
        self.av_api_key = ALPHA_VANTAGE_API_KEY
        
    def get_stock_data(self, symbol: str, period: str = "1mo") -> pd.DataFrame:
        """Get historical stock data from Yahoo Finance"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval="1d")
            return data
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_options_chain(self, symbol: str) -> Dict:
        """Get options chain for a symbol"""
        try:
            ticker = yf.Ticker(symbol)
            expirations = ticker.options
            
            # Get nearest weekly expiration
            nearest_exp = expirations[0] if expirations else None
            if nearest_exp:
                chain = ticker.option_chain(nearest_exp)
                return {
                    'calls': chain.calls,
                    'puts': chain.puts,
                    'expiration': nearest_exp
                }
        except Exception as e:
            logger.error(f"Error fetching options for {symbol}: {e}")
        return {}
    
    def get_earnings_calendar(self) -> List[Dict]:
        """Get upcoming earnings from Alpha Vantage"""
        if not self.av_api_key:
            logger.warning("Alpha Vantage API key not configured")
            return []
            
        url = f"https://www.alphavantage.co/query"
        params = {
            'function': 'EARNINGS_CALENDAR',
            'horizon': '3month',
            'apikey': self.av_api_key
        }
        
        try:
            response = requests.get(url, params=params)
            # Parse CSV response
            lines = response.text.strip().split('\n')
            if len(lines) > 1:
                headers = lines[0].split(',')
                earnings = []
                for line in lines[1:]:
                    values = line.split(',')
                    if len(values) == len(headers):
                        earnings.append(dict(zip(headers, values)))
                return earnings[:50]  # Limit to 50 upcoming
        except Exception as e:
            logger.error(f"Error fetching earnings calendar: {e}")
        
        return []