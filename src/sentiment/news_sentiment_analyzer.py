#!/usr/bin/env python3
"""
News and Sentiment Analysis Module for AWOT Trading Platform
Integrates multiple news sources and sentiment analysis for enhanced trading signals
"""

import requests
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import re
import os
from loguru import logger

try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False
    logger.warning("TextBlob not available. Install with: pip install textblob")

try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False


class SentimentScore(Enum):
    """Sentiment score levels"""
    VERY_NEGATIVE = -2
    NEGATIVE = -1
    NEUTRAL = 0
    POSITIVE = 1
    VERY_POSITIVE = 2


class NewsImpact(Enum):
    """News impact levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class NewsItem:
    """Represents a single news item"""
    title: str
    summary: str
    source: str
    timestamp: datetime
    url: str
    sentiment_score: float
    impact_level: NewsImpact
    keywords: List[str]
    relevance_score: float


@dataclass
class SentimentAnalysis:
    """Complete sentiment analysis for a symbol"""
    symbol: str
    overall_sentiment: SentimentScore
    sentiment_score: float  # -1 to 1
    news_count: int
    high_impact_news: int
    sentiment_trend: str  # "improving", "declining", "stable"
    key_themes: List[str]
    confidence: float
    last_updated: datetime


class NewsSentimentAnalyzer:
    """Analyzes news and sentiment for trading signals"""
    
    def __init__(self):
        self.news_sources = {
            'alpha_vantage': self._get_alpha_vantage_news,
            'yahoo_finance': self._get_yahoo_finance_news,
            'reddit_sentiment': self._get_reddit_sentiment,
            'twitter_sentiment': self._get_twitter_sentiment
        }
        
        # High-impact keywords that significantly move stocks
        self.high_impact_keywords = {
            'earnings': ['earnings', 'eps', 'revenue', 'guidance', 'outlook'],
            'fda': ['fda', 'approval', 'clinical', 'trial', 'drug'],
            'acquisition': ['acquisition', 'merger', 'buyout', 'takeover'],
            'product': ['launch', 'product', 'release', 'announcement'],
            'analyst': ['upgrade', 'downgrade', 'price target', 'rating'],
            'legal': ['lawsuit', 'settlement', 'investigation', 'sec'],
            'executive': ['ceo', 'cfo', 'resignation', 'appointment'],
            'partnership': ['partnership', 'deal', 'contract', 'agreement']
        }
        
        # Sentiment modifiers
        self.positive_modifiers = [
            'beat', 'exceed', 'strong', 'growth', 'positive', 'bullish',
            'upgrade', 'buy', 'outperform', 'success', 'breakthrough'
        ]
        
        self.negative_modifiers = [
            'miss', 'below', 'weak', 'decline', 'negative', 'bearish',
            'downgrade', 'sell', 'underperform', 'failure', 'concern'
        ]
    
    def analyze_sentiment(self, symbol: str, hours_back: int = 24) -> SentimentAnalysis:
        """Perform comprehensive sentiment analysis for a symbol"""
        try:
            logger.info(f"Analyzing sentiment for {symbol}")
            
            # Collect news from all sources
            all_news = []
            for source_name, source_func in self.news_sources.items():
                try:
                    news_items = source_func(symbol, hours_back)
                    all_news.extend(news_items)
                    logger.debug(f"Got {len(news_items)} items from {source_name}")
                except Exception as e:
                    logger.warning(f"Failed to get news from {source_name}: {e}")
            
            if not all_news:
                return self._create_neutral_sentiment(symbol)
            
            # Analyze sentiment
            sentiment_analysis = self._analyze_news_sentiment(symbol, all_news)
            
            logger.info(f"Sentiment analysis complete for {symbol}: "
                       f"{sentiment_analysis.overall_sentiment.name} "
                       f"(score: {sentiment_analysis.sentiment_score:.2f})")
            
            return sentiment_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment for {symbol}: {e}")
            return self._create_neutral_sentiment(symbol)
    
    def _get_alpha_vantage_news(self, symbol: str, hours_back: int) -> List[NewsItem]:
        """Get news from Alpha Vantage API"""
        api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
        if not api_key:
            return []
        
        try:
            url = f"https://www.alphavantage.co/query"
            params = {
                'function': 'NEWS_SENTIMENT',
                'tickers': symbol,
                'apikey': api_key,
                'limit': 50
            }
            
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if 'feed' not in data:
                return []
            
            news_items = []
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            for item in data['feed']:
                try:
                    # Parse timestamp
                    timestamp_str = item.get('time_published', '')
                    timestamp = datetime.strptime(timestamp_str, '%Y%m%dT%H%M%S')
                    
                    if timestamp < cutoff_time:
                        continue
                    
                    # Extract sentiment score
                    ticker_sentiment = item.get('ticker_sentiment', [])
                    sentiment_score = 0.0
                    relevance_score = 0.0
                    
                    for ticker_data in ticker_sentiment:
                        if ticker_data.get('ticker') == symbol:
                            sentiment_score = float(ticker_data.get('ticker_sentiment_score', 0))
                            relevance_score = float(ticker_data.get('relevance_score', 0))
                            break
                    
                    # Determine impact level
                    impact_level = self._determine_impact_level(
                        item.get('title', ''), 
                        item.get('summary', ''),
                        relevance_score
                    )
                    
                    news_item = NewsItem(
                        title=item.get('title', ''),
                        summary=item.get('summary', ''),
                        source='Alpha Vantage',
                        timestamp=timestamp,
                        url=item.get('url', ''),
                        sentiment_score=sentiment_score,
                        impact_level=impact_level,
                        keywords=self._extract_keywords(item.get('title', '') + ' ' + item.get('summary', '')),
                        relevance_score=relevance_score
                    )
                    
                    news_items.append(news_item)
                    
                except Exception as e:
                    logger.warning(f"Error parsing Alpha Vantage news item: {e}")
                    continue
            
            return news_items
            
        except Exception as e:
            logger.warning(f"Error fetching Alpha Vantage news: {e}")
            return []
    
    def _get_yahoo_finance_news(self, symbol: str, hours_back: int) -> List[NewsItem]:
        """Get news from Yahoo Finance"""
        if not YFINANCE_AVAILABLE:
            return []
        
        try:
            ticker = yf.Ticker(symbol)
            news = ticker.news
            
            news_items = []
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            for item in news:
                try:
                    # Parse timestamp
                    timestamp = datetime.fromtimestamp(item.get('providerPublishTime', 0))
                    
                    if timestamp < cutoff_time:
                        continue
                    
                    title = item.get('title', '')
                    summary = item.get('summary', title)  # Use title if no summary
                    
                    # Calculate sentiment using TextBlob if available
                    sentiment_score = 0.0
                    if TEXTBLOB_AVAILABLE:
                        text = f"{title} {summary}"
                        blob = TextBlob(text)
                        sentiment_score = blob.sentiment.polarity
                    
                    # Determine impact level
                    impact_level = self._determine_impact_level(title, summary, 0.5)
                    
                    news_item = NewsItem(
                        title=title,
                        summary=summary,
                        source='Yahoo Finance',
                        timestamp=timestamp,
                        url=item.get('link', ''),
                        sentiment_score=sentiment_score,
                        impact_level=impact_level,
                        keywords=self._extract_keywords(f"{title} {summary}"),
                        relevance_score=0.8  # Yahoo Finance is generally relevant
                    )
                    
                    news_items.append(news_item)
                    
                except Exception as e:
                    logger.warning(f"Error parsing Yahoo Finance news item: {e}")
                    continue
            
            return news_items
            
        except Exception as e:
            logger.warning(f"Error fetching Yahoo Finance news: {e}")
            return []
    
    def _get_reddit_sentiment(self, symbol: str, hours_back: int) -> List[NewsItem]:
        """Get sentiment from Reddit (simplified implementation)"""
        # This would integrate with Reddit API or web scraping
        # For now, return empty list - can be implemented later
        return []
    
    def _get_twitter_sentiment(self, symbol: str, hours_back: int) -> List[NewsItem]:
        """Get sentiment from Twitter (simplified implementation)"""
        # This would integrate with Twitter API
        # For now, return empty list - can be implemented later
        return []
    
    def _determine_impact_level(self, title: str, summary: str, relevance_score: float) -> NewsImpact:
        """Determine the impact level of a news item"""
        text = f"{title} {summary}".lower()
        
        # Check for high-impact keywords
        high_impact_count = 0
        for category, keywords in self.high_impact_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    high_impact_count += 1
        
        # Determine impact based on keywords and relevance
        if high_impact_count >= 3 or relevance_score > 0.8:
            return NewsImpact.CRITICAL
        elif high_impact_count >= 2 or relevance_score > 0.6:
            return NewsImpact.HIGH
        elif high_impact_count >= 1 or relevance_score > 0.4:
            return NewsImpact.MEDIUM
        else:
            return NewsImpact.LOW
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from text"""
        text = text.lower()
        keywords = []
        
        for category, keyword_list in self.high_impact_keywords.items():
            for keyword in keyword_list:
                if keyword in text:
                    keywords.append(keyword)
        
        return list(set(keywords))  # Remove duplicates
    
    def _analyze_news_sentiment(self, symbol: str, news_items: List[NewsItem]) -> SentimentAnalysis:
        """Analyze overall sentiment from news items"""
        if not news_items:
            return self._create_neutral_sentiment(symbol)
        
        # Calculate weighted sentiment score
        total_weight = 0
        weighted_sentiment = 0
        high_impact_count = 0
        
        for item in news_items:
            # Weight by impact level and relevance
            weight = item.relevance_score * item.impact_level.value
            weighted_sentiment += item.sentiment_score * weight
            total_weight += weight
            
            if item.impact_level in [NewsImpact.HIGH, NewsImpact.CRITICAL]:
                high_impact_count += 1
        
        # Calculate overall sentiment score
        overall_score = weighted_sentiment / total_weight if total_weight > 0 else 0
        
        # Determine sentiment category
        if overall_score >= 0.3:
            sentiment_category = SentimentScore.VERY_POSITIVE
        elif overall_score >= 0.1:
            sentiment_category = SentimentScore.POSITIVE
        elif overall_score <= -0.3:
            sentiment_category = SentimentScore.VERY_NEGATIVE
        elif overall_score <= -0.1:
            sentiment_category = SentimentScore.NEGATIVE
        else:
            sentiment_category = SentimentScore.NEUTRAL
        
        # Extract key themes
        all_keywords = []
        for item in news_items:
            all_keywords.extend(item.keywords)
        
        # Count keyword frequency
        keyword_counts = {}
        for keyword in all_keywords:
            keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
        
        # Get top themes
        key_themes = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        key_themes = [theme[0] for theme in key_themes]
        
        # Calculate confidence based on news volume and consistency
        confidence = min(1.0, len(news_items) / 10)  # More news = higher confidence
        if high_impact_count > 0:
            confidence = min(1.0, confidence + 0.3)  # High impact news boosts confidence
        
        # Determine trend (simplified)
        recent_items = [item for item in news_items if item.timestamp > datetime.now() - timedelta(hours=6)]
        older_items = [item for item in news_items if item.timestamp <= datetime.now() - timedelta(hours=6)]
        
        if recent_items and older_items:
            recent_sentiment = sum(item.sentiment_score for item in recent_items) / len(recent_items)
            older_sentiment = sum(item.sentiment_score for item in older_items) / len(older_items)
            
            if recent_sentiment > older_sentiment + 0.1:
                trend = "improving"
            elif recent_sentiment < older_sentiment - 0.1:
                trend = "declining"
            else:
                trend = "stable"
        else:
            trend = "stable"
        
        return SentimentAnalysis(
            symbol=symbol,
            overall_sentiment=sentiment_category,
            sentiment_score=overall_score,
            news_count=len(news_items),
            high_impact_news=high_impact_count,
            sentiment_trend=trend,
            key_themes=key_themes,
            confidence=confidence,
            last_updated=datetime.now()
        )
    
    def _create_neutral_sentiment(self, symbol: str) -> SentimentAnalysis:
        """Create neutral sentiment analysis when no news is available"""
        return SentimentAnalysis(
            symbol=symbol,
            overall_sentiment=SentimentScore.NEUTRAL,
            sentiment_score=0.0,
            news_count=0,
            high_impact_news=0,
            sentiment_trend="stable",
            key_themes=[],
            confidence=0.1,  # Low confidence due to no data
            last_updated=datetime.now()
        )
    
    def get_sentiment_signal_boost(self, sentiment: SentimentAnalysis, signal_type) -> float:
        """Calculate sentiment-based signal boost"""
        if sentiment.confidence < 0.3:
            return 0.0  # Not enough data for reliable boost
        
        # Base boost from sentiment score
        sentiment_boost = sentiment.sentiment_score * 0.2  # Max 20% boost
        
        # Additional boost for high-impact news
        if sentiment.high_impact_news > 0:
            sentiment_boost += 0.1  # 10% additional boost
        
        # Trend boost
        if sentiment.sentiment_trend == "improving" and sentiment.sentiment_score > 0:
            sentiment_boost += 0.05
        elif sentiment.sentiment_trend == "declining" and sentiment.sentiment_score < 0:
            sentiment_boost += 0.05
        
        # Ensure boost aligns with signal direction
        from signals.signal_generator import SignalType
        
        bullish_signals = [SignalType.MOMENTUM_BULLISH, SignalType.BREAKOUT_BULLISH]
        bearish_signals = [SignalType.MOMENTUM_BEARISH, SignalType.BREAKOUT_BEARISH]
        
        if signal_type in bullish_signals and sentiment_boost < 0:
            sentiment_boost = 0  # Don't penalize bullish signals with negative sentiment
        elif signal_type in bearish_signals and sentiment_boost > 0:
            sentiment_boost = 0  # Don't penalize bearish signals with positive sentiment
        
        return max(-0.3, min(0.3, sentiment_boost))  # Cap at ±30%


# Factory function
def create_sentiment_analyzer() -> NewsSentimentAnalyzer:
    """Create and return a sentiment analyzer instance"""
    return NewsSentimentAnalyzer()
