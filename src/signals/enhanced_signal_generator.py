#!/usr/bin/env python3
"""
Enhanced Signal Generator with Improved Win Rate Algorithms
Implements advanced signal filtering and multi-timeframe analysis
"""

import os
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger

from .signal_generator import SignalGenerator, TradingSignal, SignalType, SignalStrength
from indicators.technical_indicators import TechnicalIndicators
from data.market_data import MarketDataProvider
from sentiment.news_sentiment_analyzer import NewsSentimentAnalyzer


class EnhancedSignalGenerator(SignalGenerator):
    """Enhanced signal generator with improved win rate algorithms"""
    
    def __init__(self):
        super().__init__()
        # Option B: Use lowered thresholds from environment
        self.min_confidence_threshold = float(os.getenv('SIGNAL_CONFIDENCE_THRESHOLD', '0.5'))  # Lowered from 0.7
        self.volume_threshold = float(os.getenv('MIN_VOLUME_RATIO', '1.0'))  # Lowered from 1.5
        self.signal_performance_history = {}
        self.sentiment_analyzer = NewsSentimentAnalyzer()

        # Initialize market data provider (from parent class)
        self.market_data_provider = self.market_data
        
        # Optimal trading hours (hour, minute tuples)
        self.optimal_hours = [
            (9, 30, 10, 30),   # Morning breakout
            (11, 0, 14, 0),    # Midday momentum  
            (15, 0, 16, 0)     # Power hour
        ]
    
    def generate_signals(self, symbol: str) -> List[TradingSignal]:
        """Generate enhanced signals with improved filtering"""
        try:
            # Check if it's optimal trading time (allow override for testing)
            force_generate = os.getenv('FORCE_SIGNAL_GENERATION', 'false').lower() == 'true'

            if not force_generate and not self._is_optimal_trading_time():
                logger.info(f"Outside optimal trading hours, skipping signal generation for {symbol}")
                return []
            
            # Get multi-timeframe data
            timeframes = ['5m', '15m', '1h']
            market_data = {}
            
            for tf in timeframes:
                try:
                    # Use get_intraday_data for timeframes with intervals
                    if hasattr(self.market_data_provider, 'get_intraday_data'):
                        data = self.market_data_provider.get_intraday_data(symbol, interval=tf, period='1d')
                    else:
                        # Fallback to regular get_stock_data
                        data = self.market_data_provider.get_stock_data(symbol, period='1d')

                    if data is not None and not data.empty:
                        market_data[tf] = data
                except Exception as e:
                    logger.warning(f"Failed to get {tf} data for {symbol}: {e}")
            
            if not market_data:
                logger.warning(f"No market data available for {symbol}")
                return []
            
            # Generate base signals
            base_signals = super().generate_signals(symbol)
            
            # Apply enhanced filtering
            enhanced_signals = []
            for signal in base_signals:
                if self._validate_enhanced_signal(signal, symbol, market_data):
                    enhanced_signal = self._enhance_signal(signal, symbol, market_data)
                    enhanced_signals.append(enhanced_signal)
            
            return enhanced_signals
            
        except Exception as e:
            logger.error(f"Error generating enhanced signals for {symbol}: {e}")
            return []
    
    def _is_optimal_trading_time(self) -> bool:
        """Check if current time is within optimal trading hours"""
        now = datetime.now()
        current_hour = now.hour
        current_minute = now.minute
        
        for start_h, start_m, end_h, end_m in self.optimal_hours:
            if (start_h, start_m) <= (current_hour, current_minute) <= (end_h, end_m):
                return True
        return False
    
    def _validate_enhanced_signal(self, signal: TradingSignal, symbol: str, 
                                market_data: Dict) -> bool:
        """Enhanced signal validation with multiple filters"""
        
        # 1. Confidence threshold filter
        if signal.confidence < self.min_confidence_threshold:
            logger.debug(f"Signal rejected: confidence {signal.confidence} below threshold {self.min_confidence_threshold}")
            return False
        
        # 2. Multi-timeframe confirmation
        if not self._check_timeframe_confluence(signal, market_data):
            logger.debug(f"Signal rejected: insufficient timeframe confluence")
            return False
        
        # 3. Volume confirmation
        if not self._check_volume_confirmation(symbol, market_data):
            logger.debug(f"Signal rejected: insufficient volume")
            return False
        
        # 4. Market sentiment filter
        if not self._check_market_sentiment(signal):
            logger.debug(f"Signal rejected: unfavorable market sentiment")
            return False
        
        # 5. Risk management filter
        if not self._check_risk_parameters(signal):
            logger.debug(f"Signal rejected: poor risk/reward ratio")
            return False

        # 6. News and sentiment filter
        if not self._check_news_sentiment(signal, symbol):
            logger.debug(f"Signal rejected: unfavorable news sentiment")
            return False

        return True
    
    def _check_timeframe_confluence(self, signal: TradingSignal, 
                                  market_data: Dict) -> bool:
        """Check if signal is confirmed across multiple timeframes"""
        confirmations = 0
        required_confirmations = 2  # Need at least 2 timeframes to agree
        
        for timeframe, data in market_data.items():
            if data.empty:
                continue
                
            # Calculate indicators for this timeframe
            indicators = TechnicalIndicators()
            indicators_df = indicators.calculate_all_indicators(data)
            
            if indicators_df.empty:
                continue
            
            latest = indicators_df.iloc[-1]
            
            # Check trend alignment based on signal type
            if signal.signal_type in [SignalType.MOMENTUM_BULLISH, SignalType.BREAKOUT_BULLISH]:
                # Bullish confirmation
                if (latest.get('EMA_12', 0) > latest.get('EMA_26', 0) and
                    latest.get('RSI', 50) > 45 and
                    latest.get('MACD', 0) > latest.get('MACD_Signal', 0)):
                    confirmations += 1
            
            elif signal.signal_type in [SignalType.MOMENTUM_BEARISH, SignalType.BREAKOUT_BEARISH]:
                # Bearish confirmation
                if (latest.get('EMA_12', 0) < latest.get('EMA_26', 0) and
                    latest.get('RSI', 50) < 55 and
                    latest.get('MACD', 0) < latest.get('MACD_Signal', 0)):
                    confirmations += 1
        
        return confirmations >= required_confirmations
    
    def _check_volume_confirmation(self, symbol: str, market_data: Dict) -> bool:
        """Check if current volume supports the signal"""
        try:
            # Use 5-minute data for volume analysis
            data_5m = market_data.get('5m')
            if data_5m is None or data_5m.empty:
                return False
            
            # Calculate average volume over last 20 periods
            recent_volume = data_5m['Volume'].tail(20)
            avg_volume = recent_volume.mean()
            current_volume = data_5m['Volume'].iloc[-1]
            
            # Require volume to be above threshold
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            return volume_ratio >= self.volume_threshold
            
        except Exception as e:
            logger.warning(f"Volume confirmation failed for {symbol}: {e}")
            return False
    
    def _check_market_sentiment(self, signal: TradingSignal) -> bool:
        """Check broader market sentiment"""
        try:
            # Get SPY data as market proxy
            if hasattr(self.market_data_provider, 'get_intraday_data'):
                spy_data = self.market_data_provider.get_intraday_data('SPY', interval='5m', period='1d')
            else:
                spy_data = self.market_data_provider.get_stock_data('SPY', period='1d')
            
            if spy_data is None or spy_data.empty:
                return True  # Neutral if can't determine
            
            # Calculate market trend
            spy_close = spy_data['Close']
            market_change = (spy_close.iloc[-1] - spy_close.iloc[-10]) / spy_close.iloc[-10]
            
            # Align signal with market sentiment
            if signal.signal_type in [SignalType.MOMENTUM_BULLISH, SignalType.BREAKOUT_BULLISH]:
                return market_change > -0.01  # Allow slight market decline
            elif signal.signal_type in [SignalType.MOMENTUM_BEARISH, SignalType.BREAKOUT_BEARISH]:
                return market_change < 0.01   # Allow slight market rise
            
            return True  # Neutral for other signal types
            
        except Exception as e:
            logger.warning(f"Market sentiment check failed: {e}")
            return True  # Default to neutral
    
    def _check_risk_parameters(self, signal: TradingSignal) -> bool:
        """Validate risk/reward ratio"""
        if not signal.target_price or not signal.stop_loss_price or not signal.entry_price:
            return False
        
        # Calculate risk/reward ratio
        potential_profit = abs(signal.target_price - signal.entry_price)
        potential_loss = abs(signal.entry_price - signal.stop_loss_price)
        
        if potential_loss == 0:
            return False
        
        risk_reward_ratio = potential_profit / potential_loss
        
        # Require at least 1.5:1 risk/reward ratio
        return risk_reward_ratio >= 1.5

    def _check_news_sentiment(self, signal: TradingSignal, symbol: str) -> bool:
        """Check news sentiment alignment with signal"""
        try:
            # Get recent sentiment analysis
            sentiment = self.sentiment_analyzer.analyze_sentiment(symbol, hours_back=12)

            # If no news or low confidence, allow signal (neutral)
            if sentiment.confidence < 0.3:
                return True

            # Check sentiment alignment with signal direction
            if signal.signal_type in [SignalType.MOMENTUM_BULLISH, SignalType.BREAKOUT_BULLISH]:
                # For bullish signals, reject if very negative sentiment
                if sentiment.overall_sentiment.value <= -1 and sentiment.confidence > 0.6:
                    logger.debug(f"Bullish signal rejected due to negative sentiment: {sentiment.overall_sentiment.name}")
                    return False

            elif signal.signal_type in [SignalType.MOMENTUM_BEARISH, SignalType.BREAKOUT_BEARISH]:
                # For bearish signals, reject if very positive sentiment
                if sentiment.overall_sentiment.value >= 1 and sentiment.confidence > 0.6:
                    logger.debug(f"Bearish signal rejected due to positive sentiment: {sentiment.overall_sentiment.name}")
                    return False

            # Check for high-impact negative news that could override technical signals
            if sentiment.high_impact_news > 0 and sentiment.sentiment_score < -0.5:
                logger.debug(f"Signal rejected due to high-impact negative news")
                return False

            return True

        except Exception as e:
            logger.warning(f"News sentiment check failed for {symbol}: {e}")
            return True  # Default to allowing signal if sentiment check fails
    
    def _enhance_signal(self, signal: TradingSignal, symbol: str, 
                       market_data: Dict) -> TradingSignal:
        """Enhance signal with dynamic parameters"""
        
        # Calculate dynamic stop loss and take profit
        stop_loss, take_profit = self._calculate_dynamic_levels(signal, market_data)
        
        # Adjust confidence based on signal quality
        enhanced_confidence = self._calculate_enhanced_confidence(signal, symbol, market_data)
        
        # Create enhanced signal
        enhanced_signal = TradingSignal(
            symbol=signal.symbol,
            signal_type=signal.signal_type,
            strength=signal.strength,
            confidence=enhanced_confidence,
            entry_price=signal.entry_price,
            target_price=take_profit,
            stop_loss_price=stop_loss,
            expiration_date=signal.expiration_date,
            option_type=signal.option_type,
            reasoning=f"Enhanced: {signal.reasoning}"
        )
        
        return enhanced_signal
    
    def _calculate_dynamic_levels(self, signal: TradingSignal, 
                                market_data: Dict) -> Tuple[float, float]:
        """Calculate dynamic stop loss and take profit levels"""
        try:
            # Use 15-minute data for volatility calculation
            data_15m = market_data.get('15m')
            if data_15m is None or data_15m.empty:
                # Fallback to original levels
                return signal.stop_loss_price, signal.target_price
            
            # Calculate ATR (Average True Range) for volatility
            high = data_15m['High']
            low = data_15m['Low']
            close = data_15m['Close']
            
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=14).mean().iloc[-1]
            
            # Calculate volatility-based levels
            volatility_multiplier = atr / signal.entry_price
            
            if volatility_multiplier < 0.02:  # Low volatility
                stop_multiplier = 1.5
                target_multiplier = 3.0
            elif volatility_multiplier > 0.05:  # High volatility
                stop_multiplier = 2.5
                target_multiplier = 4.0
            else:  # Medium volatility
                stop_multiplier = 2.0
                target_multiplier = 3.5
            
            # Calculate levels based on signal direction
            if signal.signal_type in [SignalType.MOMENTUM_BULLISH, SignalType.BREAKOUT_BULLISH]:
                stop_loss = signal.entry_price - (atr * stop_multiplier)
                take_profit = signal.entry_price + (atr * target_multiplier)
            else:
                stop_loss = signal.entry_price + (atr * stop_multiplier)
                take_profit = signal.entry_price - (atr * target_multiplier)
            
            return stop_loss, take_profit
            
        except Exception as e:
            logger.warning(f"Dynamic level calculation failed: {e}")
            return signal.stop_loss_price, signal.target_price
    
    def _calculate_enhanced_confidence(self, signal: TradingSignal, symbol: str,
                                     market_data: Dict) -> float:
        """Calculate enhanced confidence score"""
        base_confidence = signal.confidence
        
        # Factor 1: Volume strength (0-20% boost)
        volume_boost = self._get_volume_boost(symbol, market_data)
        
        # Factor 2: Trend strength (0-15% boost)
        trend_boost = self._get_trend_strength_boost(market_data)
        
        # Factor 3: Market alignment (0-10% boost)
        market_boost = self._get_market_alignment_boost(signal)
        
        # Factor 4: Historical performance (-10% to +15% adjustment)
        performance_adjustment = self._get_performance_adjustment(signal.signal_type)

        # Factor 5: News sentiment boost (-30% to +30% adjustment)
        sentiment_boost = self._get_sentiment_boost(signal, symbol)

        # Combine all factors
        enhanced_confidence = base_confidence + volume_boost + trend_boost + market_boost + performance_adjustment + sentiment_boost
        
        # Cap between 0.1 and 1.0
        return max(0.1, min(1.0, enhanced_confidence))
    
    def _get_volume_boost(self, symbol: str, market_data: Dict) -> float:
        """Calculate volume-based confidence boost"""
        try:
            data_5m = market_data.get('5m')
            if data_5m is None or data_5m.empty:
                return 0.0
            
            recent_volume = data_5m['Volume'].tail(20)
            avg_volume = recent_volume.mean()
            current_volume = data_5m['Volume'].iloc[-1]
            
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            if volume_ratio > 3.0:
                return 0.20  # Exceptional volume
            elif volume_ratio > 2.0:
                return 0.15  # High volume
            elif volume_ratio > 1.5:
                return 0.10  # Above average volume
            else:
                return 0.0   # Normal or low volume
                
        except Exception:
            return 0.0
    
    def _get_trend_strength_boost(self, market_data: Dict) -> float:
        """Calculate trend strength boost"""
        try:
            data_15m = market_data.get('15m')
            if data_15m is None or data_15m.empty:
                return 0.0
            
            # Calculate trend strength using ADX-like logic
            close = data_15m['Close']
            price_changes = close.pct_change().abs()
            trend_strength = price_changes.rolling(window=14).mean().iloc[-1]
            
            if trend_strength > 0.03:
                return 0.15  # Strong trend
            elif trend_strength > 0.02:
                return 0.10  # Moderate trend
            elif trend_strength > 0.01:
                return 0.05  # Weak trend
            else:
                return 0.0   # No clear trend
                
        except Exception:
            return 0.0
    
    def _get_market_alignment_boost(self, signal: TradingSignal) -> float:
        """Calculate market alignment boost"""
        # This would integrate with broader market analysis
        # For now, return a modest boost
        return 0.05
    
    def _get_performance_adjustment(self, signal_type: SignalType) -> float:
        """Adjust confidence based on historical signal performance"""
        if signal_type not in self.signal_performance_history:
            return 0.0
        
        performance = self.signal_performance_history[signal_type]
        win_rate = performance.get('win_rate', 0.5)
        
        if win_rate > 0.7:
            return 0.15   # Excellent performance
        elif win_rate > 0.6:
            return 0.10   # Good performance
        elif win_rate > 0.5:
            return 0.05   # Average performance
        elif win_rate > 0.4:
            return -0.05  # Below average
        else:
            return -0.10  # Poor performance

    def _get_sentiment_boost(self, signal: TradingSignal, symbol: str) -> float:
        """Calculate sentiment-based confidence boost"""
        try:
            # Get recent sentiment analysis
            sentiment = self.sentiment_analyzer.analyze_sentiment(symbol, hours_back=12)

            # Get sentiment boost from analyzer
            sentiment_boost = self.sentiment_analyzer.get_sentiment_signal_boost(sentiment, signal.signal_type)

            logger.debug(f"Sentiment boost for {symbol}: {sentiment_boost:.3f} "
                        f"(sentiment: {sentiment.overall_sentiment.name}, "
                        f"score: {sentiment.sentiment_score:.2f})")

            return sentiment_boost

        except Exception as e:
            logger.warning(f"Sentiment boost calculation failed for {symbol}: {e}")
            return 0.0
    
    def update_signal_performance(self, signal_type: SignalType, was_profitable: bool):
        """Update historical performance tracking"""
        if signal_type not in self.signal_performance_history:
            self.signal_performance_history[signal_type] = {
                'total_signals': 0,
                'profitable_signals': 0,
                'win_rate': 0.5
            }
        
        history = self.signal_performance_history[signal_type]
        history['total_signals'] += 1
        
        if was_profitable:
            history['profitable_signals'] += 1
        
        history['win_rate'] = history['profitable_signals'] / history['total_signals']
        
        logger.info(f"Updated {signal_type.value} performance: {history['win_rate']:.2%} win rate "
                   f"({history['profitable_signals']}/{history['total_signals']})")


# Factory function to create enhanced signal generator
def create_enhanced_signal_generator() -> EnhancedSignalGenerator:
    """Create and return an enhanced signal generator instance"""
    return EnhancedSignalGenerator()
