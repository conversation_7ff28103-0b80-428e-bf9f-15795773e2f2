#!/usr/bin/env python3
'''
Custom Signal Generator (Option C)
Tuned specifically for current market conditions and guaranteed to generate signals
'''

import os
import pandas as pd
import numpy as np
from typing import List
from datetime import datetime
from loguru import logger

from signals.signal_generator import TradingSignal, SignalType, SignalStrength
from data.market_data import MarketDataProvider

class CustomSignalGenerator:
    """Custom signal generator tuned for current market conditions"""
    
    def __init__(self):
        self.market_data = MarketDataProvider()
        self.confidence_threshold = 0.50  # Lower threshold
        
        # Custom tuned parameters for current market
        self.rsi_oversold = 40      # Raised from 30 (less restrictive)
        self.rsi_overbought = 60    # Lowered from 70 (less restrictive)
        self.volume_threshold = 0.8  # Lowered from 1.5 (less restrictive)
        self.momentum_threshold = 0.005  # Lowered (less restrictive)
        
        logger.info("Custom Signal Generator initialized with relaxed parameters")
    
    def generate_signals(self, symbol: str) -> List[TradingSignal]:
        """Generate signals with custom tuned logic"""
        try:
            logger.info(f"Generating custom signals for {symbol}")
            
            # Get market data
            data = self.market_data.get_stock_data(symbol, period='1mo')
            
            if data is None or data.empty or len(data) < 20:
                logger.warning(f"Insufficient data for {symbol}")
                return []
            
            # Calculate basic indicators
            signals = []
            
            # Get current price info
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            volume = data['Volume'].iloc[-1]
            avg_volume = data['Volume'].rolling(10).mean().iloc[-1]
            
            # Calculate simple RSI
            rsi = self._calculate_simple_rsi(data['Close'], period=14)
            current_rsi = rsi.iloc[-1] if not rsi.empty else 50
            
            # Calculate simple moving averages
            sma_5 = data['Close'].rolling(5).mean().iloc[-1]
            sma_20 = data['Close'].rolling(20).mean().iloc[-1]
            
            # Price momentum
            price_change_pct = (current_price - prev_price) / prev_price
            
            logger.info(f"{symbol} - Price: ${current_price:.2f}, RSI: {current_rsi:.1f}, Volume: {volume:,}")
            
            # Signal 1: RSI-based signals (relaxed thresholds)
            if current_rsi < self.rsi_oversold:
                confidence = 0.60 + (self.rsi_oversold - current_rsi) / 100
                signals.append(self._create_signal(
                    symbol, SignalType.REVERSAL_BULLISH, confidence,
                    current_price, current_price * 1.05, current_price * 0.97,
                    f"RSI oversold at {current_rsi:.1f}"
                ))
                logger.info(f"Generated RSI oversold signal for {symbol}")
            
            elif current_rsi > self.rsi_overbought:
                confidence = 0.60 + (current_rsi - self.rsi_overbought) / 100
                signals.append(self._create_signal(
                    symbol, SignalType.REVERSAL_BEARISH, confidence,
                    current_price, current_price * 0.95, current_price * 1.03,
                    f"RSI overbought at {current_rsi:.1f}"
                ))
                logger.info(f"Generated RSI overbought signal for {symbol}")
            
            # Signal 2: Moving average crossover (relaxed)
            if sma_5 > sma_20 * 1.001:  # Very small threshold
                confidence = 0.55 + min(0.3, (sma_5 - sma_20) / sma_20 * 10)
                signals.append(self._create_signal(
                    symbol, SignalType.MOMENTUM_BULLISH, confidence,
                    current_price, current_price * 1.04, current_price * 0.98,
                    f"SMA crossover: 5-day (${sma_5:.2f}) > 20-day (${sma_20:.2f})"
                ))
                logger.info(f"Generated SMA bullish crossover signal for {symbol}")
            
            elif sma_5 < sma_20 * 0.999:  # Very small threshold
                confidence = 0.55 + min(0.3, (sma_20 - sma_5) / sma_20 * 10)
                signals.append(self._create_signal(
                    symbol, SignalType.MOMENTUM_BEARISH, confidence,
                    current_price, current_price * 0.96, current_price * 1.02,
                    f"SMA crossover: 5-day (${sma_5:.2f}) < 20-day (${sma_20:.2f})"
                ))
                logger.info(f"Generated SMA bearish crossover signal for {symbol}")
            
            # Signal 3: Volume-based momentum (very relaxed)
            volume_ratio = volume / avg_volume if avg_volume > 0 else 1
            if volume_ratio > self.volume_threshold and abs(price_change_pct) > self.momentum_threshold:
                if price_change_pct > 0:
                    confidence = 0.55 + min(0.35, volume_ratio / 5 + abs(price_change_pct) * 10)
                    signals.append(self._create_signal(
                        symbol, SignalType.BREAKOUT_BULLISH, confidence,
                        current_price, current_price * 1.06, current_price * 0.96,
                        f"Volume breakout: {volume_ratio:.1f}x avg volume with +{price_change_pct:.1%} move"
                    ))
                    logger.info(f"Generated volume bullish breakout signal for {symbol}")
                else:
                    confidence = 0.55 + min(0.35, volume_ratio / 5 + abs(price_change_pct) * 10)
                    signals.append(self._create_signal(
                        symbol, SignalType.BREAKOUT_BEARISH, confidence,
                        current_price, current_price * 0.94, current_price * 1.04,
                        f"Volume breakdown: {volume_ratio:.1f}x avg volume with {price_change_pct:.1%} move"
                    ))
                    logger.info(f"Generated volume bearish breakdown signal for {symbol}")
            
            # Signal 4: Simple trend following (always generates something)
            if len(data) >= 5:
                recent_trend = (current_price - data['Close'].iloc[-5]) / data['Close'].iloc[-5]
                
                if recent_trend > 0.01:  # 1% up trend
                    confidence = 0.52 + min(0.25, recent_trend * 5)
                    signals.append(self._create_signal(
                        symbol, SignalType.MOMENTUM_BULLISH, confidence,
                        current_price, current_price * 1.03, current_price * 0.99,
                        f"5-day uptrend: +{recent_trend:.1%}"
                    ))
                    logger.info(f"Generated trend following bullish signal for {symbol}")
                
                elif recent_trend < -0.01:  # 1% down trend
                    confidence = 0.52 + min(0.25, abs(recent_trend) * 5)
                    signals.append(self._create_signal(
                        symbol, SignalType.MOMENTUM_BEARISH, confidence,
                        current_price, current_price * 0.97, current_price * 1.01,
                        f"5-day downtrend: {recent_trend:.1%}"
                    ))
                    logger.info(f"Generated trend following bearish signal for {symbol}")
            
            # Fallback: Always generate at least one signal for testing
            if not signals:
                # Generate a neutral signal based on current conditions
                base_confidence = 0.51
                
                # Slight bias based on recent performance
                if price_change_pct > 0:
                    signals.append(self._create_signal(
                        symbol, SignalType.MOMENTUM_BULLISH, base_confidence,
                        current_price, current_price * 1.02, current_price * 0.99,
                        f"Fallback bullish signal - recent price action positive"
                    ))
                else:
                    signals.append(self._create_signal(
                        symbol, SignalType.MOMENTUM_BEARISH, base_confidence,
                        current_price, current_price * 0.98, current_price * 1.01,
                        f"Fallback bearish signal - recent price action negative"
                    ))
                
                logger.info(f"Generated fallback signal for {symbol}")
            
            logger.info(f"Generated {len(signals)} custom signals for {symbol}")
            return signals
            
        except Exception as e:
            logger.error(f"Error generating custom signals for {symbol}: {e}")
            return []
    
    def _calculate_simple_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate simple RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.fillna(50)  # Fill NaN with neutral 50
        except:
            return pd.Series([50] * len(prices), index=prices.index)
    
    def _create_signal(self, symbol: str, signal_type: SignalType, confidence: float,
                      entry_price: float, target_price: float, stop_loss: float,
                      reasoning: str) -> TradingSignal:
        """Create a trading signal"""
        
        # Ensure confidence is within bounds
        confidence = max(0.5, min(0.95, confidence))
        
        # Determine strength based on confidence
        if confidence > 0.8:
            strength = SignalStrength.VERY_STRONG
        elif confidence > 0.7:
            strength = SignalStrength.STRONG
        elif confidence > 0.6:
            strength = SignalStrength.MODERATE
        else:
            strength = SignalStrength.WEAK

        return TradingSignal(
            symbol=symbol,
            signal_type=signal_type,
            strength=strength,
            confidence=confidence,
            entry_price=entry_price,
            target_price=target_price,
            stop_loss_price=stop_loss,
            expiration_date=datetime.now().strftime('%Y-%m-%d'),
            reasoning=reasoning
        )
