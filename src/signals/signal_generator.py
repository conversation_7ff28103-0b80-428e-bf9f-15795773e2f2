import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tu<PERSON>
from loguru import logger
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

from src.indicators.technical_indicators import TechnicalIndicators
from src.data.market_data import MarketDataProvider
from config.settings import (
    RSI_OVERSOLD, RSI_OVERBOUGHT, BREAKOUT_VOLUME_THRESHOLD,
    PROFIT_TARGET, STOP_LOSS
)


class SignalType(Enum):
    """Types of trading signals"""
    MOMENTUM_BULLISH = "momentum_bullish"
    MOMENTUM_BEARISH = "momentum_bearish"
    BREAKOUT_BULLISH = "breakout_bullish"
    BREAKOUT_BEARISH = "breakout_bearish"
    VOLATILITY_EXPANSION = "volatility_expansion"
    VOLATILITY_CONTRACTION = "volatility_contraction"
    EVENT_DRIVEN = "event_driven"
    REVERSAL_BULLISH = "reversal_bullish"
    REVERSAL_BEARISH = "reversal_bearish"


class SignalStrength(Enum):
    """Signal strength levels"""
    WEAK = 1
    MODERATE = 2
    STRONG = 3
    VERY_STRONG = 4


class TradingSignal:
    """Represents a trading signal with all relevant information"""
    
    def __init__(self, symbol: str, signal_type: SignalType, strength: SignalStrength,
                 confidence: float, entry_price: float, target_price: float,
                 stop_loss_price: float, expiration_date: str, option_type: str = None,
                 strike_price: float = None, reasoning: str = ""):
        self.symbol = symbol
        self.signal_type = signal_type
        self.strength = strength
        self.confidence = confidence
        self.entry_price = entry_price
        self.target_price = target_price
        self.stop_loss_price = stop_loss_price
        self.expiration_date = expiration_date
        self.option_type = option_type  # 'call' or 'put'
        self.strike_price = strike_price
        self.reasoning = reasoning
        self.timestamp = datetime.now()
        self.risk_reward_ratio = abs(target_price - entry_price) / abs(entry_price - stop_loss_price) if stop_loss_price != entry_price else 0
    
    def to_dict(self) -> Dict:
        """Convert signal to dictionary"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type.value,
            'strength': self.strength.value,
            'confidence': self.confidence,
            'entry_price': self.entry_price,
            'target_price': self.target_price,
            'stop_loss_price': self.stop_loss_price,
            'expiration_date': self.expiration_date,
            'option_type': self.option_type,
            'strike_price': self.strike_price,
            'reasoning': self.reasoning,
            'timestamp': self.timestamp.isoformat(),
            'risk_reward_ratio': self.risk_reward_ratio
        }


class SignalGenerator:
    """
    Advanced signal generation engine for options trading.
    Combines technical analysis, volume analysis, and market sentiment.
    """
    
    def __init__(self):
        self.technical_indicators = TechnicalIndicators()
        self.market_data = MarketDataProvider()
        self.signal_history = []
    
    def generate_signals(self, symbol: str, timeframe: str = "1d") -> List[TradingSignal]:
        """
        Generate all types of trading signals for a given symbol.
        
        Args:
            symbol: Stock symbol to analyze
            timeframe: Data timeframe (1d, 1h, etc.)
            
        Returns:
            List of TradingSignal objects
        """
        signals = []
        
        try:
            # Get comprehensive market data
            market_data = self.market_data.get_comprehensive_market_data(symbol)
            if not market_data or not market_data.get('historical_data'):
                logger.warning(f"Insufficient data for {symbol}")
                return signals
            
            # Convert historical data back to DataFrame
            historical_df = pd.DataFrame(market_data['historical_data'])
            if historical_df.empty:
                return signals
            
            # Calculate technical indicators
            indicators_df = self.technical_indicators.calculate_all_indicators(historical_df)
            if indicators_df.empty:
                return signals
            
            # Generate different types of signals
            momentum_signals = self._generate_momentum_signals(symbol, indicators_df, market_data)
            breakout_signals = self._generate_breakout_signals(symbol, indicators_df, market_data)
            volatility_signals = self._generate_volatility_signals(symbol, indicators_df, market_data)
            reversal_signals = self._generate_reversal_signals(symbol, indicators_df, market_data)
            event_signals = self._generate_event_driven_signals(symbol, market_data)
            
            # Combine all signals
            signals.extend(momentum_signals)
            signals.extend(breakout_signals)
            signals.extend(volatility_signals)
            signals.extend(reversal_signals)
            signals.extend(event_signals)
            
            # Filter and rank signals
            signals = self._filter_and_rank_signals(signals)
            
            # Store in history
            self.signal_history.extend(signals)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")
            return []
    
    def _generate_momentum_signals(self, symbol: str, indicators_df: pd.DataFrame, 
                                 market_data: Dict) -> List[TradingSignal]:
        """Generate momentum-based signals"""
        signals = []
        
        if indicators_df.empty:
            return signals
        
        latest = indicators_df.iloc[-1]
        current_price = market_data['price_data'].get('current_price', 0)
        
        # RSI Momentum Signal
        if latest.get('RSI', 50) < RSI_OVERSOLD and latest.get('MACD_Bullish', 0):
            confidence = 0.7 + (RSI_OVERSOLD - latest['RSI']) / 100
            target_price = current_price * (1 + PROFIT_TARGET)
            stop_price = current_price * (1 - STOP_LOSS)
            
            signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.MOMENTUM_BULLISH,
                strength=SignalStrength.STRONG,
                confidence=confidence,
                entry_price=current_price,
                target_price=target_price,
                stop_loss_price=stop_price,
                expiration_date=(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
                option_type='call',
                reasoning=f"RSI oversold ({latest['RSI']:.1f}) with MACD bullish crossover"
            )
            signals.append(signal)
        
        # Moving Average Crossover Signal
        if (latest.get('SMA_Cross_5_20', 0) and 
            latest.get('EMA_Cross_5_20', 0) and 
            latest.get('High_Volume', 0)):
            
            confidence = 0.65
            target_price = current_price * (1 + PROFIT_TARGET * 0.8)
            stop_price = current_price * (1 - STOP_LOSS * 0.8)
            
            signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.MOMENTUM_BULLISH,
                strength=SignalStrength.MODERATE,
                confidence=confidence,
                entry_price=current_price,
                target_price=target_price,
                stop_loss_price=stop_price,
                expiration_date=(datetime.now() + timedelta(days=5)).strftime('%Y-%m-%d'),
                option_type='call',
                reasoning="Multiple MA crossovers with high volume confirmation"
            )
            signals.append(signal)
        
        return signals
    
    def _generate_breakout_signals(self, symbol: str, indicators_df: pd.DataFrame,
                                 market_data: Dict) -> List[TradingSignal]:
        """Generate breakout-based signals"""
        signals = []
        
        if indicators_df.empty:
            return signals
        
        latest = indicators_df.iloc[-1]
        current_price = market_data['price_data'].get('current_price', 0)
        volume_analysis = market_data.get('volume_analysis', {})
        
        # High Volume Breakout
        if (latest.get('Breakout_High', 0) and 
            volume_analysis.get('unusual_volume', False) and
            latest.get('ADX_Strong_Trend', 0)):
            
            confidence = 0.8
            target_price = current_price * (1 + PROFIT_TARGET * 1.2)
            stop_price = current_price * (1 - STOP_LOSS * 0.6)
            
            signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.BREAKOUT_BULLISH,
                strength=SignalStrength.VERY_STRONG,
                confidence=confidence,
                entry_price=current_price,
                target_price=target_price,
                stop_loss_price=stop_price,
                expiration_date=(datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d'),
                option_type='call',
                reasoning=f"High volume breakout with {volume_analysis.get('volume_ratio', 0):.1f}x volume"
            )
            signals.append(signal)
        
        # Bollinger Band Breakout
        if (latest.get('BB_Position', 0.5) > 1.0 and 
            latest.get('Vol_Expansion', 0)):
            
            confidence = 0.7
            target_price = current_price * (1 + PROFIT_TARGET)
            stop_price = latest.get('BB_Middle', current_price * 0.95)
            
            signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.BREAKOUT_BULLISH,
                strength=SignalStrength.STRONG,
                confidence=confidence,
                entry_price=current_price,
                target_price=target_price,
                stop_loss_price=stop_price,
                expiration_date=(datetime.now() + timedelta(days=5)).strftime('%Y-%m-%d'),
                option_type='call',
                reasoning="Bollinger Band breakout with volatility expansion"
            )
            signals.append(signal)
        
        return signals
    
    def _generate_volatility_signals(self, symbol: str, indicators_df: pd.DataFrame,
                                   market_data: Dict) -> List[TradingSignal]:
        """Generate volatility-based signals"""
        signals = []
        
        if indicators_df.empty:
            return signals
        
        latest = indicators_df.iloc[-1]
        current_price = market_data['price_data'].get('current_price', 0)
        
        # Volatility Expansion Signal
        if (latest.get('Vol_Expansion', 0) and 
            latest.get('BB_Width', 0) > indicators_df['BB_Width'].rolling(20).mean().iloc[-1]):
            
            confidence = 0.6
            # For volatility plays, we might use straddles
            target_price = current_price * (1 + PROFIT_TARGET * 0.5)
            stop_price = current_price * (1 - STOP_LOSS * 0.5)
            
            signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.VOLATILITY_EXPANSION,
                strength=SignalStrength.MODERATE,
                confidence=confidence,
                entry_price=current_price,
                target_price=target_price,
                stop_loss_price=stop_price,
                expiration_date=(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
                reasoning="Volatility expansion detected - consider straddle strategy"
            )
            signals.append(signal)
        
        return signals
    
    def _generate_reversal_signals(self, symbol: str, indicators_df: pd.DataFrame,
                                 market_data: Dict) -> List[TradingSignal]:
        """Generate reversal-based signals"""
        signals = []
        
        if indicators_df.empty:
            return signals
        
        latest = indicators_df.iloc[-1]
        current_price = market_data['price_data'].get('current_price', 0)
        
        # Oversold Reversal
        if (latest.get('RSI', 50) < 25 and 
            latest.get('Williams_R', -50) < -80 and
            latest.get('Near_Support', 0)):
            
            confidence = 0.75
            target_price = current_price * (1 + PROFIT_TARGET * 0.8)
            stop_price = latest.get('Support_20', current_price * 0.95)
            
            signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.REVERSAL_BULLISH,
                strength=SignalStrength.STRONG,
                confidence=confidence,
                entry_price=current_price,
                target_price=target_price,
                stop_loss_price=stop_price,
                expiration_date=(datetime.now() + timedelta(days=10)).strftime('%Y-%m-%d'),
                option_type='call',
                reasoning="Multiple oversold indicators near support level"
            )
            signals.append(signal)
        
        return signals
    
    def _generate_event_driven_signals(self, symbol: str, market_data: Dict) -> List[TradingSignal]:
        """Generate event-driven signals"""
        signals = []
        
        # Check earnings calendar
        earnings_calendar = self.market_data.get_earnings_calendar()
        upcoming_earnings = [e for e in earnings_calendar if e.get('symbol') == symbol]
        
        if upcoming_earnings:
            current_price = market_data['price_data'].get('current_price', 0)
            confidence = 0.5  # Event-driven signals are inherently uncertain
            
            signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.EVENT_DRIVEN,
                strength=SignalStrength.MODERATE,
                confidence=confidence,
                entry_price=current_price,
                target_price=current_price * (1 + PROFIT_TARGET * 1.5),
                stop_loss_price=current_price * (1 - STOP_LOSS * 1.5),
                expiration_date=upcoming_earnings[0].get('reportDate', 
                    (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d')),
                reasoning=f"Upcoming earnings on {upcoming_earnings[0].get('reportDate')}"
            )
            signals.append(signal)
        
        return signals
    
    def _filter_and_rank_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """Filter and rank signals by quality and strength"""
        if not signals:
            return signals
        
        # Filter out low-confidence signals
        filtered_signals = [s for s in signals if s.confidence >= 0.5]
        
        # Sort by combined score (confidence * strength * risk_reward_ratio)
        def signal_score(signal):
            return (signal.confidence * signal.strength.value * 
                   min(signal.risk_reward_ratio, 3))  # Cap risk-reward at 3
        
        filtered_signals.sort(key=signal_score, reverse=True)
        
        # Return top 5 signals to avoid overtrading
        return filtered_signals[:5]
