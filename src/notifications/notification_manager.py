"""
Notification Manager for AWOT Trading Platform
Handles email, Telegram, and SMS notifications for trades, alerts, and system status
"""

import smtplib
import requests
import json
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime
from typing import Dict, List, Optional
from loguru import logger
import os
from enum import Enum

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass


class NotificationType(Enum):
    """Types of notifications"""
    TRADE_EXECUTED = "trade_executed"
    SIGNAL_GENERATED = "signal_generated"
    RISK_ALERT = "risk_alert"
    SYSTEM_STATUS = "system_status"
    ERROR = "error"
    PORTFOLIO_UPDATE = "portfolio_update"


class NotificationPriority(Enum):
    """Notification priority levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


class NotificationManager:
    """
    Comprehensive notification system for the AWOT trading platform.
    Supports email, Telegram, and SMS notifications with priority levels.
    """
    
    def __init__(self):
        # Email configuration
        self.email_enabled = False
        self.smtp_server = os.getenv('EMAIL_SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = int(os.getenv('EMAIL_SMTP_PORT', '587'))
        self.email_username = os.getenv('EMAIL_USERNAME')
        self.email_password = os.getenv('EMAIL_PASSWORD')
        self.email_from = os.getenv('EMAIL_FROM', self.email_username)
        self.email_to = os.getenv('EMAIL_TO', self.email_username)
        
        # Telegram configuration
        self.telegram_enabled = False
        self.telegram_bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        # SMS configuration (using Twilio)
        self.sms_enabled = False
        self.twilio_account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        self.twilio_auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        self.twilio_phone_number = os.getenv('TWILIO_PHONE_NUMBER')
        self.sms_to_number = os.getenv('SMS_TO_NUMBER')
        
        # Initialize services
        self._initialize_services()
        
        # Notification history
        self.notification_history = []
        
        # Priority thresholds
        self.priority_thresholds = {
            NotificationPriority.LOW: ['email'],
            NotificationPriority.MEDIUM: ['email', 'telegram'],
            NotificationPriority.HIGH: ['email', 'telegram'],
            NotificationPriority.CRITICAL: ['email', 'telegram', 'sms']
        }
    
    def _initialize_services(self):
        """Initialize notification services based on available configuration"""
        # Check email configuration
        if self.email_username and self.email_password:
            self.email_enabled = True
            logger.info("Email notifications enabled")
        else:
            logger.warning("Email notifications disabled - missing configuration")
        
        # Check Telegram configuration
        if self.telegram_bot_token and self.telegram_chat_id:
            self.telegram_enabled = True
            logger.info("Telegram notifications enabled")
        else:
            logger.warning("Telegram notifications disabled - missing configuration")
        
        # Check SMS configuration
        if self.twilio_account_sid and self.twilio_auth_token and self.twilio_phone_number:
            self.sms_enabled = True
            logger.info("SMS notifications enabled")
        else:
            logger.warning("SMS notifications disabled - missing configuration")
    
    def send_notification(self, 
                         notification_type: NotificationType,
                         title: str,
                         message: str,
                         priority: NotificationPriority = NotificationPriority.MEDIUM,
                         data: Optional[Dict] = None) -> bool:
        """
        Send a notification through appropriate channels based on priority.
        
        Args:
            notification_type: Type of notification
            title: Notification title
            message: Notification message
            priority: Priority level
            data: Additional data for the notification
            
        Returns:
            True if at least one notification was sent successfully
        """
        try:
            # Get channels for this priority level
            channels = self.priority_thresholds.get(priority, ['email'])
            
            # Format message with emoji based on type
            formatted_title, formatted_message = self._format_message(
                notification_type, title, message, data
            )
            
            success_count = 0
            
            # Send through each channel
            for channel in channels:
                try:
                    if channel == 'email' and self.email_enabled:
                        if self._send_email(formatted_title, formatted_message):
                            success_count += 1
                    
                    elif channel == 'telegram' and self.telegram_enabled:
                        if self._send_telegram(formatted_title, formatted_message):
                            success_count += 1
                    
                    elif channel == 'sms' and self.sms_enabled:
                        # SMS messages should be shorter
                        sms_message = f"{formatted_title}: {message[:100]}..."
                        if self._send_sms(sms_message):
                            success_count += 1
                
                except Exception as e:
                    logger.error(f"Error sending {channel} notification: {e}")
            
            # Log notification
            self._log_notification(notification_type, title, message, priority, success_count > 0)
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
            return False
    
    def _format_message(self, notification_type: NotificationType, title: str, 
                       message: str, data: Optional[Dict] = None) -> tuple:
        """Format message with appropriate emoji and structure"""
        
        # Emoji mapping
        emoji_map = {
            NotificationType.TRADE_EXECUTED: "💰",
            NotificationType.SIGNAL_GENERATED: "🎯",
            NotificationType.RISK_ALERT: "⚠️",
            NotificationType.SYSTEM_STATUS: "🔧",
            NotificationType.ERROR: "❌",
            NotificationType.PORTFOLIO_UPDATE: "📊"
        }
        
        emoji = emoji_map.get(notification_type, "📢")
        formatted_title = f"{emoji} {title}"
        
        # Add timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"{message}\n\n🕐 Time: {timestamp}"
        
        # Add additional data if provided
        if data:
            formatted_message += "\n\n📋 Details:"
            for key, value in data.items():
                formatted_message += f"\n• {key}: {value}"
        
        return formatted_title, formatted_message
    
    def _send_email(self, title: str, message: str) -> bool:
        """Send email notification"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.email_from
            msg['To'] = self.email_to
            msg['Subject'] = f"AWOT Alert: {title}"
            
            # Add body
            msg.attach(MIMEText(message, 'plain'))
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.email_username, self.email_password)
                server.send_message(msg)
            
            logger.info(f"Email notification sent: {title}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False
    
    def _send_telegram(self, title: str, message: str) -> bool:
        """Send Telegram notification"""
        try:
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            
            # Combine title and message
            full_message = f"*{title}*\n\n{message}"
            
            payload = {
                'chat_id': self.telegram_chat_id,
                'text': full_message,
                'parse_mode': 'Markdown'
            }
            
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            logger.info(f"Telegram notification sent: {title}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return False
    
    def _send_sms(self, message: str) -> bool:
        """Send SMS notification using Twilio"""
        try:
            # This would require the Twilio library
            # For now, we'll just log that SMS would be sent
            logger.info(f"SMS notification would be sent: {message[:50]}...")
            
            # TODO: Implement actual Twilio SMS sending
            # from twilio.rest import Client
            # client = Client(self.twilio_account_sid, self.twilio_auth_token)
            # message = client.messages.create(
            #     body=message,
            #     from_=self.twilio_phone_number,
            #     to=self.sms_to_number
            # )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send SMS: {e}")
            return False
    
    def _log_notification(self, notification_type: NotificationType, title: str,
                         message: str, priority: NotificationPriority, success: bool):
        """Log notification to history"""
        notification_record = {
            'timestamp': datetime.now(),
            'type': notification_type.value,
            'title': title,
            'message': message,
            'priority': priority.value,
            'success': success
        }
        
        self.notification_history.append(notification_record)
        
        # Keep only last 100 notifications
        if len(self.notification_history) > 100:
            self.notification_history = self.notification_history[-100:]
    
    def send_trade_notification(self, trade_data: Dict) -> bool:
        """Send notification for executed trade"""
        title = f"Trade Executed: {trade_data.get('symbol', 'Unknown')}"
        
        action = trade_data.get('action', 'Unknown')
        symbol = trade_data.get('symbol', 'Unknown')
        quantity = trade_data.get('quantity', 0)
        price = trade_data.get('price', 0)
        option_type = trade_data.get('type', 'Unknown')
        
        message = f"""
{action} {quantity} {symbol} {option_type} contracts at ${price:.2f}

Total Value: ${quantity * price * 100:.2f}
Status: {trade_data.get('status', 'Unknown')}
"""
        
        return self.send_notification(
            NotificationType.TRADE_EXECUTED,
            title,
            message.strip(),
            NotificationPriority.HIGH,
            trade_data
        )
    
    def send_signal_notification(self, signal_data: Dict) -> bool:
        """Send notification for new trading signal"""
        title = f"New Signal: {signal_data.get('symbol', 'Unknown')}"
        
        symbol = signal_data.get('symbol', 'Unknown')
        signal_type = signal_data.get('type', 'Unknown')
        confidence = signal_data.get('confidence', 0)
        entry_price = signal_data.get('entry_price', 0)
        target_price = signal_data.get('target_price', 0)
        
        message = f"""
Signal Type: {signal_type}
Confidence: {confidence:.1%}
Entry Price: ${entry_price:.2f}
Target Price: ${target_price:.2f}
Potential Return: {((target_price - entry_price) / entry_price * 100):.1f}%
"""
        
        priority = NotificationPriority.HIGH if confidence > 0.8 else NotificationPriority.MEDIUM
        
        return self.send_notification(
            NotificationType.SIGNAL_GENERATED,
            title,
            message.strip(),
            priority,
            signal_data
        )
    
    def send_risk_alert(self, risk_data: Dict) -> bool:
        """Send risk alert notification"""
        title = f"Risk Alert: {risk_data.get('alert_type', 'Unknown')}"
        
        message = risk_data.get('message', 'Risk threshold exceeded')
        
        # Determine priority based on risk level
        risk_level = risk_data.get('risk_level', 'medium').lower()
        if risk_level == 'critical':
            priority = NotificationPriority.CRITICAL
        elif risk_level == 'high':
            priority = NotificationPriority.HIGH
        else:
            priority = NotificationPriority.MEDIUM
        
        return self.send_notification(
            NotificationType.RISK_ALERT,
            title,
            message,
            priority,
            risk_data
        )
    
    def send_portfolio_update(self, portfolio_data: Dict) -> bool:
        """Send daily portfolio update"""
        title = "Daily Portfolio Update"
        
        total_value = portfolio_data.get('total_value', 0)
        daily_pnl = portfolio_data.get('daily_pnl', 0)
        total_return = portfolio_data.get('total_return', 0)
        active_positions = len(portfolio_data.get('positions', []))
        
        message = f"""
Portfolio Value: ${total_value:,.2f}
Daily P&L: ${daily_pnl:+,.2f}
Total Return: {total_return:+.2%}
Active Positions: {active_positions}
"""
        
        return self.send_notification(
            NotificationType.PORTFOLIO_UPDATE,
            title,
            message.strip(),
            NotificationPriority.LOW,
            portfolio_data
        )
    
    def send_system_status(self, status: str, details: str = "") -> bool:
        """Send system status notification"""
        title = f"System Status: {status}"
        message = details if details else f"Trading system status changed to: {status}"
        
        priority = NotificationPriority.HIGH if status.lower() in ['error', 'stopped'] else NotificationPriority.MEDIUM
        
        return self.send_notification(
            NotificationType.SYSTEM_STATUS,
            title,
            message,
            priority
        )
    
    def get_notification_history(self, limit: int = 50) -> List[Dict]:
        """Get recent notification history"""
        return self.notification_history[-limit:]
    
    def test_notifications(self) -> Dict[str, bool]:
        """Test all notification channels"""
        results = {}
        
        test_title = "AWOT Test Notification"
        test_message = "This is a test notification from the AWOT trading platform."
        
        # Test email
        if self.email_enabled:
            results['email'] = self._send_email(test_title, test_message)
        else:
            results['email'] = False
        
        # Test Telegram
        if self.telegram_enabled:
            results['telegram'] = self._send_telegram(test_title, test_message)
        else:
            results['telegram'] = False
        
        # Test SMS
        if self.sms_enabled:
            results['sms'] = self._send_sms(f"{test_title}: {test_message}")
        else:
            results['sms'] = False
        
        return results
