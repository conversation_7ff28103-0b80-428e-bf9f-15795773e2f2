import pandas as pd
import numpy as np
import ta
from typing import Dict, List, Optional, Tuple
from loguru import logger


class TechnicalIndicators:
    """
    Comprehensive technical indicators calculator using the ta library.
    Provides all essential indicators for options trading strategies.
    """
    
    def __init__(self):
        self.indicators_cache = {}
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all technical indicators for the given price data.
        
        Args:
            data: DataFrame with OHLCV data (Open, High, Low, Close, Volume)
            
        Returns:
            DataFrame with all calculated indicators
        """
        if data.empty:
            logger.warning("Empty data provided to calculate_all_indicators")
            return pd.DataFrame()
        
        try:
            # Create a copy to avoid modifying original data
            df = data.copy()
            
            # Ensure required columns exist
            required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in df.columns for col in required_cols):
                logger.error(f"Missing required columns. Need: {required_cols}")
                return pd.DataFrame()
            
            # Moving Averages
            df = self._add_moving_averages(df)
            
            # Momentum Indicators
            df = self._add_momentum_indicators(df)
            
            # Volatility Indicators
            df = self._add_volatility_indicators(df)
            
            # Volume Indicators
            df = self._add_volume_indicators(df)
            
            # Trend Indicators
            df = self._add_trend_indicators(df)
            
            # Custom Options-Specific Indicators
            df = self._add_options_indicators(df)
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return pd.DataFrame()
    
    def _add_moving_averages(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add various moving averages"""
        # Simple Moving Averages
        df['SMA_5'] = ta.trend.sma_indicator(df['Close'], window=5)
        df['SMA_10'] = ta.trend.sma_indicator(df['Close'], window=10)
        df['SMA_20'] = ta.trend.sma_indicator(df['Close'], window=20)
        df['SMA_50'] = ta.trend.sma_indicator(df['Close'], window=50)
        df['SMA_200'] = ta.trend.sma_indicator(df['Close'], window=200)
        
        # Exponential Moving Averages
        df['EMA_5'] = ta.trend.ema_indicator(df['Close'], window=5)
        df['EMA_10'] = ta.trend.ema_indicator(df['Close'], window=10)
        df['EMA_20'] = ta.trend.ema_indicator(df['Close'], window=20)
        df['EMA_50'] = ta.trend.ema_indicator(df['Close'], window=50)
        
        # Moving Average Crossover Signals
        df['SMA_Cross_5_20'] = np.where(df['SMA_5'] > df['SMA_20'], 1, 0)
        df['SMA_Cross_20_50'] = np.where(df['SMA_20'] > df['SMA_50'], 1, 0)
        df['EMA_Cross_5_20'] = np.where(df['EMA_5'] > df['EMA_20'], 1, 0)
        
        return df
    
    def _add_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum-based indicators"""
        # RSI (Relative Strength Index)
        df['RSI'] = ta.momentum.rsi(df['Close'], window=14)
        df['RSI_Oversold'] = np.where(df['RSI'] < 30, 1, 0)
        df['RSI_Overbought'] = np.where(df['RSI'] > 70, 1, 0)
        
        # MACD (Moving Average Convergence Divergence)
        macd = ta.trend.MACD(df['Close'])
        df['MACD'] = macd.macd()
        df['MACD_Signal'] = macd.macd_signal()
        df['MACD_Histogram'] = macd.macd_diff()
        df['MACD_Bullish'] = np.where(df['MACD'] > df['MACD_Signal'], 1, 0)
        
        # Stochastic Oscillator
        stoch = ta.momentum.StochasticOscillator(df['High'], df['Low'], df['Close'])
        df['Stoch_K'] = stoch.stoch()
        df['Stoch_D'] = stoch.stoch_signal()
        
        # Williams %R
        df['Williams_R'] = ta.momentum.williams_r(df['High'], df['Low'], df['Close'])
        
        # Rate of Change
        df['ROC'] = ta.momentum.roc(df['Close'], window=12)
        
        return df
    
    def _add_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based indicators"""
        # Bollinger Bands
        bb = ta.volatility.BollingerBands(df['Close'])
        df['BB_Upper'] = bb.bollinger_hband()
        df['BB_Middle'] = bb.bollinger_mavg()
        df['BB_Lower'] = bb.bollinger_lband()
        df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
        df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
        
        # Average True Range (ATR)
        df['ATR'] = ta.volatility.average_true_range(df['High'], df['Low'], df['Close'])
        df['ATR_Percent'] = df['ATR'] / df['Close'] * 100
        
        # Keltner Channels
        kc = ta.volatility.KeltnerChannel(df['High'], df['Low'], df['Close'])
        df['KC_Upper'] = kc.keltner_channel_hband()
        df['KC_Middle'] = kc.keltner_channel_mband()
        df['KC_Lower'] = kc.keltner_channel_lband()
        
        # Donchian Channels
        dc = ta.volatility.DonchianChannel(df['High'], df['Low'], df['Close'])
        df['DC_Upper'] = dc.donchian_channel_hband()
        df['DC_Lower'] = dc.donchian_channel_lband()
        df['DC_Middle'] = dc.donchian_channel_mband()
        
        return df
    
    def _add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based indicators"""
        # Volume Moving Averages
        df['Volume_SMA_20'] = ta.trend.sma_indicator(df['Volume'], window=20)
        df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA_20']
        df['High_Volume'] = np.where(df['Volume_Ratio'] > 2.0, 1, 0)
        
        # On-Balance Volume (OBV)
        df['OBV'] = ta.volume.on_balance_volume(df['Close'], df['Volume'])
        
        # Volume Price Trend (VPT)
        df['VPT'] = ta.volume.volume_price_trend(df['Close'], df['Volume'])
        
        # Accumulation/Distribution Line
        df['ADL'] = ta.volume.acc_dist_index(df['High'], df['Low'], df['Close'], df['Volume'])
        
        # Chaikin Money Flow
        df['CMF'] = ta.volume.chaikin_money_flow(df['High'], df['Low'], df['Close'], df['Volume'])
        
        return df
    
    def _add_trend_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add trend-based indicators"""
        # Average Directional Index (ADX)
        df['ADX'] = ta.trend.adx(df['High'], df['Low'], df['Close'])
        df['ADX_Strong_Trend'] = np.where(df['ADX'] > 25, 1, 0)
        
        # Parabolic SAR
        df['PSAR'] = ta.trend.psar_down(df['High'], df['Low'], df['Close'])
        
        # Commodity Channel Index (CCI)
        df['CCI'] = ta.trend.cci(df['High'], df['Low'], df['Close'])
        
        # Detrended Price Oscillator (DPO)
        df['DPO'] = ta.trend.dpo(df['Close'])
        
        return df
    
    def _add_options_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add custom indicators specific to options trading"""
        # Price momentum for breakout detection
        df['Price_Change_1D'] = df['Close'].pct_change(1)
        df['Price_Change_5D'] = df['Close'].pct_change(5)
        df['Price_Change_20D'] = df['Close'].pct_change(20)
        
        # Volatility expansion signals
        df['ATR_Expansion'] = df['ATR'] / df['ATR'].rolling(20).mean()
        df['Vol_Expansion'] = np.where(df['ATR_Expansion'] > 1.5, 1, 0)
        
        # Breakout signals
        df['Breakout_High'] = np.where(df['Close'] > df['High'].rolling(20).max().shift(1), 1, 0)
        df['Breakout_Low'] = np.where(df['Close'] < df['Low'].rolling(20).min().shift(1), 1, 0)
        
        # Support/Resistance levels
        df['Resistance_20'] = df['High'].rolling(20).max()
        df['Support_20'] = df['Low'].rolling(20).min()
        df['Near_Resistance'] = np.where(df['Close'] / df['Resistance_20'] > 0.98, 1, 0)
        df['Near_Support'] = np.where(df['Close'] / df['Support_20'] < 1.02, 1, 0)
        
        return df
    
    def get_signal_strength(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate overall signal strength for different trading strategies.
        
        Returns:
            Dictionary with signal strengths for different strategies
        """
        if data.empty:
            return {}
        
        latest = data.iloc[-1]
        
        # Momentum signal strength
        momentum_signals = [
            latest.get('RSI_Oversold', 0),
            latest.get('RSI_Overbought', 0),
            latest.get('MACD_Bullish', 0),
            latest.get('SMA_Cross_5_20', 0),
            latest.get('EMA_Cross_5_20', 0)
        ]
        momentum_strength = sum(momentum_signals) / len(momentum_signals)
        
        # Volatility signal strength
        volatility_signals = [
            latest.get('Vol_Expansion', 0),
            latest.get('High_Volume', 0),
            min(latest.get('ATR_Expansion', 1), 2) / 2  # Normalize to 0-1
        ]
        volatility_strength = sum(volatility_signals) / len(volatility_signals)
        
        # Breakout signal strength
        breakout_signals = [
            latest.get('Breakout_High', 0),
            latest.get('Breakout_Low', 0),
            latest.get('ADX_Strong_Trend', 0)
        ]
        breakout_strength = sum(breakout_signals) / len(breakout_signals)
        
        return {
            'momentum': momentum_strength,
            'volatility': volatility_strength,
            'breakout': breakout_strength,
            'overall': (momentum_strength + volatility_strength + breakout_strength) / 3
        }
    
    def detect_patterns(self, data: pd.DataFrame) -> List[Dict]:
        """
        Detect common chart patterns relevant for options trading.
        
        Returns:
            List of detected patterns with confidence scores
        """
        patterns = []
        
        if len(data) < 20:
            return patterns
        
        latest = data.tail(20)
        
        # Double bottom pattern
        if self._detect_double_bottom(latest):
            patterns.append({
                'pattern': 'double_bottom',
                'confidence': 0.7,
                'signal': 'bullish',
                'timeframe': '20_days'
            })
        
        # Double top pattern
        if self._detect_double_top(latest):
            patterns.append({
                'pattern': 'double_top',
                'confidence': 0.7,
                'signal': 'bearish',
                'timeframe': '20_days'
            })
        
        # Ascending triangle
        if self._detect_ascending_triangle(latest):
            patterns.append({
                'pattern': 'ascending_triangle',
                'confidence': 0.6,
                'signal': 'bullish',
                'timeframe': '20_days'
            })
        
        return patterns
    
    def _detect_double_bottom(self, data: pd.DataFrame) -> bool:
        """Detect double bottom pattern"""
        lows = data['Low'].values
        if len(lows) < 10:
            return False
        
        # Find local minima
        local_mins = []
        for i in range(2, len(lows) - 2):
            if lows[i] < lows[i-1] and lows[i] < lows[i+1] and lows[i] < lows[i-2] and lows[i] < lows[i+2]:
                local_mins.append((i, lows[i]))
        
        if len(local_mins) >= 2:
            # Check if two recent lows are similar
            last_two = local_mins[-2:]
            price_diff = abs(last_two[0][1] - last_two[1][1]) / last_two[0][1]
            return price_diff < 0.02  # Within 2%
        
        return False
    
    def _detect_double_top(self, data: pd.DataFrame) -> bool:
        """Detect double top pattern"""
        highs = data['High'].values
        if len(highs) < 10:
            return False
        
        # Find local maxima
        local_maxs = []
        for i in range(2, len(highs) - 2):
            if highs[i] > highs[i-1] and highs[i] > highs[i+1] and highs[i] > highs[i-2] and highs[i] > highs[i+2]:
                local_maxs.append((i, highs[i]))
        
        if len(local_maxs) >= 2:
            # Check if two recent highs are similar
            last_two = local_maxs[-2:]
            price_diff = abs(last_two[0][1] - last_two[1][1]) / last_two[0][1]
            return price_diff < 0.02  # Within 2%
        
        return False
    
    def _detect_ascending_triangle(self, data: pd.DataFrame) -> bool:
        """Detect ascending triangle pattern"""
        highs = data['High'].values
        lows = data['Low'].values
        
        if len(highs) < 10:
            return False
        
        # Check for horizontal resistance (similar highs)
        recent_highs = highs[-10:]
        max_high = max(recent_highs)
        resistance_touches = sum(1 for h in recent_highs if abs(h - max_high) / max_high < 0.01)
        
        # Check for ascending support (higher lows)
        recent_lows = lows[-10:]
        ascending_lows = all(recent_lows[i] >= recent_lows[i-1] * 0.99 for i in range(1, len(recent_lows)))
        
        return resistance_touches >= 2 and ascending_lows
