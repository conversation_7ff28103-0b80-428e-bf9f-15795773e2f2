import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
from loguru import logger
from datetime import datetime, timedelta
from enum import Enum

from signals.signal_generator import TradingSignal, SignalType, SignalStrength
from data.market_data import MarketDataProvider
# Configuration with fallback values
INITIAL_CAPITAL = 50000
RISK_PER_TRADE = 0.10
MAX_POSITIONS = 3

try:
    from config.settings import INITIAL_CAPITAL, RISK_PER_TRADE, MAX_POSITIONS
except ImportError:
    pass  # Use fallback values defined above


class StrategyType(Enum):
    """Types of trading strategies"""
    MOMENTUM = "momentum"
    BREAKOUT = "breakout"
    VOLATILITY = "volatility"
    REVERSAL = "reversal"
    EVENT_DRIVEN = "event_driven"


class PositionType(Enum):
    """Types of option positions"""
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    SHORT_CALL = "short_call"
    SHORT_PUT = "short_put"
    STRADDLE = "straddle"
    STRANGLE = "strangle"
    IRON_CONDOR = "iron_condor"
    BUTTERFLY = "butterfly"


class Position:
    """Represents an options position"""
    
    def __init__(self, symbol: str, position_type: PositionType, entry_price: float,
                 quantity: int, strike_price: float, expiration_date: str,
                 entry_time: datetime = None, stop_loss: float = None,
                 take_profit: float = None):
        self.symbol = symbol
        self.position_type = position_type
        self.entry_price = entry_price
        self.quantity = quantity
        self.strike_price = strike_price
        self.expiration_date = expiration_date
        self.entry_time = entry_time or datetime.now()
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.current_price = entry_price
        self.unrealized_pnl = 0.0
        self.is_open = True
        self.exit_price = None
        self.exit_time = None
        self.realized_pnl = 0.0
    
    def update_price(self, current_price: float):
        """Update current price and calculate unrealized P&L"""
        self.current_price = current_price
        if self.is_open:
            self.unrealized_pnl = (current_price - self.entry_price) * self.quantity
    
    def close_position(self, exit_price: float, exit_time: datetime = None):
        """Close the position and calculate realized P&L"""
        self.exit_price = exit_price
        self.exit_time = exit_time or datetime.now()
        self.realized_pnl = (exit_price - self.entry_price) * self.quantity
        self.is_open = False
    
    def to_dict(self) -> Dict:
        """Convert position to dictionary"""
        return {
            'symbol': self.symbol,
            'position_type': self.position_type.value,
            'entry_price': self.entry_price,
            'current_price': self.current_price,
            'quantity': self.quantity,
            'strike_price': self.strike_price,
            'expiration_date': self.expiration_date,
            'entry_time': self.entry_time.isoformat(),
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'is_open': self.is_open,
            'exit_price': self.exit_price,
            'exit_time': self.exit_time.isoformat() if self.exit_time else None
        }


class BaseStrategy(ABC):
    """Base class for all trading strategies"""
    
    def __init__(self, name: str, strategy_type: StrategyType):
        self.name = name
        self.strategy_type = strategy_type
        self.market_data = MarketDataProvider()
        self.positions = []
        self.capital = INITIAL_CAPITAL
        self.available_capital = INITIAL_CAPITAL
        self.max_risk_per_trade = RISK_PER_TRADE
        self.max_positions = MAX_POSITIONS
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }
    
    @abstractmethod
    def analyze_signal(self, signal: TradingSignal) -> Dict:
        """Analyze a trading signal and determine if it fits the strategy"""
        pass
    
    @abstractmethod
    def calculate_position_size(self, signal: TradingSignal, current_price: float) -> int:
        """Calculate the appropriate position size for a signal"""
        pass
    
    @abstractmethod
    def determine_entry_criteria(self, signal: TradingSignal) -> bool:
        """Determine if entry criteria are met for a signal"""
        pass
    
    @abstractmethod
    def determine_exit_criteria(self, position: Position, current_data: Dict) -> bool:
        """Determine if exit criteria are met for a position"""
        pass
    
    def execute_signal(self, signal: TradingSignal) -> Optional[Position]:
        """Execute a trading signal if it meets strategy criteria"""
        try:
            # Check if signal fits strategy
            analysis = self.analyze_signal(signal)
            if not analysis.get('suitable', False):
                logger.info(f"Signal for {signal.symbol} not suitable for {self.name}")
                return None
            
            # Check entry criteria
            if not self.determine_entry_criteria(signal):
                logger.info(f"Entry criteria not met for {signal.symbol}")
                return None
            
            # Check position limits
            if len([p for p in self.positions if p.is_open]) >= self.max_positions:
                logger.info(f"Maximum positions ({self.max_positions}) reached")
                return None
            
            # Calculate position size
            position_size = self.calculate_position_size(signal, signal.entry_price)
            if position_size <= 0:
                logger.info(f"Position size too small for {signal.symbol}")
                return None
            
            # Create position
            position = self._create_position(signal, position_size)
            if position:
                self.positions.append(position)
                self._update_capital(position)
                logger.info(f"Opened position: {position.symbol} {position.position_type.value}")
            
            return position
            
        except Exception as e:
            logger.error(f"Error executing signal for {signal.symbol}: {e}")
            return None
    
    def _create_position(self, signal: TradingSignal, quantity: int) -> Optional[Position]:
        """Create a position from a signal"""
        try:
            # Determine position type based on signal
            if signal.signal_type in [SignalType.MOMENTUM_BULLISH, SignalType.BREAKOUT_BULLISH, 
                                    SignalType.REVERSAL_BULLISH]:
                position_type = PositionType.LONG_CALL
            elif signal.signal_type in [SignalType.MOMENTUM_BEARISH, SignalType.BREAKOUT_BEARISH,
                                      SignalType.REVERSAL_BEARISH]:
                position_type = PositionType.LONG_PUT
            elif signal.signal_type == SignalType.VOLATILITY_EXPANSION:
                position_type = PositionType.STRADDLE
            else:
                position_type = PositionType.LONG_CALL  # Default
            
            position = Position(
                symbol=signal.symbol,
                position_type=position_type,
                entry_price=signal.entry_price,
                quantity=quantity,
                strike_price=signal.strike_price or signal.entry_price,
                expiration_date=signal.expiration_date,
                stop_loss=signal.stop_loss_price,
                take_profit=signal.target_price
            )
            
            return position
            
        except Exception as e:
            logger.error(f"Error creating position: {e}")
            return None
    
    def _update_capital(self, position: Position):
        """Update available capital after opening a position"""
        position_cost = position.entry_price * position.quantity
        self.available_capital -= position_cost
    
    def update_positions(self):
        """Update all open positions with current market data"""
        for position in self.positions:
            if position.is_open:
                try:
                    # Get current option price (simplified - in reality would need options pricing)
                    current_data = self.market_data.get_real_time_data(position.symbol)
                    current_price = current_data.get('current_price', position.current_price)
                    
                    # Update position
                    position.update_price(current_price)
                    
                    # Check exit criteria
                    if self.determine_exit_criteria(position, current_data):
                        self._close_position(position, current_price)
                        
                except Exception as e:
                    logger.error(f"Error updating position {position.symbol}: {e}")
    
    def _close_position(self, position: Position, exit_price: float):
        """Close a position and update metrics"""
        position.close_position(exit_price)
        self.available_capital += exit_price * position.quantity
        self._update_performance_metrics(position)
        logger.info(f"Closed position: {position.symbol} P&L: {position.realized_pnl:.2f}")
    
    def _update_performance_metrics(self, position: Position):
        """Update strategy performance metrics"""
        self.performance_metrics['total_trades'] += 1
        self.performance_metrics['total_pnl'] += position.realized_pnl
        
        if position.realized_pnl > 0:
            self.performance_metrics['winning_trades'] += 1
            self.performance_metrics['avg_win'] = (
                (self.performance_metrics['avg_win'] * (self.performance_metrics['winning_trades'] - 1) + 
                 position.realized_pnl) / self.performance_metrics['winning_trades']
            )
        else:
            self.performance_metrics['losing_trades'] += 1
            self.performance_metrics['avg_loss'] = (
                (self.performance_metrics['avg_loss'] * (self.performance_metrics['losing_trades'] - 1) + 
                 position.realized_pnl) / self.performance_metrics['losing_trades']
            )
        
        # Update win rate
        if self.performance_metrics['total_trades'] > 0:
            self.performance_metrics['win_rate'] = (
                self.performance_metrics['winning_trades'] / self.performance_metrics['total_trades']
            )
    
    def get_portfolio_summary(self) -> Dict:
        """Get current portfolio summary"""
        open_positions = [p for p in self.positions if p.is_open]
        total_unrealized_pnl = sum(p.unrealized_pnl for p in open_positions)
        total_realized_pnl = sum(p.realized_pnl for p in self.positions if not p.is_open)
        
        return {
            'strategy_name': self.name,
            'total_capital': self.capital,
            'available_capital': self.available_capital,
            'open_positions': len(open_positions),
            'total_positions': len(self.positions),
            'unrealized_pnl': total_unrealized_pnl,
            'realized_pnl': total_realized_pnl,
            'total_pnl': total_unrealized_pnl + total_realized_pnl,
            'performance_metrics': self.performance_metrics
        }


class MomentumStrategy(BaseStrategy):
    """Strategy focused on momentum-based signals"""
    
    def __init__(self):
        super().__init__("Momentum Strategy", StrategyType.MOMENTUM)
        self.min_rsi_threshold = 30
        self.max_rsi_threshold = 70
        self.min_volume_ratio = 1.5
    
    def analyze_signal(self, signal: TradingSignal) -> Dict:
        """Analyze if signal fits momentum strategy"""
        suitable = signal.signal_type in [
            SignalType.MOMENTUM_BULLISH, 
            SignalType.MOMENTUM_BEARISH
        ]
        
        confidence_adjustment = 0
        if signal.confidence > 0.7:
            confidence_adjustment += 0.1
        
        return {
            'suitable': suitable,
            'adjusted_confidence': signal.confidence + confidence_adjustment,
            'reasoning': "Momentum signal analysis"
        }
    
    def calculate_position_size(self, signal: TradingSignal, current_price: float) -> int:
        """Calculate position size based on risk management"""
        max_risk = self.available_capital * self.max_risk_per_trade
        risk_per_contract = abs(current_price - signal.stop_loss_price)
        
        if risk_per_contract <= 0:
            return 0
        
        position_size = int(max_risk / risk_per_contract)
        return max(1, min(position_size, 10))  # Between 1 and 10 contracts
    
    def determine_entry_criteria(self, signal: TradingSignal) -> bool:
        """Check if entry criteria are met"""
        return (signal.confidence >= 0.6 and 
                signal.strength.value >= 2 and
                signal.risk_reward_ratio >= 1.5)
    
    def determine_exit_criteria(self, position: Position, current_data: Dict) -> bool:
        """Check if exit criteria are met"""
        current_price = current_data.get('current_price', position.current_price)
        
        # Stop loss
        if position.stop_loss and current_price <= position.stop_loss:
            return True
        
        # Take profit
        if position.take_profit and current_price >= position.take_profit:
            return True
        
        # Time-based exit (close to expiration)
        days_to_expiry = (datetime.strptime(position.expiration_date, '%Y-%m-%d') - datetime.now()).days
        if days_to_expiry <= 1:
            return True
        
        return False


class BreakoutStrategy(BaseStrategy):
    """Strategy focused on breakout signals"""
    
    def __init__(self):
        super().__init__("Breakout Strategy", StrategyType.BREAKOUT)
        self.min_volume_ratio = 2.0
        self.min_price_change = 0.02
    
    def analyze_signal(self, signal: TradingSignal) -> Dict:
        """Analyze if signal fits breakout strategy"""
        suitable = signal.signal_type in [
            SignalType.BREAKOUT_BULLISH,
            SignalType.BREAKOUT_BEARISH
        ]
        
        return {
            'suitable': suitable,
            'adjusted_confidence': signal.confidence,
            'reasoning': "Breakout signal analysis"
        }
    
    def calculate_position_size(self, signal: TradingSignal, current_price: float) -> int:
        """Calculate position size for breakout trades"""
        max_risk = self.available_capital * self.max_risk_per_trade * 1.2  # Slightly higher risk for breakouts
        risk_per_contract = abs(current_price - signal.stop_loss_price)
        
        if risk_per_contract <= 0:
            return 0
        
        position_size = int(max_risk / risk_per_contract)
        return max(1, min(position_size, 15))  # Up to 15 contracts for strong breakouts
    
    def determine_entry_criteria(self, signal: TradingSignal) -> bool:
        """Check if entry criteria are met for breakouts"""
        return (signal.confidence >= 0.7 and 
                signal.strength.value >= 3)
    
    def determine_exit_criteria(self, position: Position, current_data: Dict) -> bool:
        """Check if exit criteria are met for breakout positions"""
        current_price = current_data.get('current_price', position.current_price)
        
        # Tighter stop loss for breakouts
        if position.stop_loss and current_price <= position.stop_loss * 1.05:
            return True
        
        # Take profit
        if position.take_profit and current_price >= position.take_profit:
            return True
        
        # Quick exit if breakout fails (within 2 days)
        days_held = (datetime.now() - position.entry_time).days
        if days_held >= 2 and position.unrealized_pnl < -position.entry_price * 0.3:
            return True
        
        return False
