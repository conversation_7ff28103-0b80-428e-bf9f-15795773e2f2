import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from loguru import logger
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass

from src.strategies.strategy import BaseStrategy, Position
from src.signals.signal_generator import SignalGenerator, TradingSignal
from src.data.market_data import MarketDataProvider


@dataclass
class BacktestResult:
    """Results from a backtest run"""
    strategy_name: str
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    total_return: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    max_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    profit_factor: float
    trades: List[Dict]
    equity_curve: pd.DataFrame


class BacktestEngine:
    """
    Comprehensive backtesting engine for options trading strategies.
    Simulates historical trading with realistic constraints and costs.
    """
    
    def __init__(self):
        self.market_data = MarketDataProvider()
        self.signal_generator = SignalGenerator()
        self.results = []
    
    def run_backtest(self, strategy: BaseStrategy, symbols: List[str], 
                    start_date: str, end_date: str, 
                    initial_capital: float = 50000) -> BacktestResult:
        """
        Run a complete backtest for a strategy.
        
        Args:
            strategy: Trading strategy to test
            symbols: List of symbols to trade
            start_date: Start date for backtest (YYYY-MM-DD)
            end_date: End date for backtest (YYYY-MM-DD)
            initial_capital: Starting capital
            
        Returns:
            BacktestResult object with comprehensive results
        """
        logger.info(f"Starting backtest for {strategy.name} from {start_date} to {end_date}")
        
        try:
            # Initialize strategy with backtest capital
            strategy.capital = initial_capital
            strategy.available_capital = initial_capital
            strategy.positions = []
            
            # Generate date range for simulation
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            date_range = [d for d in date_range if d.weekday() < 5]  # Trading days only
            
            # Track equity curve
            equity_data = []
            
            # Simulate trading day by day
            for current_date in date_range:
                daily_equity = self._simulate_trading_day(
                    strategy, symbols, current_date
                )
                equity_data.append({
                    'date': current_date,
                    'equity': daily_equity,
                    'positions': len([p for p in strategy.positions if p.is_open])
                })
            
            # Calculate final results
            result = self._calculate_backtest_results(
                strategy, start_date, end_date, initial_capital, equity_data
            )
            
            self.results.append(result)
            logger.info(f"Backtest completed. Final return: {result.total_return:.2%}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            raise
    
    def _simulate_trading_day(self, strategy: BaseStrategy, symbols: List[str], 
                            current_date: datetime) -> float:
        """Simulate trading for a single day"""
        try:
            # Update existing positions
            strategy.update_positions()
            
            # Generate signals for each symbol
            for symbol in symbols:
                try:
                    # Get historical data up to current date
                    end_date = current_date.strftime('%Y-%m-%d')
                    start_date = (current_date - timedelta(days=90)).strftime('%Y-%m-%d')
                    
                    historical_data = self.market_data.get_stock_data(
                        symbol, period="3mo"
                    )
                    
                    if historical_data.empty:
                        continue
                    
                    # Filter data to current date
                    historical_data = historical_data[historical_data.index <= current_date]
                    
                    if len(historical_data) < 20:  # Need minimum data
                        continue
                    
                    # Generate signals (simplified for backtesting)
                    signals = self._generate_backtest_signals(symbol, historical_data)
                    
                    # Execute signals
                    for signal in signals:
                        if len([p for p in strategy.positions if p.is_open]) < strategy.max_positions:
                            strategy.execute_signal(signal)
                    
                except Exception as e:
                    logger.debug(f"Error processing {symbol} on {current_date}: {e}")
                    continue
            
            # Calculate current equity
            open_positions = [p for p in strategy.positions if p.is_open]
            unrealized_pnl = sum(p.unrealized_pnl for p in open_positions)
            current_equity = strategy.available_capital + unrealized_pnl
            
            return current_equity
            
        except Exception as e:
            logger.error(f"Error simulating trading day {current_date}: {e}")
            return strategy.capital
    
    def _generate_backtest_signals(self, symbol: str, historical_data: pd.DataFrame) -> List[TradingSignal]:
        """Generate signals for backtesting (simplified version)"""
        try:
            # Use only the data available up to the current point
            if len(historical_data) < 20:
                return []
            
            # Calculate basic indicators for signal generation
            data = historical_data.copy()
            
            # Simple momentum signal
            data['SMA_20'] = data['Close'].rolling(20).mean()
            data['SMA_5'] = data['Close'].rolling(5).mean()
            data['RSI'] = self._calculate_rsi(data['Close'], 14)
            data['Volume_Ratio'] = data['Volume'] / data['Volume'].rolling(20).mean()
            
            latest = data.iloc[-1]
            current_price = latest['Close']
            
            signals = []
            
            # Momentum bullish signal
            if (latest['SMA_5'] > latest['SMA_20'] and 
                latest['RSI'] < 70 and 
                latest['Volume_Ratio'] > 1.5):
                
                signal = TradingSignal(
                    symbol=symbol,
                    signal_type=self.signal_generator.SignalType.MOMENTUM_BULLISH,
                    strength=self.signal_generator.SignalStrength.MODERATE,
                    confidence=0.7,
                    entry_price=current_price,
                    target_price=current_price * 1.2,
                    stop_loss_price=current_price * 0.9,
                    expiration_date=(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
                    option_type='call'
                )
                signals.append(signal)
            
            # Oversold reversal signal
            elif latest['RSI'] < 30 and latest['Volume_Ratio'] > 2.0:
                signal = TradingSignal(
                    symbol=symbol,
                    signal_type=self.signal_generator.SignalType.REVERSAL_BULLISH,
                    strength=self.signal_generator.SignalStrength.STRONG,
                    confidence=0.75,
                    entry_price=current_price,
                    target_price=current_price * 1.15,
                    stop_loss_price=current_price * 0.92,
                    expiration_date=(datetime.now() + timedelta(days=10)).strftime('%Y-%m-%d'),
                    option_type='call'
                )
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating backtest signals for {symbol}: {e}")
            return []
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_backtest_results(self, strategy: BaseStrategy, start_date: str, 
                                  end_date: str, initial_capital: float, 
                                  equity_data: List[Dict]) -> BacktestResult:
        """Calculate comprehensive backtest results"""
        
        # Create equity curve DataFrame
        equity_df = pd.DataFrame(equity_data)
        equity_df.set_index('date', inplace=True)
        
        # Calculate returns
        equity_df['returns'] = equity_df['equity'].pct_change()
        equity_df['cumulative_returns'] = (1 + equity_df['returns']).cumprod() - 1
        
        # Final metrics
        final_capital = equity_df['equity'].iloc[-1] if not equity_df.empty else initial_capital
        total_return = (final_capital - initial_capital) / initial_capital
        
        # Trade statistics
        closed_trades = [p for p in strategy.positions if not p.is_open]
        winning_trades = len([p for p in closed_trades if p.realized_pnl > 0])
        losing_trades = len([p for p in closed_trades if p.realized_pnl <= 0])
        total_trades = len(closed_trades)
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Calculate average win/loss
        wins = [p.realized_pnl for p in closed_trades if p.realized_pnl > 0]
        losses = [p.realized_pnl for p in closed_trades if p.realized_pnl <= 0]
        
        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 0
        
        # Risk metrics
        max_drawdown = self._calculate_max_drawdown(equity_df['equity'])
        sharpe_ratio = self._calculate_sharpe_ratio(equity_df['returns'])
        sortino_ratio = self._calculate_sortino_ratio(equity_df['returns'])
        calmar_ratio = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Profit factor
        total_wins = sum(wins) if wins else 0
        total_losses = abs(sum(losses)) if losses else 1
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        # Prepare trade data
        trades_data = [p.to_dict() for p in closed_trades]
        
        return BacktestResult(
            strategy_name=strategy.name,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            final_capital=final_capital,
            total_return=total_return,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            profit_factor=profit_factor,
            trades=trades_data,
            equity_curve=equity_df
        )
    
    def _calculate_max_drawdown(self, equity_series: pd.Series) -> float:
        """Calculate maximum drawdown"""
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak
        return drawdown.min()
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        if returns.std() == 0:
            return 0
        
        excess_returns = returns.mean() * 252 - risk_free_rate  # Annualized
        volatility = returns.std() * np.sqrt(252)  # Annualized
        
        return excess_returns / volatility
    
    def _calculate_sortino_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio"""
        downside_returns = returns[returns < 0]
        if len(downside_returns) == 0 or downside_returns.std() == 0:
            return 0
        
        excess_returns = returns.mean() * 252 - risk_free_rate  # Annualized
        downside_volatility = downside_returns.std() * np.sqrt(252)  # Annualized
        
        return excess_returns / downside_volatility
    
    def generate_backtest_report(self, result: BacktestResult) -> str:
        """Generate a comprehensive backtest report"""
        report = f"""
BACKTEST REPORT: {result.strategy_name}
{'='*50}

PERIOD: {result.start_date} to {result.end_date}

PERFORMANCE SUMMARY:
- Initial Capital: ${result.initial_capital:,.2f}
- Final Capital: ${result.final_capital:,.2f}
- Total Return: {result.total_return:.2%}
- Total Trades: {result.total_trades}

TRADE STATISTICS:
- Winning Trades: {result.winning_trades}
- Losing Trades: {result.losing_trades}
- Win Rate: {result.win_rate:.2%}
- Average Win: ${result.avg_win:.2f}
- Average Loss: ${result.avg_loss:.2f}
- Profit Factor: {result.profit_factor:.2f}

RISK METRICS:
- Maximum Drawdown: {result.max_drawdown:.2%}
- Sharpe Ratio: {result.sharpe_ratio:.2f}
- Sortino Ratio: {result.sortino_ratio:.2f}
- Calmar Ratio: {result.calmar_ratio:.2f}
"""
        return report
    
    def plot_backtest_results(self, result: BacktestResult, save_path: Optional[str] = None):
        """Plot backtest results"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Equity curve
        axes[0, 0].plot(result.equity_curve.index, result.equity_curve['equity'])
        axes[0, 0].set_title('Equity Curve')
        axes[0, 0].set_ylabel('Portfolio Value ($)')
        
        # Drawdown
        peak = result.equity_curve['equity'].expanding().max()
        drawdown = (result.equity_curve['equity'] - peak) / peak
        axes[0, 1].fill_between(result.equity_curve.index, drawdown, 0, alpha=0.3, color='red')
        axes[0, 1].set_title('Drawdown')
        axes[0, 1].set_ylabel('Drawdown (%)')
        
        # Returns distribution
        axes[1, 0].hist(result.equity_curve['returns'].dropna(), bins=50, alpha=0.7)
        axes[1, 0].set_title('Returns Distribution')
        axes[1, 0].set_xlabel('Daily Returns')
        
        # Trade P&L
        if result.trades:
            pnl_values = [trade['realized_pnl'] for trade in result.trades]
            axes[1, 1].bar(range(len(pnl_values)), pnl_values, 
                          color=['green' if pnl > 0 else 'red' for pnl in pnl_values])
            axes[1, 1].set_title('Trade P&L')
            axes[1, 1].set_xlabel('Trade Number')
            axes[1, 1].set_ylabel('P&L ($)')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
        else:
            plt.show()
    
    def compare_strategies(self, results: List[BacktestResult]) -> pd.DataFrame:
        """Compare multiple backtest results"""
        comparison_data = []
        
        for result in results:
            comparison_data.append({
                'Strategy': result.strategy_name,
                'Total Return': result.total_return,
                'Win Rate': result.win_rate,
                'Sharpe Ratio': result.sharpe_ratio,
                'Max Drawdown': result.max_drawdown,
                'Profit Factor': result.profit_factor,
                'Total Trades': result.total_trades
            })
        
        return pd.DataFrame(comparison_data)
