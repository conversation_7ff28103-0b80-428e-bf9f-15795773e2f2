import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from loguru import logger
from datetime import datetime, timedelta
from enum import Enum

from strategies.strategy import Position, PositionType
from signals.signal_generator import TradingSignal, SignalType, SignalStrength
# Configuration with fallback values
INITIAL_CAPITAL = 50000
RISK_PER_TRADE = 0.10
MAX_POSITIONS = 3

try:
    from config.settings import INITIAL_CAPITAL, RISK_PER_TRADE, MAX_POSITIONS
except ImportError:
    pass  # Use fallback values defined above


class RiskLevel(Enum):
    """Risk levels for different scenarios"""
    LOW = 1
    MODERATE = 2
    HIGH = 3
    EXTREME = 4


class RiskMetric:
    """Individual risk metric with value and threshold"""
    
    def __init__(self, name: str, value: float, threshold: float, 
                 risk_level: RiskLevel, description: str = ""):
        self.name = name
        self.value = value
        self.threshold = threshold
        self.risk_level = risk_level
        self.description = description
        self.is_breached = value > threshold
    
    def to_dict(self) -> Dict:
        return {
            'name': self.name,
            'value': self.value,
            'threshold': self.threshold,
            'risk_level': self.risk_level.name,
            'is_breached': self.is_breached,
            'description': self.description
        }


class RiskManager:
    """
    Comprehensive risk management system for options trading.
    Handles position sizing, portfolio risk, and dynamic risk adjustments.
    """
    
    def __init__(self, initial_capital: float = INITIAL_CAPITAL):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_risk_per_trade = RISK_PER_TRADE
        self.max_positions = MAX_POSITIONS
        self.max_portfolio_risk = 0.25  # 25% of portfolio
        self.max_sector_concentration = 0.30  # 30% in any sector
        self.max_single_position_risk = 0.15  # 15% in any single position
        self.volatility_adjustment_factor = 1.0
        self.risk_metrics = {}
        self.risk_alerts = []
    
    def calculate_position_size(self, signal: TradingSignal, current_price: float,
                              portfolio_value: float, existing_positions: List[Position]) -> Dict:
        """
        Calculate optimal position size considering multiple risk factors.
        
        Returns:
            Dictionary with position size recommendation and risk analysis
        """
        try:
            # Base position size calculation
            base_risk_amount = portfolio_value * self.max_risk_per_trade
            
            # Adjust for signal strength and confidence
            signal_adjustment = self._calculate_signal_adjustment(signal)
            adjusted_risk_amount = base_risk_amount * signal_adjustment
            
            # Adjust for market volatility
            volatility_adjustment = self._calculate_volatility_adjustment(signal.symbol)
            adjusted_risk_amount *= volatility_adjustment
            
            # Calculate position size based on stop loss
            risk_per_contract = abs(current_price - signal.stop_loss_price)
            if risk_per_contract <= 0:
                return {'position_size': 0, 'risk_analysis': 'Invalid stop loss level'}
            
            base_position_size = int(adjusted_risk_amount / risk_per_contract)
            
            # Apply portfolio-level constraints
            final_position_size = self._apply_portfolio_constraints(
                base_position_size, signal, current_price, portfolio_value, existing_positions
            )
            
            # Calculate final risk metrics
            final_risk_amount = final_position_size * risk_per_contract
            risk_percentage = final_risk_amount / portfolio_value
            
            return {
                'position_size': final_position_size,
                'risk_amount': final_risk_amount,
                'risk_percentage': risk_percentage,
                'signal_adjustment': signal_adjustment,
                'volatility_adjustment': volatility_adjustment,
                'risk_analysis': self._generate_position_risk_analysis(
                    signal, final_position_size, final_risk_amount, risk_percentage
                )
            }
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {'position_size': 0, 'risk_analysis': f'Error: {e}'}
    
    def _calculate_signal_adjustment(self, signal: TradingSignal) -> float:
        """Calculate position size adjustment based on signal quality"""
        base_adjustment = 1.0
        
        # Confidence adjustment
        if signal.confidence >= 0.8:
            base_adjustment *= 1.2
        elif signal.confidence >= 0.7:
            base_adjustment *= 1.1
        elif signal.confidence < 0.6:
            base_adjustment *= 0.8
        
        # Strength adjustment
        strength_multipliers = {
            SignalStrength.WEAK: 0.7,
            SignalStrength.MODERATE: 1.0,
            SignalStrength.STRONG: 1.2,
            SignalStrength.VERY_STRONG: 1.4
        }
        base_adjustment *= strength_multipliers.get(signal.strength, 1.0)
        
        # Risk-reward adjustment
        if signal.risk_reward_ratio >= 3.0:
            base_adjustment *= 1.3
        elif signal.risk_reward_ratio >= 2.0:
            base_adjustment *= 1.1
        elif signal.risk_reward_ratio < 1.5:
            base_adjustment *= 0.8
        
        return min(base_adjustment, 2.0)  # Cap at 2x
    
    def _calculate_volatility_adjustment(self, symbol: str) -> float:
        """Calculate position size adjustment based on market volatility"""
        try:
            # This would typically use VIX or symbol-specific volatility
            # For now, using a simplified approach
            
            # Get recent volatility (simplified)
            # In production, this would calculate actual volatility metrics
            base_volatility = 0.20  # 20% annual volatility baseline
            
            # Adjust position size inversely to volatility
            # Higher volatility = smaller positions
            if self.volatility_adjustment_factor > 1.5:
                return 0.7  # Reduce position size by 30%
            elif self.volatility_adjustment_factor > 1.2:
                return 0.85  # Reduce position size by 15%
            elif self.volatility_adjustment_factor < 0.8:
                return 1.2  # Increase position size by 20%
            else:
                return 1.0
                
        except Exception as e:
            logger.error(f"Error calculating volatility adjustment: {e}")
            return 1.0
    
    def _apply_portfolio_constraints(self, base_position_size: int, signal: TradingSignal,
                                   current_price: float, portfolio_value: float,
                                   existing_positions: List[Position]) -> int:
        """Apply portfolio-level risk constraints"""
        
        # Check maximum positions limit
        open_positions = len([p for p in existing_positions if p.is_open])
        if open_positions >= self.max_positions:
            return 0
        
        # Check single position risk limit
        position_value = base_position_size * current_price
        single_position_risk = position_value / portfolio_value
        if single_position_risk > self.max_single_position_risk:
            max_position_size = int((portfolio_value * self.max_single_position_risk) / current_price)
            base_position_size = min(base_position_size, max_position_size)
        
        # Check sector concentration (simplified)
        # In production, this would check actual sector exposure
        symbol_sector_exposure = self._calculate_sector_exposure(signal.symbol, existing_positions)
        if symbol_sector_exposure > self.max_sector_concentration:
            base_position_size = int(base_position_size * 0.5)  # Reduce by 50%
        
        # Check total portfolio risk
        total_risk = self._calculate_total_portfolio_risk(existing_positions, portfolio_value)
        position_risk = (base_position_size * abs(current_price - signal.stop_loss_price)) / portfolio_value
        
        if total_risk + position_risk > self.max_portfolio_risk:
            max_additional_risk = self.max_portfolio_risk - total_risk
            if max_additional_risk <= 0:
                return 0
            max_position_size = int((max_additional_risk * portfolio_value) / 
                                  abs(current_price - signal.stop_loss_price))
            base_position_size = min(base_position_size, max_position_size)
        
        return max(0, base_position_size)
    
    def _calculate_sector_exposure(self, symbol: str, existing_positions: List[Position]) -> float:
        """Calculate current sector exposure (simplified)"""
        # This is a simplified version - in production, you'd use actual sector mappings
        sector_symbols = [p.symbol for p in existing_positions if p.is_open and p.symbol.startswith(symbol[0])]
        return len(sector_symbols) / max(len(existing_positions), 1)
    
    def _calculate_total_portfolio_risk(self, existing_positions: List[Position], 
                                      portfolio_value: float) -> float:
        """Calculate total portfolio risk from existing positions"""
        total_risk = 0
        for position in existing_positions:
            if position.is_open and position.stop_loss:
                position_risk = abs(position.current_price - position.stop_loss) * position.quantity
                total_risk += position_risk / portfolio_value
        return total_risk
    
    def _generate_position_risk_analysis(self, signal: TradingSignal, position_size: int,
                                       risk_amount: float, risk_percentage: float) -> str:
        """Generate human-readable risk analysis"""
        analysis = []
        
        if position_size == 0:
            analysis.append("Position rejected due to risk constraints")
        else:
            analysis.append(f"Position size: {position_size} contracts")
            analysis.append(f"Risk amount: ${risk_amount:.2f} ({risk_percentage:.2%})")
            
            if risk_percentage > 0.10:
                analysis.append("⚠️ High risk position")
            elif risk_percentage > 0.05:
                analysis.append("⚡ Moderate risk position")
            else:
                analysis.append("✅ Low risk position")
        
        return " | ".join(analysis)
    
    def assess_portfolio_risk(self, positions: List[Position], 
                            portfolio_value: float) -> Dict[str, RiskMetric]:
        """Comprehensive portfolio risk assessment"""
        risk_metrics = {}
        
        try:
            open_positions = [p for p in positions if p.is_open]
            
            # Portfolio concentration risk
            concentration_risk = len(open_positions) / self.max_positions
            risk_metrics['concentration'] = RiskMetric(
                name="Position Concentration",
                value=concentration_risk,
                threshold=0.8,
                risk_level=RiskLevel.MODERATE if concentration_risk > 0.8 else RiskLevel.LOW,
                description=f"{len(open_positions)}/{self.max_positions} positions used"
            )
            
            # Total portfolio risk
            total_risk = self._calculate_total_portfolio_risk(open_positions, portfolio_value)
            risk_metrics['total_risk'] = RiskMetric(
                name="Total Portfolio Risk",
                value=total_risk,
                threshold=self.max_portfolio_risk,
                risk_level=RiskLevel.HIGH if total_risk > self.max_portfolio_risk else RiskLevel.LOW,
                description=f"{total_risk:.2%} of portfolio at risk"
            )
            
            # Drawdown risk
            unrealized_pnl = sum(p.unrealized_pnl for p in open_positions)
            drawdown = abs(min(0, unrealized_pnl)) / portfolio_value
            risk_metrics['drawdown'] = RiskMetric(
                name="Current Drawdown",
                value=drawdown,
                threshold=0.10,  # 10% drawdown threshold
                risk_level=RiskLevel.HIGH if drawdown > 0.15 else RiskLevel.MODERATE if drawdown > 0.10 else RiskLevel.LOW,
                description=f"{drawdown:.2%} unrealized loss"
            )
            
            # Time decay risk (for options)
            time_decay_risk = self._calculate_time_decay_risk(open_positions)
            risk_metrics['time_decay'] = RiskMetric(
                name="Time Decay Risk",
                value=time_decay_risk,
                threshold=0.20,
                risk_level=RiskLevel.MODERATE if time_decay_risk > 0.20 else RiskLevel.LOW,
                description=f"{time_decay_risk:.2%} of positions near expiry"
            )
            
            self.risk_metrics = risk_metrics
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error assessing portfolio risk: {e}")
            return {}
    
    def _calculate_time_decay_risk(self, positions: List[Position]) -> float:
        """Calculate risk from time decay on options positions"""
        if not positions:
            return 0
        
        near_expiry_positions = 0
        for position in positions:
            try:
                expiry_date = datetime.strptime(position.expiration_date, '%Y-%m-%d')
                days_to_expiry = (expiry_date - datetime.now()).days
                if days_to_expiry <= 3:  # 3 days or less
                    near_expiry_positions += 1
            except:
                continue
        
        return near_expiry_positions / len(positions)
    
    def generate_risk_alerts(self, positions: List[Position], 
                           portfolio_value: float) -> List[Dict]:
        """Generate risk alerts based on current portfolio state"""
        alerts = []
        
        # Assess current risks
        risk_metrics = self.assess_portfolio_risk(positions, portfolio_value)
        
        # Generate alerts for breached thresholds
        for metric_name, metric in risk_metrics.items():
            if metric.is_breached:
                alerts.append({
                    'type': 'risk_breach',
                    'severity': metric.risk_level.name,
                    'message': f"{metric.name} exceeded threshold: {metric.value:.2%} > {metric.threshold:.2%}",
                    'recommendation': self._get_risk_recommendation(metric_name, metric),
                    'timestamp': datetime.now().isoformat()
                })
        
        # Check for positions approaching expiry
        for position in positions:
            if position.is_open:
                try:
                    expiry_date = datetime.strptime(position.expiration_date, '%Y-%m-%d')
                    days_to_expiry = (expiry_date - datetime.now()).days
                    
                    if days_to_expiry <= 1:
                        alerts.append({
                            'type': 'expiry_warning',
                            'severity': 'HIGH',
                            'message': f"{position.symbol} position expires in {days_to_expiry} day(s)",
                            'recommendation': "Consider closing position or rolling to next expiry",
                            'timestamp': datetime.now().isoformat()
                        })
                    elif days_to_expiry <= 3:
                        alerts.append({
                            'type': 'expiry_warning',
                            'severity': 'MODERATE',
                            'message': f"{position.symbol} position expires in {days_to_expiry} days",
                            'recommendation': "Monitor position closely for time decay",
                            'timestamp': datetime.now().isoformat()
                        })
                except:
                    continue
        
        self.risk_alerts = alerts
        return alerts
    
    def _get_risk_recommendation(self, metric_name: str, metric: RiskMetric) -> str:
        """Get specific recommendations for risk metric breaches"""
        recommendations = {
            'concentration': "Consider reducing number of open positions",
            'total_risk': "Reduce position sizes or close some positions",
            'drawdown': "Review stop-loss levels and consider defensive measures",
            'time_decay': "Close or roll positions approaching expiry"
        }
        return recommendations.get(metric_name, "Review and adjust risk exposure")
    
    def get_risk_summary(self, positions: List[Position], portfolio_value: float) -> Dict:
        """Get comprehensive risk summary"""
        risk_metrics = self.assess_portfolio_risk(positions, portfolio_value)
        alerts = self.generate_risk_alerts(positions, portfolio_value)
        
        # Calculate overall risk score (0-100)
        risk_scores = [metric.value / metric.threshold * 100 for metric in risk_metrics.values()]
        overall_risk_score = min(100, max(risk_scores) if risk_scores else 0)
        
        # Determine risk level
        if overall_risk_score > 80:
            overall_risk_level = RiskLevel.EXTREME
        elif overall_risk_score > 60:
            overall_risk_level = RiskLevel.HIGH
        elif overall_risk_score > 40:
            overall_risk_level = RiskLevel.MODERATE
        else:
            overall_risk_level = RiskLevel.LOW
        
        return {
            'overall_risk_score': overall_risk_score,
            'overall_risk_level': overall_risk_level.name,
            'risk_metrics': {name: metric.to_dict() for name, metric in risk_metrics.items()},
            'active_alerts': len(alerts),
            'alerts': alerts,
            'recommendations': self._generate_portfolio_recommendations(risk_metrics, alerts)
        }
    
    def _generate_portfolio_recommendations(self, risk_metrics: Dict[str, RiskMetric], 
                                          alerts: List[Dict]) -> List[str]:
        """Generate portfolio-level recommendations"""
        recommendations = []
        
        if any(metric.is_breached for metric in risk_metrics.values()):
            recommendations.append("🔴 Risk limits breached - immediate action required")
        
        if len(alerts) > 3:
            recommendations.append("⚠️ Multiple risk alerts active - review portfolio")
        
        high_risk_metrics = [m for m in risk_metrics.values() if m.risk_level in [RiskLevel.HIGH, RiskLevel.EXTREME]]
        if high_risk_metrics:
            recommendations.append("📉 Consider reducing overall portfolio risk")
        
        if not recommendations:
            recommendations.append("✅ Portfolio risk within acceptable limits")
        
        return recommendations
