"""
Paper Trading Implementation for AWOT Trading Platform
Simulates real trading without using actual money for testing strategies safely
"""

import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from loguru import logger
from dataclasses import dataclass, asdict
import numpy as np
import pandas as pd

from trading.robinhood_client import OrderSide, OptionType
from signals.signal_generator import TradingSignal


@dataclass
class PaperPosition:
    """Paper trading position"""
    symbol: str
    position_type: str  # 'stock' or 'option'
    quantity: int
    entry_price: float
    entry_time: datetime
    
    # Options-specific fields
    strike_price: Optional[float] = None
    expiration_date: Optional[str] = None
    option_type: Optional[str] = None  # 'call' or 'put'
    
    # Position tracking
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    is_open: bool = True
    
    # Exit information
    exit_price: Optional[float] = None
    exit_time: Optional[datetime] = None
    exit_reason: str = ""
    
    def update_price(self, new_price: float):
        """Update current price and calculate unrealized P&L"""
        self.current_price = new_price
        if self.is_open:
            if self.position_type == 'stock':
                self.unrealized_pnl = (new_price - self.entry_price) * self.quantity
            else:  # option
                self.unrealized_pnl = (new_price - self.entry_price) * self.quantity * 100
    
    def close_position(self, exit_price: float, reason: str = "Manual close"):
        """Close the position"""
        self.exit_price = exit_price
        self.exit_time = datetime.now()
        self.exit_reason = reason
        self.is_open = False
        
        if self.position_type == 'stock':
            self.realized_pnl = (exit_price - self.entry_price) * self.quantity
        else:  # option
            self.realized_pnl = (exit_price - self.entry_price) * self.quantity * 100
        
        self.unrealized_pnl = 0.0
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        result = asdict(self)
        result['entry_time'] = self.entry_time.isoformat()
        if self.exit_time:
            result['exit_time'] = self.exit_time.isoformat()
        return result


@dataclass
class PaperTrade:
    """Paper trade record"""
    trade_id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: int
    price: float
    trade_type: str  # 'stock' or 'option'
    timestamp: datetime
    
    # Options-specific
    strike_price: Optional[float] = None
    expiration_date: Optional[str] = None
    option_type: Optional[str] = None
    
    # Trade metadata
    strategy_name: str = ""
    signal_id: str = ""
    commission: float = 0.0
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


class PaperTradingEngine:
    """
    Paper trading engine that simulates real trading with realistic market conditions.
    Includes slippage, commissions, and market impact simulation.
    """
    
    def __init__(self, initial_capital: float = 50000.0):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.positions: Dict[str, PaperPosition] = {}
        self.trade_history: List[PaperTrade] = []
        
        # Trading settings
        self.commission_per_trade = 0.0  # Robinhood is commission-free
        self.slippage_factor = 0.001  # 0.1% slippage
        self.market_impact_factor = 0.0005  # 0.05% market impact
        
        # Performance tracking
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_value = initial_capital
        
        # Statistics
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'largest_win': 0.0,
            'largest_loss': 0.0,
            'profit_factor': 0.0
        }
        
        # Try to load existing state if available
        self._auto_load_state()

        logger.info(f"Paper Trading Engine initialized with ${initial_capital:,.2f}")
    
    def place_order(self, symbol: str, quantity: int, side: OrderSide, 
                   price: float, order_type: str = "stock",
                   strike_price: Optional[float] = None,
                   expiration_date: Optional[str] = None,
                   option_type: Optional[OptionType] = None,
                   strategy_name: str = "",
                   signal_id: str = "") -> Dict:
        """
        Place a paper trading order.
        
        Args:
            symbol: Stock symbol
            quantity: Number of shares/contracts
            side: Buy or sell
            price: Order price
            order_type: 'stock' or 'option'
            strike_price: Option strike price
            expiration_date: Option expiration date
            option_type: Call or put
            strategy_name: Name of strategy placing order
            signal_id: Signal identifier
            
        Returns:
            Order result dictionary
        """
        try:
            # Apply slippage and market impact
            execution_price = self._calculate_execution_price(price, side, quantity)
            
            # Calculate order value
            if order_type == "stock":
                order_value = execution_price * quantity
            else:  # option
                order_value = execution_price * quantity * 100  # Options are per 100 shares
            
            # Check if we have enough cash for buy orders
            if side == OrderSide.BUY:
                total_cost = order_value + self.commission_per_trade
                if total_cost > self.cash:
                    return {
                        'success': False,
                        'error': f'Insufficient cash. Need ${total_cost:.2f}, have ${self.cash:.2f}'
                    }
            
            # Check if we have enough shares/contracts for sell orders
            elif side == OrderSide.SELL:
                position_key = self._get_position_key(symbol, order_type, strike_price, 
                                                    expiration_date, option_type)
                
                if position_key not in self.positions:
                    return {
                        'success': False,
                        'error': f'No position found to sell: {position_key}'
                    }
                
                position = self.positions[position_key]
                if position.quantity < quantity:
                    return {
                        'success': False,
                        'error': f'Insufficient quantity. Need {quantity}, have {position.quantity}'
                    }
            
            # Execute the trade
            trade_id = f"PAPER_{int(time.time() * 1000)}"
            
            # Create trade record
            trade = PaperTrade(
                trade_id=trade_id,
                symbol=symbol,
                side=side.value,
                quantity=quantity,
                price=execution_price,
                trade_type=order_type,
                timestamp=datetime.now(),
                strike_price=strike_price,
                expiration_date=expiration_date,
                option_type=option_type.value if option_type else None,
                strategy_name=strategy_name,
                signal_id=signal_id,
                commission=self.commission_per_trade
            )
            
            self.trade_history.append(trade)
            
            # Update positions and cash
            if side == OrderSide.BUY:
                self._open_position(symbol, quantity, execution_price, order_type,
                                  strike_price, expiration_date, option_type)
                self.cash -= (order_value + self.commission_per_trade)
            else:  # SELL
                proceeds = self._close_position(symbol, quantity, execution_price, order_type,
                                              strike_price, expiration_date, option_type)
                self.cash += (proceeds - self.commission_per_trade)
            
            # Update statistics
            self.stats['total_trades'] += 1
            self._update_performance_metrics()
            
            logger.info(f"Paper trade executed: {side.value} {quantity} {symbol} at ${execution_price:.2f}")
            
            return {
                'success': True,
                'trade_id': trade_id,
                'execution_price': execution_price,
                'commission': self.commission_per_trade,
                'cash_remaining': self.cash
            }
            
        except Exception as e:
            logger.error(f"Error placing paper order: {e}")
            return {'success': False, 'error': str(e)}
    
    def _calculate_execution_price(self, order_price: float, side: OrderSide, quantity: int) -> float:
        """Calculate execution price with slippage and market impact"""
        # Base slippage
        slippage = order_price * self.slippage_factor
        
        # Market impact based on order size (simplified)
        market_impact = order_price * self.market_impact_factor * min(quantity / 100, 1.0)
        
        # Apply slippage and market impact
        if side == OrderSide.BUY:
            # Buying costs more due to slippage and market impact
            execution_price = order_price + slippage + market_impact
        else:
            # Selling gets less due to slippage and market impact
            execution_price = order_price - slippage - market_impact
        
        return max(execution_price, 0.01)  # Minimum price of $0.01
    
    def _get_position_key(self, symbol: str, position_type: str,
                         strike_price: Optional[float] = None,
                         expiration_date: Optional[str] = None,
                         option_type: Optional[OptionType] = None) -> str:
        """Generate unique position key"""
        if position_type == "stock":
            return f"{symbol}_STOCK"
        else:
            option_type_str = option_type.value if option_type else "unknown"
            return f"{symbol}_{strike_price}_{option_type_str}_{expiration_date}"
    
    def _open_position(self, symbol: str, quantity: int, price: float, position_type: str,
                      strike_price: Optional[float] = None,
                      expiration_date: Optional[str] = None,
                      option_type: Optional[OptionType] = None):
        """Open or add to a position"""
        position_key = self._get_position_key(symbol, position_type, strike_price, 
                                            expiration_date, option_type)
        
        if position_key in self.positions:
            # Add to existing position (average price)
            existing_position = self.positions[position_key]
            total_quantity = existing_position.quantity + quantity
            total_cost = (existing_position.entry_price * existing_position.quantity + 
                         price * quantity)
            avg_price = total_cost / total_quantity
            
            existing_position.quantity = total_quantity
            existing_position.entry_price = avg_price
        else:
            # Create new position
            position = PaperPosition(
                symbol=symbol,
                position_type=position_type,
                quantity=quantity,
                entry_price=price,
                entry_time=datetime.now(),
                strike_price=strike_price,
                expiration_date=expiration_date,
                option_type=option_type.value if option_type else None,
                current_price=price
            )
            
            self.positions[position_key] = position
    
    def _close_position(self, symbol: str, quantity: int, price: float, position_type: str,
                       strike_price: Optional[float] = None,
                       expiration_date: Optional[str] = None,
                       option_type: Optional[OptionType] = None) -> float:
        """Close or reduce a position"""
        position_key = self._get_position_key(symbol, position_type, strike_price, 
                                            expiration_date, option_type)
        
        if position_key not in self.positions:
            raise ValueError(f"Position not found: {position_key}")
        
        position = self.positions[position_key]
        
        if quantity >= position.quantity:
            # Close entire position
            proceeds = price * position.quantity
            if position_type == "option":
                proceeds *= 100
            
            position.close_position(price, "Sold")
            
            # Update statistics
            if position.realized_pnl > 0:
                self.stats['winning_trades'] += 1
                self.stats['largest_win'] = max(self.stats['largest_win'], position.realized_pnl)
            else:
                self.stats['losing_trades'] += 1
                self.stats['largest_loss'] = min(self.stats['largest_loss'], position.realized_pnl)
            
            # Remove position if fully closed
            del self.positions[position_key]
        else:
            # Partial close
            proceeds = price * quantity
            if position_type == "option":
                proceeds *= 100
            
            # Calculate partial P&L
            partial_pnl = (price - position.entry_price) * quantity
            if position_type == "option":
                partial_pnl *= 100
            
            position.quantity -= quantity
            position.realized_pnl += partial_pnl
        
        return proceeds
    
    def update_positions(self, market_data: Dict[str, float]):
        """Update position prices with current market data"""
        for position_key, position in self.positions.items():
            if position.is_open and position.symbol in market_data:
                # For simplicity, use stock price for options too
                # In reality, you'd calculate option prices using Black-Scholes
                current_price = market_data[position.symbol]
                
                if position.position_type == "option":
                    # Simplified option pricing - in reality use proper option pricing model
                    if position.option_type == "call":
                        # Call option value increases with stock price
                        option_value = max(0, current_price - position.strike_price) * 0.5
                    else:  # put
                        # Put option value increases when stock price decreases
                        option_value = max(0, position.strike_price - current_price) * 0.5
                    
                    # Add time value (simplified)
                    if position.expiration_date:
                        try:
                            expiry = datetime.strptime(position.expiration_date, '%Y-%m-%d')
                            days_to_expiry = (expiry - datetime.now()).days
                            time_value = max(0, days_to_expiry / 365 * current_price * 0.1)
                            option_value += time_value
                        except:
                            pass
                    
                    position.update_price(max(option_value, 0.01))
                else:
                    position.update_price(current_price)
    
    def _update_performance_metrics(self):
        """Update performance metrics"""
        # Calculate total portfolio value
        portfolio_value = self.cash
        for position in self.positions.values():
            if position.is_open:
                if position.position_type == "stock":
                    portfolio_value += position.current_price * position.quantity
                else:  # option
                    portfolio_value += position.current_price * position.quantity * 100
        
        # Update P&L
        self.total_pnl = portfolio_value - self.initial_capital
        
        # Update drawdown
        if portfolio_value > self.peak_value:
            self.peak_value = portfolio_value
        
        current_drawdown = (self.peak_value - portfolio_value) / self.peak_value
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
        
        # Update win rate
        total_closed_trades = self.stats['winning_trades'] + self.stats['losing_trades']
        if total_closed_trades > 0:
            self.stats['win_rate'] = self.stats['winning_trades'] / total_closed_trades
        
        # Calculate average win/loss
        if self.stats['winning_trades'] > 0:
            wins = [p.realized_pnl for p in self.positions.values() 
                   if not p.is_open and p.realized_pnl > 0]
            self.stats['avg_win'] = np.mean(wins) if wins else 0
        
        if self.stats['losing_trades'] > 0:
            losses = [p.realized_pnl for p in self.positions.values() 
                     if not p.is_open and p.realized_pnl <= 0]
            self.stats['avg_loss'] = np.mean(losses) if losses else 0
        
        # Calculate profit factor
        total_wins = sum(p.realized_pnl for p in self.positions.values() 
                        if not p.is_open and p.realized_pnl > 0)
        total_losses = abs(sum(p.realized_pnl for p in self.positions.values() 
                              if not p.is_open and p.realized_pnl <= 0))
        
        if total_losses > 0:
            self.stats['profit_factor'] = total_wins / total_losses
    
    def get_portfolio_summary(self) -> Dict:
        """Get comprehensive portfolio summary"""
        # Calculate current portfolio value
        portfolio_value = self.cash
        unrealized_pnl = 0
        
        for position in self.positions.values():
            if position.is_open:
                if position.position_type == "stock":
                    position_value = position.current_price * position.quantity
                else:  # option
                    position_value = position.current_price * position.quantity * 100
                
                portfolio_value += position_value
                unrealized_pnl += position.unrealized_pnl
        
        # Calculate realized P&L
        realized_pnl = sum(p.realized_pnl for p in self.positions.values() if not p.is_open)
        
        return {
            'initial_capital': self.initial_capital,
            'cash': self.cash,
            'portfolio_value': portfolio_value,
            'total_pnl': portfolio_value - self.initial_capital,
            'unrealized_pnl': unrealized_pnl,
            'realized_pnl': realized_pnl,
            'total_return': (portfolio_value - self.initial_capital) / self.initial_capital,
            'max_drawdown': self.max_drawdown,
            'open_positions': len([p for p in self.positions.values() if p.is_open]),
            'total_positions': len(self.positions),
            'stats': self.stats.copy()
        }
    
    def get_positions(self) -> List[Dict]:
        """Get all positions as dictionaries"""
        return [position.to_dict() for position in self.positions.values()]
    
    def get_trade_history(self) -> List[Dict]:
        """Get trade history as dictionaries"""
        return [trade.to_dict() for trade in self.trade_history]
    
    def close_all_positions(self, reason: str = "Manual close all"):
        """Close all open positions"""
        positions_to_close = [p for p in self.positions.values() if p.is_open]
        
        for position in positions_to_close:
            # Use current price as exit price
            self._close_position(
                position.symbol, position.quantity, position.current_price,
                position.position_type, position.strike_price, 
                position.expiration_date, 
                OptionType.CALL if position.option_type == "call" else OptionType.PUT
            )
        
        logger.info(f"Closed {len(positions_to_close)} positions: {reason}")
    
    def reset_portfolio(self, new_capital: Optional[float] = None):
        """Reset portfolio to initial state"""
        if new_capital:
            self.initial_capital = new_capital
        
        self.cash = self.initial_capital
        self.positions.clear()
        self.trade_history.clear()
        
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_value = self.initial_capital
        
        # Reset statistics
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'largest_win': 0.0,
            'largest_loss': 0.0,
            'profit_factor': 0.0
        }
        
        logger.info(f"Portfolio reset with ${self.initial_capital:,.2f}")
    
    def save_state(self, filename: str):
        """Save current state to file"""
        state = {
            'initial_capital': self.initial_capital,
            'cash': self.cash,
            'positions': [p.to_dict() for p in self.positions.values()],
            'trade_history': [t.to_dict() for t in self.trade_history],
            'stats': self.stats,
            'max_drawdown': self.max_drawdown,
            'peak_value': self.peak_value
        }
        
        with open(filename, 'w') as f:
            json.dump(state, f, indent=2)
        
        logger.info(f"Paper trading state saved to {filename}")
    
    def load_state(self, filename: str):
        """Load state from file"""
        try:
            with open(filename, 'r') as f:
                state = json.load(f)
            
            self.initial_capital = state['initial_capital']
            self.cash = state['cash']
            self.stats = state['stats']
            self.max_drawdown = state['max_drawdown']
            self.peak_value = state['peak_value']
            
            # Reconstruct positions
            self.positions.clear()
            for pos_data in state['positions']:
                position = PaperPosition(
                    symbol=pos_data['symbol'],
                    position_type=pos_data['position_type'],
                    quantity=pos_data['quantity'],
                    entry_price=pos_data['entry_price'],
                    entry_time=datetime.fromisoformat(pos_data['entry_time']),
                    strike_price=pos_data.get('strike_price'),
                    expiration_date=pos_data.get('expiration_date'),
                    option_type=pos_data.get('option_type'),
                    current_price=pos_data['current_price'],
                    unrealized_pnl=pos_data['unrealized_pnl'],
                    realized_pnl=pos_data['realized_pnl'],
                    is_open=pos_data['is_open']
                )
                
                if pos_data.get('exit_time'):
                    position.exit_time = datetime.fromisoformat(pos_data['exit_time'])
                    position.exit_price = pos_data.get('exit_price')
                    position.exit_reason = pos_data.get('exit_reason', '')
                
                position_key = self._get_position_key(
                    position.symbol, position.position_type,
                    position.strike_price, position.expiration_date,
                    OptionType.CALL if position.option_type == "call" else OptionType.PUT
                )
                self.positions[position_key] = position
            
            # Reconstruct trade history
            self.trade_history.clear()
            for trade_data in state['trade_history']:
                trade = PaperTrade(
                    trade_id=trade_data['trade_id'],
                    symbol=trade_data['symbol'],
                    side=trade_data['side'],
                    quantity=trade_data['quantity'],
                    price=trade_data['price'],
                    trade_type=trade_data['trade_type'],
                    timestamp=datetime.fromisoformat(trade_data['timestamp']),
                    strike_price=trade_data.get('strike_price'),
                    expiration_date=trade_data.get('expiration_date'),
                    option_type=trade_data.get('option_type'),
                    strategy_name=trade_data.get('strategy_name', ''),
                    signal_id=trade_data.get('signal_id', ''),
                    commission=trade_data.get('commission', 0.0)
                )
                self.trade_history.append(trade)
            
            logger.info(f"Paper trading state loaded from {filename}")
            
        except Exception as e:
            logger.error(f"Error loading paper trading state: {e}")
            raise

    def _auto_load_state(self):
        """Automatically load state from default file if it exists"""
        default_state_file = "current_portfolio_state.json"

        if os.path.exists(default_state_file):
            try:
                self.load_state(default_state_file)
                logger.info(f"Auto-loaded existing state with {len(self.trade_history)} trades")
            except Exception as e:
                logger.warning(f"Could not auto-load state: {e}")
                # Continue with fresh state if loading fails
