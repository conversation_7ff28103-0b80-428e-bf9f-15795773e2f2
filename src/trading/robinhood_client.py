"""
Robinhood API Client for AWOT Trading Platform
Handles authentication, order placement, and portfolio management with Robin<PERSON>
"""

import robin_stocks.robinhood as rh
import pyotp
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from enum import Enum
import time

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass


class OrderType(Enum):
    """Order types supported by Robinhood"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"


class OptionType(Enum):
    """Option types"""
    CALL = "call"
    PUT = "put"


class RobinhoodClient:
    """
    Robinhood API client with comprehensive trading functionality.
    Handles authentication, order management, and portfolio tracking.
    """
    
    def __init__(self):
        # Authentication credentials
        self.username = os.getenv('ROBINHOOD_USERNAME')
        self.password = os.getenv('ROBINHOOD_PASSWORD')
        self.totp_code = os.getenv('ROBINHOOD_TOTP_SECRET')  # For 2FA
        
        # Connection state
        self.is_authenticated = False
        self.account_info = None
        self.portfolio_info = None
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 1 second between requests
        
        # Paper trading mode
        self.paper_trading = os.getenv('PAPER_TRADING', 'true').lower() == 'true'
        self.paper_portfolio = {
            'cash': 50000.0,
            'positions': {},
            'orders': [],
            'total_value': 50000.0
        }
        
        logger.info(f"Robinhood client initialized - Paper trading: {self.paper_trading}")
    
    def authenticate(self) -> bool:
        """
        Authenticate with Robinhood API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        if self.paper_trading:
            logger.info("Paper trading mode - skipping real authentication")
            self.is_authenticated = True
            return True
        
        try:
            if not self.username or not self.password:
                logger.error("Robinhood credentials not provided")
                return False
            
            # Handle 2FA if TOTP secret is provided
            mfa_code = None
            if self.totp_code:
                totp = pyotp.TOTP(self.totp_code)
                mfa_code = totp.now()
                logger.info("Generated 2FA code for authentication")
            
            # Login to Robinhood
            login_result = rh.login(
                username=self.username,
                password=self.password,
                mfa_code=mfa_code,
                store_session=True
            )
            
            if login_result:
                self.is_authenticated = True
                self.account_info = rh.profiles.load_account_profile()
                self.portfolio_info = rh.profiles.load_portfolio_profile()
                
                logger.info("Successfully authenticated with Robinhood")
                logger.info(f"Account: {self.account_info.get('account_number', 'Unknown')}")
                
                return True
            else:
                logger.error("Failed to authenticate with Robinhood")
                return False
                
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def logout(self):
        """Logout from Robinhood"""
        if not self.paper_trading:
            try:
                rh.logout()
                logger.info("Logged out from Robinhood")
            except Exception as e:
                logger.error(f"Logout error: {e}")
        
        self.is_authenticated = False
        self.account_info = None
        self.portfolio_info = None
    
    def _rate_limit(self):
        """Implement rate limiting to avoid API limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def get_account_info(self) -> Dict:
        """Get account information"""
        if self.paper_trading:
            return {
                'account_number': 'PAPER_ACCOUNT',
                'buying_power': self.paper_portfolio['cash'],
                'total_value': self.paper_portfolio['total_value'],
                'day_trades_remaining': 3,
                'pattern_day_trader': False
            }
        
        if not self.is_authenticated:
            logger.error("Not authenticated with Robinhood")
            return {}
        
        try:
            self._rate_limit()
            
            account = rh.profiles.load_account_profile()
            portfolio = rh.profiles.load_portfolio_profile()
            
            return {
                'account_number': account.get('account_number'),
                'buying_power': float(account.get('buying_power', 0)),
                'total_value': float(portfolio.get('total_return_today', 0)),
                'day_trades_remaining': int(account.get('day_trades_remaining', 0)),
                'pattern_day_trader': account.get('pattern_day_trader', False)
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {}
    
    def get_portfolio_positions(self) -> List[Dict]:
        """Get current portfolio positions"""
        if self.paper_trading:
            positions = []
            for symbol, position_data in self.paper_portfolio['positions'].items():
                positions.append({
                    'symbol': symbol,
                    'quantity': position_data['quantity'],
                    'average_buy_price': position_data['avg_price'],
                    'current_price': position_data.get('current_price', position_data['avg_price']),
                    'total_return': position_data.get('unrealized_pnl', 0),
                    'type': position_data.get('type', 'stock')
                })
            return positions
        
        if not self.is_authenticated:
            logger.error("Not authenticated with Robinhood")
            return []
        
        try:
            self._rate_limit()
            
            positions = rh.account.build_holdings()
            formatted_positions = []
            
            for symbol, data in positions.items():
                formatted_positions.append({
                    'symbol': symbol,
                    'quantity': float(data.get('quantity', 0)),
                    'average_buy_price': float(data.get('average_buy_price', 0)),
                    'current_price': float(data.get('price', 0)),
                    'total_return': float(data.get('total_return_today', 0)),
                    'type': 'stock'
                })
            
            return formatted_positions
            
        except Exception as e:
            logger.error(f"Error getting portfolio positions: {e}")
            return []
    
    def get_options_positions(self) -> List[Dict]:
        """Get current options positions"""
        if self.paper_trading:
            # Filter options positions from paper portfolio
            options_positions = []
            for symbol, position_data in self.paper_portfolio['positions'].items():
                if position_data.get('type') == 'option':
                    options_positions.append({
                        'symbol': symbol,
                        'quantity': position_data['quantity'],
                        'average_buy_price': position_data['avg_price'],
                        'current_price': position_data.get('current_price', position_data['avg_price']),
                        'total_return': position_data.get('unrealized_pnl', 0),
                        'strike_price': position_data.get('strike_price'),
                        'expiration_date': position_data.get('expiration_date'),
                        'option_type': position_data.get('option_type'),
                        'type': 'option'
                    })
            return options_positions
        
        if not self.is_authenticated:
            logger.error("Not authenticated with Robinhood")
            return []
        
        try:
            self._rate_limit()
            
            options_positions = rh.options.get_open_option_positions()
            formatted_positions = []
            
            for position in options_positions:
                instrument = position.get('instrument')
                if instrument:
                    # Get option details
                    option_data = rh.options.get_option_instrument_data_by_id(
                        instrument.split('/')[-2]
                    )
                    
                    formatted_positions.append({
                        'symbol': option_data.get('chain_symbol'),
                        'quantity': float(position.get('quantity', 0)),
                        'average_buy_price': float(position.get('average_price', 0)),
                        'current_price': 0,  # Would need separate API call
                        'total_return': 0,   # Would need calculation
                        'strike_price': float(option_data.get('strike_price', 0)),
                        'expiration_date': option_data.get('expiration_date'),
                        'option_type': option_data.get('type'),
                        'type': 'option'
                    })
            
            return formatted_positions
            
        except Exception as e:
            logger.error(f"Error getting options positions: {e}")
            return []
    
    def place_stock_order(self, symbol: str, quantity: int, side: OrderSide, 
                         order_type: OrderType = OrderType.MARKET, 
                         price: Optional[float] = None) -> Dict:
        """
        Place a stock order.
        
        Args:
            symbol: Stock symbol
            quantity: Number of shares
            side: Buy or sell
            order_type: Market, limit, etc.
            price: Limit price (required for limit orders)
            
        Returns:
            Order result dictionary
        """
        if self.paper_trading:
            return self._place_paper_stock_order(symbol, quantity, side, order_type, price)
        
        if not self.is_authenticated:
            logger.error("Not authenticated with Robinhood")
            return {'success': False, 'error': 'Not authenticated'}
        
        try:
            self._rate_limit()
            
            # Get current price for market orders
            if order_type == OrderType.MARKET:
                quote = rh.stocks.get_latest_price(symbol)[0]
                execution_price = float(quote)
            else:
                execution_price = price
            
            # Place the order
            if side == OrderSide.BUY:
                if order_type == OrderType.MARKET:
                    result = rh.orders.order_buy_market(symbol, quantity)
                else:
                    result = rh.orders.order_buy_limit(symbol, quantity, price)
            else:
                if order_type == OrderType.MARKET:
                    result = rh.orders.order_sell_market(symbol, quantity)
                else:
                    result = rh.orders.order_sell_limit(symbol, quantity, price)
            
            if result:
                order_id = result.get('id')
                logger.info(f"Stock order placed: {side.value} {quantity} {symbol} at {execution_price}")
                
                return {
                    'success': True,
                    'order_id': order_id,
                    'symbol': symbol,
                    'quantity': quantity,
                    'side': side.value,
                    'type': order_type.value,
                    'price': execution_price,
                    'status': result.get('state', 'pending')
                }
            else:
                return {'success': False, 'error': 'Order placement failed'}
                
        except Exception as e:
            logger.error(f"Error placing stock order: {e}")
            return {'success': False, 'error': str(e)}
    
    def place_option_order(self, symbol: str, strike_price: float, 
                          expiration_date: str, option_type: OptionType,
                          quantity: int, side: OrderSide, price: float) -> Dict:
        """
        Place an options order.
        
        Args:
            symbol: Underlying stock symbol
            strike_price: Option strike price
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: Call or put
            quantity: Number of contracts
            side: Buy or sell
            price: Limit price per contract
            
        Returns:
            Order result dictionary
        """
        if self.paper_trading:
            return self._place_paper_option_order(
                symbol, strike_price, expiration_date, option_type, quantity, side, price
            )
        
        if not self.is_authenticated:
            logger.error("Not authenticated with Robinhood")
            return {'success': False, 'error': 'Not authenticated'}
        
        try:
            self._rate_limit()
            
            # Find the option instrument
            option_instruments = rh.options.find_tradeable_options(
                symbol, expiration_date, strike_price, option_type.value
            )
            
            if not option_instruments:
                return {'success': False, 'error': 'Option instrument not found'}
            
            instrument = option_instruments[0]
            
            # Place the order
            if side == OrderSide.BUY:
                result = rh.orders.order_buy_option_limit(
                    'open', instrument, quantity, price
                )
            else:
                result = rh.orders.order_sell_option_limit(
                    'close', instrument, quantity, price
                )
            
            if result:
                order_id = result.get('id')
                logger.info(f"Option order placed: {side.value} {quantity} {symbol} "
                           f"{strike_price}{option_type.value[0].upper()} {expiration_date} at ${price}")
                
                return {
                    'success': True,
                    'order_id': order_id,
                    'symbol': symbol,
                    'strike_price': strike_price,
                    'expiration_date': expiration_date,
                    'option_type': option_type.value,
                    'quantity': quantity,
                    'side': side.value,
                    'price': price,
                    'status': result.get('state', 'pending')
                }
            else:
                return {'success': False, 'error': 'Option order placement failed'}
                
        except Exception as e:
            logger.error(f"Error placing option order: {e}")
            return {'success': False, 'error': str(e)}
    
    def _place_paper_stock_order(self, symbol: str, quantity: int, side: OrderSide,
                                order_type: OrderType, price: Optional[float]) -> Dict:
        """Place a paper trading stock order"""
        try:
            # Simulate getting current price
            current_price = price if price else 150.0  # Mock price
            
            order_id = f"PAPER_{int(time.time())}"
            
            # Update paper portfolio
            if side == OrderSide.BUY:
                cost = current_price * quantity
                if cost <= self.paper_portfolio['cash']:
                    self.paper_portfolio['cash'] -= cost
                    
                    if symbol in self.paper_portfolio['positions']:
                        # Average down
                        existing = self.paper_portfolio['positions'][symbol]
                        total_shares = existing['quantity'] + quantity
                        total_cost = (existing['avg_price'] * existing['quantity']) + cost
                        self.paper_portfolio['positions'][symbol] = {
                            'quantity': total_shares,
                            'avg_price': total_cost / total_shares,
                            'type': 'stock'
                        }
                    else:
                        self.paper_portfolio['positions'][symbol] = {
                            'quantity': quantity,
                            'avg_price': current_price,
                            'type': 'stock'
                        }
                else:
                    return {'success': False, 'error': 'Insufficient buying power'}
            
            else:  # SELL
                if symbol in self.paper_portfolio['positions']:
                    position = self.paper_portfolio['positions'][symbol]
                    if position['quantity'] >= quantity:
                        proceeds = current_price * quantity
                        self.paper_portfolio['cash'] += proceeds
                        position['quantity'] -= quantity
                        
                        if position['quantity'] == 0:
                            del self.paper_portfolio['positions'][symbol]
                    else:
                        return {'success': False, 'error': 'Insufficient shares'}
                else:
                    return {'success': False, 'error': 'No position to sell'}
            
            # Record the order
            order_record = {
                'order_id': order_id,
                'symbol': symbol,
                'quantity': quantity,
                'side': side.value,
                'type': order_type.value,
                'price': current_price,
                'status': 'filled',
                'timestamp': datetime.now()
            }
            
            self.paper_portfolio['orders'].append(order_record)
            
            logger.info(f"Paper trade executed: {side.value} {quantity} {symbol} at ${current_price}")
            
            return {
                'success': True,
                'order_id': order_id,
                'symbol': symbol,
                'quantity': quantity,
                'side': side.value,
                'type': order_type.value,
                'price': current_price,
                'status': 'filled'
            }
            
        except Exception as e:
            logger.error(f"Error in paper stock order: {e}")
            return {'success': False, 'error': str(e)}
    
    def _place_paper_option_order(self, symbol: str, strike_price: float,
                                 expiration_date: str, option_type: OptionType,
                                 quantity: int, side: OrderSide, price: float) -> Dict:
        """Place a paper trading option order"""
        try:
            order_id = f"PAPER_OPT_{int(time.time())}"
            option_symbol = f"{symbol}_{strike_price}{option_type.value[0].upper()}_{expiration_date}"
            
            if side == OrderSide.BUY:
                cost = price * quantity * 100  # Options are per 100 shares
                if cost <= self.paper_portfolio['cash']:
                    self.paper_portfolio['cash'] -= cost
                    
                    self.paper_portfolio['positions'][option_symbol] = {
                        'quantity': quantity,
                        'avg_price': price,
                        'strike_price': strike_price,
                        'expiration_date': expiration_date,
                        'option_type': option_type.value,
                        'type': 'option'
                    }
                else:
                    return {'success': False, 'error': 'Insufficient buying power'}
            
            else:  # SELL
                if option_symbol in self.paper_portfolio['positions']:
                    position = self.paper_portfolio['positions'][option_symbol]
                    if position['quantity'] >= quantity:
                        proceeds = price * quantity * 100
                        self.paper_portfolio['cash'] += proceeds
                        position['quantity'] -= quantity
                        
                        if position['quantity'] == 0:
                            del self.paper_portfolio['positions'][option_symbol]
                    else:
                        return {'success': False, 'error': 'Insufficient contracts'}
                else:
                    return {'success': False, 'error': 'No position to sell'}
            
            # Record the order
            order_record = {
                'order_id': order_id,
                'symbol': symbol,
                'strike_price': strike_price,
                'expiration_date': expiration_date,
                'option_type': option_type.value,
                'quantity': quantity,
                'side': side.value,
                'price': price,
                'status': 'filled',
                'timestamp': datetime.now()
            }
            
            self.paper_portfolio['orders'].append(order_record)
            
            logger.info(f"Paper option trade executed: {side.value} {quantity} {option_symbol} at ${price}")
            
            return {
                'success': True,
                'order_id': order_id,
                'symbol': symbol,
                'strike_price': strike_price,
                'expiration_date': expiration_date,
                'option_type': option_type.value,
                'quantity': quantity,
                'side': side.value,
                'price': price,
                'status': 'filled'
            }
            
        except Exception as e:
            logger.error(f"Error in paper option order: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_order_status(self, order_id: str) -> Dict:
        """Get the status of an order"""
        if self.paper_trading:
            # Find order in paper portfolio
            for order in self.paper_portfolio['orders']:
                if order['order_id'] == order_id:
                    return {
                        'order_id': order_id,
                        'status': order['status'],
                        'filled_quantity': order['quantity'],
                        'average_fill_price': order['price']
                    }
            return {'order_id': order_id, 'status': 'not_found'}
        
        if not self.is_authenticated:
            return {'order_id': order_id, 'status': 'error', 'error': 'Not authenticated'}
        
        try:
            self._rate_limit()
            order_info = rh.orders.get_stock_order_info(order_id)
            
            return {
                'order_id': order_id,
                'status': order_info.get('state', 'unknown'),
                'filled_quantity': float(order_info.get('quantity', 0)),
                'average_fill_price': float(order_info.get('average_fill_price', 0))
            }
            
        except Exception as e:
            logger.error(f"Error getting order status: {e}")
            return {'order_id': order_id, 'status': 'error', 'error': str(e)}
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        if self.paper_trading:
            logger.info(f"Paper trading: Order {order_id} cancellation simulated")
            return True
        
        if not self.is_authenticated:
            logger.error("Not authenticated with Robinhood")
            return False
        
        try:
            self._rate_limit()
            result = rh.orders.cancel_stock_order(order_id)
            
            if result:
                logger.info(f"Order {order_id} cancelled successfully")
                return True
            else:
                logger.error(f"Failed to cancel order {order_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    def get_paper_portfolio_summary(self) -> Dict:
        """Get paper trading portfolio summary"""
        if not self.paper_trading:
            return {}
        
        total_value = self.paper_portfolio['cash']
        
        # Add position values (simplified)
        for symbol, position in self.paper_portfolio['positions'].items():
            if position['type'] == 'stock':
                total_value += position['quantity'] * position['avg_price']
            else:  # option
                total_value += position['quantity'] * position['avg_price'] * 100
        
        self.paper_portfolio['total_value'] = total_value
        
        return {
            'cash': self.paper_portfolio['cash'],
            'total_value': total_value,
            'positions_count': len(self.paper_portfolio['positions']),
            'orders_count': len(self.paper_portfolio['orders']),
            'total_return': total_value - 50000.0,  # Initial capital
            'total_return_pct': (total_value - 50000.0) / 50000.0
        }
