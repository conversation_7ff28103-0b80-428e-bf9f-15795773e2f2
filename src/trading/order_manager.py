"""
Order Management System for AWOT Trading Platform
Handles order placement, tracking, cancellation, and retry logic with proper error handling
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from loguru import logger
from enum import Enum
from dataclasses import dataclass, asdict
import json
import queue

from trading.robinhood_client import RobinhoodClient, OrderType, OrderSide, OptionType


class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    FAILED = "failed"


class OrderPriority(Enum):
    """Order priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class OrderRequest:
    """Order request data structure"""
    order_id: str
    symbol: str
    quantity: int
    side: OrderSide
    order_type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    
    # Options-specific fields
    strike_price: Optional[float] = None
    expiration_date: Optional[str] = None
    option_type: Optional[OptionType] = None
    
    # Order management fields
    priority: OrderPriority = OrderPriority.NORMAL
    max_retries: int = 3
    retry_delay: float = 5.0
    timeout: float = 300.0  # 5 minutes
    
    # Callbacks
    on_fill: Optional[Callable] = None
    on_cancel: Optional[Callable] = None
    on_error: Optional[Callable] = None
    
    # Metadata
    strategy_name: str = ""
    signal_id: str = ""
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class OrderResult:
    """Order execution result"""
    order_id: str
    status: OrderStatus
    filled_quantity: int = 0
    average_fill_price: float = 0.0
    commission: float = 0.0
    error_message: str = ""
    broker_order_id: str = ""
    filled_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict:
        result = asdict(self)
        if self.filled_at:
            result['filled_at'] = self.filled_at.isoformat()
        result['status'] = self.status.value
        return result


class OrderManager:
    """
    Comprehensive order management system with retry logic, error handling,
    and real-time order tracking.
    """
    
    def __init__(self, robinhood_client: RobinhoodClient):
        self.client = robinhood_client
        
        # Order tracking
        self.pending_orders: Dict[str, OrderRequest] = {}
        self.completed_orders: Dict[str, OrderResult] = {}
        self.order_queue = queue.PriorityQueue()
        
        # Threading
        self.order_processor_thread = None
        self.order_monitor_thread = None
        self.is_running = False
        
        # Statistics
        self.stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'cancelled_orders': 0,
            'retry_count': 0,
            'average_fill_time': 0.0
        }
        
        # Configuration
        self.max_concurrent_orders = 10
        self.order_check_interval = 2.0  # seconds
        
        logger.info("Order Manager initialized")
    
    def start(self):
        """Start the order management system"""
        if self.is_running:
            logger.warning("Order Manager already running")
            return
        
        self.is_running = True
        
        # Start order processing thread
        self.order_processor_thread = threading.Thread(
            target=self._process_orders,
            daemon=True,
            name="OrderProcessor"
        )
        self.order_processor_thread.start()
        
        # Start order monitoring thread
        self.order_monitor_thread = threading.Thread(
            target=self._monitor_orders,
            daemon=True,
            name="OrderMonitor"
        )
        self.order_monitor_thread.start()
        
        logger.info("Order Manager started")
    
    def stop(self):
        """Stop the order management system"""
        self.is_running = False
        
        # Cancel all pending orders
        self.cancel_all_orders()
        
        # Wait for threads to finish
        if self.order_processor_thread:
            self.order_processor_thread.join(timeout=5.0)
        
        if self.order_monitor_thread:
            self.order_monitor_thread.join(timeout=5.0)
        
        logger.info("Order Manager stopped")
    
    def submit_stock_order(self, symbol: str, quantity: int, side: OrderSide,
                          order_type: OrderType = OrderType.MARKET,
                          price: Optional[float] = None,
                          priority: OrderPriority = OrderPriority.NORMAL,
                          **kwargs) -> str:
        """
        Submit a stock order for execution.
        
        Args:
            symbol: Stock symbol
            quantity: Number of shares
            side: Buy or sell
            order_type: Market, limit, etc.
            price: Limit price (required for limit orders)
            priority: Order priority
            **kwargs: Additional order parameters
            
        Returns:
            Order ID
        """
        order_id = f"STOCK_{symbol}_{int(time.time() * 1000)}"
        
        order_request = OrderRequest(
            order_id=order_id,
            symbol=symbol,
            quantity=quantity,
            side=side,
            order_type=order_type,
            price=price,
            priority=priority,
            **kwargs
        )
        
        return self._submit_order(order_request)
    
    def submit_option_order(self, symbol: str, strike_price: float,
                           expiration_date: str, option_type: OptionType,
                           quantity: int, side: OrderSide, price: float,
                           priority: OrderPriority = OrderPriority.NORMAL,
                           **kwargs) -> str:
        """
        Submit an options order for execution.
        
        Args:
            symbol: Underlying stock symbol
            strike_price: Option strike price
            expiration_date: Expiration date (YYYY-MM-DD)
            option_type: Call or put
            quantity: Number of contracts
            side: Buy or sell
            price: Limit price per contract
            priority: Order priority
            **kwargs: Additional order parameters
            
        Returns:
            Order ID
        """
        order_id = f"OPT_{symbol}_{strike_price}_{option_type.value[0].upper()}_{int(time.time() * 1000)}"
        
        order_request = OrderRequest(
            order_id=order_id,
            symbol=symbol,
            quantity=quantity,
            side=side,
            order_type=OrderType.LIMIT,  # Options are typically limit orders
            price=price,
            strike_price=strike_price,
            expiration_date=expiration_date,
            option_type=option_type,
            priority=priority,
            **kwargs
        )
        
        return self._submit_order(order_request)
    
    def _submit_order(self, order_request: OrderRequest) -> str:
        """Submit an order to the processing queue"""
        try:
            # Validate order
            if not self._validate_order(order_request):
                logger.error(f"Order validation failed: {order_request.order_id}")
                return ""
            
            # Add to pending orders
            self.pending_orders[order_request.order_id] = order_request
            
            # Add to processing queue with priority
            priority_value = (5 - order_request.priority.value, time.time())
            self.order_queue.put((priority_value, order_request))
            
            self.stats['total_orders'] += 1
            
            logger.info(f"Order submitted: {order_request.order_id} - {order_request.side.value} "
                       f"{order_request.quantity} {order_request.symbol}")
            
            return order_request.order_id
            
        except Exception as e:
            logger.error(f"Error submitting order: {e}")
            return ""
    
    def _validate_order(self, order_request: OrderRequest) -> bool:
        """Validate order parameters"""
        try:
            # Basic validation
            if not order_request.symbol or order_request.quantity <= 0:
                return False
            
            # Limit order validation
            if order_request.order_type == OrderType.LIMIT and not order_request.price:
                return False
            
            # Options validation
            if order_request.option_type:
                if not all([order_request.strike_price, order_request.expiration_date]):
                    return False
            
            # Check for duplicate orders
            if order_request.order_id in self.pending_orders:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Order validation error: {e}")
            return False
    
    def _process_orders(self):
        """Process orders from the queue"""
        logger.info("Order processor started")
        
        while self.is_running:
            try:
                # Check if we have capacity for more orders
                if len(self.pending_orders) >= self.max_concurrent_orders:
                    time.sleep(1.0)
                    continue
                
                # Get next order from queue (with timeout)
                try:
                    priority, order_request = self.order_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # Execute the order
                self._execute_order(order_request)
                
            except Exception as e:
                logger.error(f"Error in order processor: {e}")
                time.sleep(1.0)
        
        logger.info("Order processor stopped")
    
    def _execute_order(self, order_request: OrderRequest):
        """Execute a single order with retry logic"""
        retry_count = 0
        
        while retry_count <= order_request.max_retries:
            try:
                # Check if order was cancelled
                if order_request.order_id not in self.pending_orders:
                    logger.info(f"Order {order_request.order_id} was cancelled before execution")
                    return
                
                # Execute the order
                if order_request.option_type:
                    result = self.client.place_option_order(
                        symbol=order_request.symbol,
                        strike_price=order_request.strike_price,
                        expiration_date=order_request.expiration_date,
                        option_type=order_request.option_type,
                        quantity=order_request.quantity,
                        side=order_request.side,
                        price=order_request.price
                    )
                else:
                    result = self.client.place_stock_order(
                        symbol=order_request.symbol,
                        quantity=order_request.quantity,
                        side=order_request.side,
                        order_type=order_request.order_type,
                        price=order_request.price
                    )
                
                if result.get('success'):
                    # Order submitted successfully
                    order_result = OrderResult(
                        order_id=order_request.order_id,
                        status=OrderStatus.SUBMITTED,
                        broker_order_id=result.get('order_id', '')
                    )
                    
                    self.completed_orders[order_request.order_id] = order_result
                    
                    logger.info(f"Order executed successfully: {order_request.order_id}")
                    
                    # Call success callback
                    if order_request.on_fill:
                        try:
                            order_request.on_fill(order_result)
                        except Exception as e:
                            logger.error(f"Error in order fill callback: {e}")
                    
                    self.stats['successful_orders'] += 1
                    return
                
                else:
                    # Order failed
                    error_msg = result.get('error', 'Unknown error')
                    logger.error(f"Order execution failed: {order_request.order_id} - {error_msg}")
                    
                    retry_count += 1
                    if retry_count <= order_request.max_retries:
                        logger.info(f"Retrying order {order_request.order_id} (attempt {retry_count})")
                        time.sleep(order_request.retry_delay)
                        self.stats['retry_count'] += 1
                    else:
                        # Max retries exceeded
                        order_result = OrderResult(
                            order_id=order_request.order_id,
                            status=OrderStatus.FAILED,
                            error_message=error_msg
                        )
                        
                        self.completed_orders[order_request.order_id] = order_result
                        
                        # Call error callback
                        if order_request.on_error:
                            try:
                                order_request.on_error(order_result)
                            except Exception as e:
                                logger.error(f"Error in order error callback: {e}")
                        
                        self.stats['failed_orders'] += 1
                        break
                
            except Exception as e:
                logger.error(f"Exception in order execution: {e}")
                retry_count += 1
                if retry_count <= order_request.max_retries:
                    time.sleep(order_request.retry_delay)
                else:
                    order_result = OrderResult(
                        order_id=order_request.order_id,
                        status=OrderStatus.FAILED,
                        error_message=str(e)
                    )
                    self.completed_orders[order_request.order_id] = order_result
                    self.stats['failed_orders'] += 1
                    break
        
        # Remove from pending orders
        if order_request.order_id in self.pending_orders:
            del self.pending_orders[order_request.order_id]
    
    def _monitor_orders(self):
        """Monitor submitted orders for fills and updates"""
        logger.info("Order monitor started")
        
        while self.is_running:
            try:
                # Check status of submitted orders
                orders_to_check = [
                    order for order in self.completed_orders.values()
                    if order.status == OrderStatus.SUBMITTED
                ]
                
                for order_result in orders_to_check:
                    try:
                        # Get order status from broker
                        status_info = self.client.get_order_status(order_result.broker_order_id)
                        
                        # Update order status
                        broker_status = status_info.get('status', '').lower()
                        
                        if broker_status in ['filled', 'completely_filled']:
                            order_result.status = OrderStatus.FILLED
                            order_result.filled_quantity = status_info.get('filled_quantity', 0)
                            order_result.average_fill_price = status_info.get('average_fill_price', 0.0)
                            order_result.filled_at = datetime.now()
                            
                            logger.info(f"Order filled: {order_result.order_id}")
                        
                        elif broker_status in ['cancelled', 'canceled']:
                            order_result.status = OrderStatus.CANCELLED
                            logger.info(f"Order cancelled: {order_result.order_id}")
                        
                        elif broker_status in ['rejected']:
                            order_result.status = OrderStatus.REJECTED
                            order_result.error_message = status_info.get('error', 'Order rejected')
                            logger.warning(f"Order rejected: {order_result.order_id}")
                        
                        elif broker_status in ['partially_filled']:
                            order_result.status = OrderStatus.PARTIALLY_FILLED
                            order_result.filled_quantity = status_info.get('filled_quantity', 0)
                            order_result.average_fill_price = status_info.get('average_fill_price', 0.0)
                    
                    except Exception as e:
                        logger.error(f"Error checking order status {order_result.order_id}: {e}")
                
                time.sleep(self.order_check_interval)
                
            except Exception as e:
                logger.error(f"Error in order monitor: {e}")
                time.sleep(self.order_check_interval)
        
        logger.info("Order monitor stopped")
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel a pending or submitted order"""
        try:
            # Remove from pending orders
            if order_id in self.pending_orders:
                order_request = self.pending_orders[order_id]
                del self.pending_orders[order_id]
                
                # Call cancel callback
                if order_request.on_cancel:
                    try:
                        order_request.on_cancel(order_id)
                    except Exception as e:
                        logger.error(f"Error in order cancel callback: {e}")
                
                logger.info(f"Pending order cancelled: {order_id}")
                self.stats['cancelled_orders'] += 1
                return True
            
            # Cancel submitted order
            if order_id in self.completed_orders:
                order_result = self.completed_orders[order_id]
                if order_result.status == OrderStatus.SUBMITTED:
                    success = self.client.cancel_order(order_result.broker_order_id)
                    if success:
                        order_result.status = OrderStatus.CANCELLED
                        logger.info(f"Submitted order cancelled: {order_id}")
                        self.stats['cancelled_orders'] += 1
                        return True
            
            logger.warning(f"Order not found or cannot be cancelled: {order_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    def cancel_all_orders(self):
        """Cancel all pending and submitted orders"""
        logger.info("Cancelling all orders")
        
        # Cancel pending orders
        pending_order_ids = list(self.pending_orders.keys())
        for order_id in pending_order_ids:
            self.cancel_order(order_id)
        
        # Cancel submitted orders
        submitted_orders = [
            order for order in self.completed_orders.values()
            if order.status == OrderStatus.SUBMITTED
        ]
        
        for order in submitted_orders:
            self.cancel_order(order.order_id)
    
    def get_order_status(self, order_id: str) -> Optional[OrderResult]:
        """Get the status of an order"""
        return self.completed_orders.get(order_id)
    
    def get_pending_orders(self) -> List[OrderRequest]:
        """Get all pending orders"""
        return list(self.pending_orders.values())
    
    def get_completed_orders(self) -> List[OrderResult]:
        """Get all completed orders"""
        return list(self.completed_orders.values())
    
    def get_statistics(self) -> Dict:
        """Get order management statistics"""
        return self.stats.copy()
