"""
Live Trading Engine for AWOT Trading Platform
Coordinates signals, strategies, risk management, and order execution for live trading
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from loguru import logger
from enum import Enum
import json

from trading.robinhood_client import RobinhoodClient, OrderSide, OptionType
from trading.order_manager import OrderManager, OrderPriority
from signals.signal_generator import SignalGenerator, TradingSignal, SignalType
from strategies.strategy import BaseStrategy, MomentumStrategy, BreakoutStrategy
from risk.risk_manager import RiskManager
from notifications.notification_manager import NotificationManager, NotificationType, NotificationPriority


class TradingMode(Enum):
    """Trading modes"""
    PAPER = "paper"
    LIVE = "live"
    SIMULATION = "simulation"


class TradingState(Enum):
    """Trading engine states"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"


class LiveTradingEngine:
    """
    Comprehensive live trading engine that coordinates all trading components.
    Handles signal generation, strategy execution, risk management, and order placement.
    """
    
    def __init__(self, trading_mode: TradingMode = TradingMode.PAPER):
        self.trading_mode = trading_mode
        self.state = TradingState.STOPPED
        
        # Core components
        self.robinhood_client = RobinhoodClient()
        self.order_manager = OrderManager(self.robinhood_client)
        self.signal_generator = SignalGenerator()
        self.risk_manager = RiskManager()
        self.notification_manager = NotificationManager()
        
        # Strategies
        self.strategies = {
            'momentum': MomentumStrategy(),
            'breakout': BreakoutStrategy()
        }
        self.active_strategies: Set[str] = {'momentum', 'breakout'}
        
        # Trading configuration
        self.config = {
            'max_positions': 3,
            'risk_per_trade': 0.10,
            'signal_check_interval': 30.0,  # seconds
            'position_check_interval': 60.0,  # seconds
            'max_daily_trades': 10,
            'trading_hours_start': '09:30',
            'trading_hours_end': '16:00',
            'symbols_to_trade': ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL']
        }
        
        # State tracking
        self.current_positions = {}
        self.daily_trades = 0
        self.last_signal_check = datetime.min
        self.last_position_check = datetime.min
        
        # Threading
        self.main_thread = None
        self.is_running = False
        
        # Statistics
        self.stats = {
            'signals_generated': 0,
            'signals_executed': 0,
            'trades_today': 0,
            'total_pnl': 0.0,
            'start_time': None,
            'uptime': 0.0
        }
        
        logger.info(f"Live Trading Engine initialized - Mode: {trading_mode.value}")
    
    def start(self) -> bool:
        """Start the live trading engine"""
        if self.state != TradingState.STOPPED:
            logger.warning(f"Cannot start engine in state: {self.state}")
            return False
        
        try:
            self.state = TradingState.STARTING
            logger.info("Starting Live Trading Engine...")
            
            # Authenticate with Robinhood
            if not self.robinhood_client.authenticate():
                logger.error("Failed to authenticate with Robinhood")
                self.state = TradingState.ERROR
                return False
            
            # Start order manager
            self.order_manager.start()
            
            # Initialize positions
            self._sync_positions()
            
            # Start main trading loop
            self.is_running = True
            self.main_thread = threading.Thread(
                target=self._main_trading_loop,
                daemon=True,
                name="LiveTradingEngine"
            )
            self.main_thread.start()
            
            self.state = TradingState.RUNNING
            self.stats['start_time'] = datetime.now()
            
            # Send startup notification
            self.notification_manager.send_system_status(
                "STARTED",
                f"Live Trading Engine started in {self.trading_mode.value} mode"
            )
            
            logger.info("Live Trading Engine started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting Live Trading Engine: {e}")
            self.state = TradingState.ERROR
            return False
    
    def stop(self):
        """Stop the live trading engine"""
        if self.state == TradingState.STOPPED:
            logger.warning("Engine already stopped")
            return
        
        try:
            self.state = TradingState.STOPPING
            logger.info("Stopping Live Trading Engine...")
            
            # Stop main loop
            self.is_running = False
            
            # Cancel all pending orders
            self.order_manager.cancel_all_orders()
            
            # Stop order manager
            self.order_manager.stop()
            
            # Wait for main thread to finish
            if self.main_thread:
                self.main_thread.join(timeout=10.0)
            
            # Logout from Robinhood
            self.robinhood_client.logout()
            
            self.state = TradingState.STOPPED
            
            # Send shutdown notification
            self.notification_manager.send_system_status(
                "STOPPED",
                "Live Trading Engine stopped"
            )
            
            logger.info("Live Trading Engine stopped")
            
        except Exception as e:
            logger.error(f"Error stopping Live Trading Engine: {e}")
            self.state = TradingState.ERROR
    
    def pause(self):
        """Pause trading (stop generating new signals but keep monitoring)"""
        if self.state == TradingState.RUNNING:
            self.state = TradingState.PAUSED
            logger.info("Live Trading Engine paused")
            
            self.notification_manager.send_system_status(
                "PAUSED",
                "Trading paused - monitoring positions only"
            )
    
    def resume(self):
        """Resume trading from paused state"""
        if self.state == TradingState.PAUSED:
            self.state = TradingState.RUNNING
            logger.info("Live Trading Engine resumed")
            
            self.notification_manager.send_system_status(
                "RESUMED",
                "Trading resumed"
            )
    
    def _main_trading_loop(self):
        """Main trading loop that runs continuously"""
        logger.info("Main trading loop started")
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Update uptime
                if self.stats['start_time']:
                    self.stats['uptime'] = (current_time - self.stats['start_time']).total_seconds()
                
                # Check if we're in trading hours
                if not self._is_trading_hours():
                    time.sleep(60)  # Check every minute outside trading hours
                    continue
                
                # Check and update positions
                if (current_time - self.last_position_check).total_seconds() >= self.config['position_check_interval']:
                    self._check_positions()
                    self.last_position_check = current_time
                
                # Generate and process signals (only if not paused)
                if (self.state == TradingState.RUNNING and 
                    (current_time - self.last_signal_check).total_seconds() >= self.config['signal_check_interval']):
                    
                    self._process_signals()
                    self.last_signal_check = current_time
                
                # Brief sleep to prevent excessive CPU usage
                time.sleep(1.0)
                
            except Exception as e:
                logger.error(f"Error in main trading loop: {e}")
                time.sleep(5.0)  # Wait before retrying
        
        logger.info("Main trading loop stopped")
    
    def _is_trading_hours(self) -> bool:
        """Check if current time is within trading hours"""
        now = datetime.now()
        
        # Skip weekends
        if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
        
        # Check time
        current_time = now.strftime('%H:%M')
        return self.config['trading_hours_start'] <= current_time <= self.config['trading_hours_end']
    
    def _sync_positions(self):
        """Sync current positions from broker"""
        try:
            # Get stock positions
            stock_positions = self.robinhood_client.get_portfolio_positions()
            
            # Get options positions
            options_positions = self.robinhood_client.get_options_positions()
            
            # Update current positions
            self.current_positions = {}
            
            for position in stock_positions + options_positions:
                symbol = position['symbol']
                self.current_positions[symbol] = position
            
            logger.info(f"Synced {len(self.current_positions)} positions")
            
        except Exception as e:
            logger.error(f"Error syncing positions: {e}")
    
    def _check_positions(self):
        """Check current positions for exit conditions"""
        try:
            # Sync latest positions
            self._sync_positions()
            
            for symbol, position in self.current_positions.items():
                # Check if any strategy wants to exit this position
                for strategy_name in self.active_strategies:
                    strategy = self.strategies.get(strategy_name)
                    if strategy:
                        # Convert position to strategy format (simplified)
                        # In real implementation, you'd properly convert the position
                        should_exit = False  # strategy.should_exit_position(position)
                        
                        if should_exit:
                            self._exit_position(symbol, position, f"Strategy exit: {strategy_name}")
            
        except Exception as e:
            logger.error(f"Error checking positions: {e}")
    
    def _process_signals(self):
        """Generate and process trading signals"""
        try:
            # Check daily trade limit
            if self.daily_trades >= self.config['max_daily_trades']:
                logger.info("Daily trade limit reached")
                return
            
            # Check position limit
            if len(self.current_positions) >= self.config['max_positions']:
                logger.info("Maximum positions reached")
                return
            
            # Generate signals for each symbol
            for symbol in self.config['symbols_to_trade']:
                try:
                    signals = self.signal_generator.generate_signals(symbol)
                    
                    for signal in signals:
                        self.stats['signals_generated'] += 1
                        
                        # Check if we should execute this signal
                        if self._should_execute_signal(signal):
                            success = self._execute_signal(signal)
                            if success:
                                self.stats['signals_executed'] += 1
                                self.daily_trades += 1
                                
                                # Send signal notification
                                self.notification_manager.send_signal_notification({
                                    'symbol': signal.symbol,
                                    'type': signal.signal_type.value,
                                    'confidence': signal.confidence,
                                    'entry_price': signal.entry_price,
                                    'target_price': signal.target_price
                                })
                
                except Exception as e:
                    logger.error(f"Error processing signals for {symbol}: {e}")
            
        except Exception as e:
            logger.error(f"Error in signal processing: {e}")
    
    def _should_execute_signal(self, signal: TradingSignal) -> bool:
        """Determine if a signal should be executed"""
        try:
            # Check if we already have a position in this symbol
            if signal.symbol in self.current_positions:
                return False
            
            # Check signal quality
            if signal.confidence < 0.6:
                return False
            
            # Check risk-reward ratio
            if signal.risk_reward_ratio < 1.5:
                return False
            
            # Check with risk manager
            position_size_info = self.risk_manager.calculate_position_size(
                signal, signal.entry_price, 50000, []  # Simplified
            )
            
            if position_size_info['position_size'] <= 0:
                return False
            
            # Check strategy approval
            for strategy_name in self.active_strategies:
                strategy = self.strategies.get(strategy_name)
                if strategy:
                    analysis = strategy.analyze_signal(signal)
                    if analysis.get('suitable', False):
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error evaluating signal: {e}")
            return False
    
    def _execute_signal(self, signal: TradingSignal) -> bool:
        """Execute a trading signal"""
        try:
            # Calculate position size
            position_size_info = self.risk_manager.calculate_position_size(
                signal, signal.entry_price, 50000, []  # Simplified
            )
            
            quantity = position_size_info['position_size']
            if quantity <= 0:
                return False
            
            # Determine if this is an options or stock trade
            if signal.option_type and signal.strike_price:
                # Options trade
                option_type = OptionType.CALL if signal.option_type == 'call' else OptionType.PUT
                
                order_id = self.order_manager.submit_option_order(
                    symbol=signal.symbol,
                    strike_price=signal.strike_price,
                    expiration_date=signal.expiration_date,
                    option_type=option_type,
                    quantity=quantity,
                    side=OrderSide.BUY,
                    price=signal.entry_price,
                    priority=OrderPriority.HIGH,
                    strategy_name="live_trading",
                    signal_id=signal.symbol + str(int(time.time()))
                )
            else:
                # Stock trade (fallback)
                order_id = self.order_manager.submit_stock_order(
                    symbol=signal.symbol,
                    quantity=quantity * 100,  # Convert to shares
                    side=OrderSide.BUY,
                    price=signal.entry_price,
                    priority=OrderPriority.HIGH,
                    strategy_name="live_trading",
                    signal_id=signal.symbol + str(int(time.time()))
                )
            
            if order_id:
                logger.info(f"Signal executed: {signal.symbol} - Order ID: {order_id}")
                return True
            else:
                logger.error(f"Failed to execute signal: {signal.symbol}")
                return False
            
        except Exception as e:
            logger.error(f"Error executing signal: {e}")
            return False
    
    def _exit_position(self, symbol: str, position: Dict, reason: str):
        """Exit a position"""
        try:
            quantity = int(position.get('quantity', 0))
            if quantity <= 0:
                return
            
            # Determine order type based on position type
            if position.get('type') == 'option':
                # Options exit
                order_id = self.order_manager.submit_option_order(
                    symbol=symbol,
                    strike_price=position.get('strike_price', 0),
                    expiration_date=position.get('expiration_date', ''),
                    option_type=OptionType.CALL if position.get('option_type') == 'call' else OptionType.PUT,
                    quantity=quantity,
                    side=OrderSide.SELL,
                    price=position.get('current_price', 0),
                    priority=OrderPriority.URGENT
                )
            else:
                # Stock exit
                order_id = self.order_manager.submit_stock_order(
                    symbol=symbol,
                    quantity=quantity,
                    side=OrderSide.SELL,
                    priority=OrderPriority.URGENT
                )
            
            if order_id:
                logger.info(f"Position exit initiated: {symbol} - {reason}")
                
                # Send trade notification
                self.notification_manager.send_trade_notification({
                    'symbol': symbol,
                    'action': 'SELL',
                    'quantity': quantity,
                    'price': position.get('current_price', 0),
                    'reason': reason,
                    'status': 'Submitted'
                })
            
        except Exception as e:
            logger.error(f"Error exiting position {symbol}: {e}")
    
    def get_status(self) -> Dict:
        """Get current engine status"""
        return {
            'state': self.state.value,
            'trading_mode': self.trading_mode.value,
            'active_strategies': list(self.active_strategies),
            'current_positions': len(self.current_positions),
            'daily_trades': self.daily_trades,
            'stats': self.stats.copy(),
            'config': self.config.copy()
        }
    
    def update_config(self, new_config: Dict):
        """Update trading configuration"""
        self.config.update(new_config)
        logger.info(f"Configuration updated: {new_config}")
    
    def enable_strategy(self, strategy_name: str):
        """Enable a trading strategy"""
        if strategy_name in self.strategies:
            self.active_strategies.add(strategy_name)
            logger.info(f"Strategy enabled: {strategy_name}")
    
    def disable_strategy(self, strategy_name: str):
        """Disable a trading strategy"""
        self.active_strategies.discard(strategy_name)
        logger.info(f"Strategy disabled: {strategy_name}")
    
    def emergency_stop(self):
        """Emergency stop - cancel all orders and close all positions"""
        logger.warning("EMERGENCY STOP INITIATED")
        
        try:
            # Cancel all pending orders
            self.order_manager.cancel_all_orders()
            
            # Close all positions
            for symbol, position in self.current_positions.items():
                self._exit_position(symbol, position, "Emergency stop")
            
            # Pause trading
            self.pause()
            
            # Send emergency notification
            self.notification_manager.send_risk_alert({
                'alert_type': 'Emergency Stop',
                'message': 'Emergency stop activated - all orders cancelled and positions closing',
                'risk_level': 'critical'
            })
            
            logger.warning("Emergency stop completed")
            
        except Exception as e:
            logger.error(f"Error during emergency stop: {e}")
