# AWOT Trading Platform - Production Docker Image
FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    cron \
    supervisor \
    nginx \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -u 1000 awot && \
    chown -R awot:awot /app

# Copy requirements first for better caching
COPY requirements.txt .
COPY requirements-prod.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-prod.txt

# Copy application code
COPY src/ ./src/
COPY dashboard/ ./dashboard/
COPY config/ ./config/
COPY deployment/ ./deployment/
COPY run_dashboard.py .
COPY run_trading.py .

# Copy configuration files
COPY deployment/nginx/nginx.conf /etc/nginx/nginx.conf
COPY deployment/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY deployment/scripts/ ./scripts/

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/backups && \
    chown -R awot:awot /app

# Copy startup script
COPY deployment/scripts/start.sh /start.sh
RUN chmod +x /start.sh

# Switch to non-root user
USER awot

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose ports
EXPOSE 8080 8501

# Start application
CMD ["/start.sh"]
