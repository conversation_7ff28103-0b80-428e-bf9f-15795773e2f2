#!/bin/bash

# AWOT Trading Platform Startup Script
set -e

echo "🚀 Starting AWOT Trading Platform..."

# Wait for dependencies
echo "⏳ Waiting for dependencies..."
./scripts/wait-for-it.sh postgres:5432 --timeout=60 --strict -- echo "✅ PostgreSQL is ready"
./scripts/wait-for-it.sh redis:6379 --timeout=60 --strict -- echo "✅ Redis is ready"

# Run database migrations
echo "🔄 Running database migrations..."
python scripts/migrate.py

# Initialize application data
echo "📊 Initializing application data..."
python scripts/init_data.py

# Start supervisor to manage all processes
echo "🎛️ Starting supervisor..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
