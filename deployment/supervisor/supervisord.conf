[supervisord]
nodaemon=true
user=awot
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid

[program:awot-dashboard]
command=python run_dashboard.py
directory=/app
user=awot
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/dashboard.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5

[program:awot-trading-engine]
command=python run_trading.py
directory=/app
user=awot
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/trading.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5

[program:awot-api]
command=gunicorn --bind 0.0.0.0:8080 --workers 4 --worker-class uvicorn.workers.UvicornWorker api.main:app
directory=/app
user=awot
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/api.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5

[program:awot-worker]
command=celery -A src.tasks.celery_app worker --loglevel=info
directory=/app
user=awot
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5

[program:awot-scheduler]
command=celery -A src.tasks.celery_app beat --loglevel=info
directory=/app
user=awot
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/scheduler.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/nginx.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
