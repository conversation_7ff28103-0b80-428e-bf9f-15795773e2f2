#!/usr/bin/env python3
'''
Production Mock Automated Trading System
This runs continuously with guaranteed mock signals for immediate testing
'''

import sys
import os
import time
import logging
from datetime import datetime, time as dt_time
import random

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from signals.signal_generator import TradingSignal, SignalType, SignalStrength
from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/mock_automated_trading.log'),
        logging.StreamHandler()
    ]
)

class ProductionMockTrader:
    """Production-ready mock automated trader"""
    
    def __init__(self):
        self.paper_engine = PaperTradingEngine()
        self.confidence_threshold = 0.70
        self.max_position_size = 0.08  # 8% of portfolio per position
        self.symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL', 'AMZN', 'META']
        self.signal_cycle_count = 0
        
        # Load existing portfolio state
        if os.path.exists('current_portfolio_state.json'):
            try:
                self.paper_engine.load_state('current_portfolio_state.json')
                portfolio = self.paper_engine.get_portfolio_summary()
                logging.info(f"✅ Loaded existing portfolio: ${portfolio['portfolio_value']:,.2f} value, {portfolio['open_positions']} positions")
            except Exception as e:
                logging.error(f"Error loading portfolio state: {e}")
                logging.info("Starting with fresh portfolio")
        else:
            logging.info("No existing portfolio found, starting fresh")
    
    def is_market_hours(self):
        '''Check if market is open (9:30 AM - 4:00 PM ET, weekdays)'''
        now = datetime.now()
        current_time = now.time()
        market_open = dt_time(9, 30)
        market_close = dt_time(16, 0)
        is_weekday = now.weekday() < 5
        
        return market_open <= current_time <= market_close and is_weekday
    
    def generate_realistic_mock_signals(self):
        """Generate realistic mock signals with varying conditions"""
        signals = []
        self.signal_cycle_count += 1
        
        # Vary signal generation based on cycle to simulate real conditions
        num_signals = random.randint(1, 4)  # 1-4 signals per cycle
        selected_symbols = random.sample(self.symbols, num_signals)
        
        for symbol in selected_symbols:
            # Generate realistic signal parameters
            signal_types = [
                SignalType.MOMENTUM_BULLISH,
                SignalType.MOMENTUM_BEARISH,
                SignalType.BREAKOUT_BULLISH,
                SignalType.REVERSAL_BULLISH,
                SignalType.REVERSAL_BEARISH
            ]
            
            signal_type = random.choice(signal_types)
            
            # Realistic confidence levels (some below threshold)
            confidence = random.uniform(0.55, 0.95)
            
            # Realistic price estimates
            price_estimates = {
                'AAPL': random.uniform(210, 220),
                'TSLA': random.uniform(320, 340),
                'MSFT': random.uniform(405, 420),
                'NVDA': random.uniform(480, 500),
                'GOOGL': random.uniform(185, 195),
                'AMZN': random.uniform(140, 150),
                'META': random.uniform(315, 325)
            }
            
            entry_price = price_estimates.get(symbol, 200.0)
            
            # Calculate target and stop loss
            if 'bullish' in signal_type.value.lower():
                target_price = entry_price * random.uniform(1.02, 1.08)  # 2-8% upside
                stop_loss = entry_price * random.uniform(0.95, 0.98)     # 2-5% downside
            else:
                target_price = entry_price * random.uniform(0.92, 0.98)  # 2-8% downside
                stop_loss = entry_price * random.uniform(1.02, 1.05)     # 2-5% upside
            
            # Realistic reasoning
            reasons = [
                f"Strong momentum detected with volume confirmation",
                f"Breakout above key resistance level",
                f"Oversold conditions with reversal signals",
                f"Positive earnings momentum building",
                f"Technical indicators showing bullish divergence",
                f"Market sentiment shift detected",
                f"Volume spike with price confirmation"
            ]
            
            signal = TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                strength=SignalStrength.STRONG if confidence > 0.8 else SignalStrength.MODERATE,
                confidence=confidence,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss_price=stop_loss,
                expiration_date=datetime.now().strftime('%Y-%m-%d'),
                reasoning=random.choice(reasons)
            )
            signals.append(signal)
        
        return signals
    
    def process_signals(self):
        '''Generate and process mock signals for trading'''
        try:
            # Generate realistic mock signals
            signals = self.generate_realistic_mock_signals()
            
            logging.info(f"🎯 Generated {len(signals)} mock signals (cycle #{self.signal_cycle_count})")
            trades_executed = 0
            
            for signal in signals:
                logging.info(f"\n📊 Processing signal: {signal.symbol}")
                logging.info(f"   Type: {signal.signal_type}")
                logging.info(f"   Confidence: {signal.confidence:.2%}")
                logging.info(f"   Reasoning: {signal.reasoning}")
                
                if signal.confidence >= self.confidence_threshold:
                    logging.info(f"   ✅ Signal meets threshold ({self.confidence_threshold:.0%})")
                    if self.execute_signal(signal):
                        trades_executed += 1
                else:
                    logging.info(f"   ❌ Signal below threshold ({signal.confidence:.2%} < {self.confidence_threshold:.0%})")
            
            return trades_executed
            
        except Exception as e:
            logging.error(f"Error processing signals: {e}")
            return 0
    
    def execute_signal(self, signal):
        '''Execute a trading signal'''
        try:
            # Determine action based on signal type
            signal_type_str = str(signal.signal_type.value).lower()
            
            if 'bullish' in signal_type_str or 'breakout_bullish' in signal_type_str:
                side = OrderSide.BUY
                action = 'BUY'
            elif 'bearish' in signal_type_str:
                side = OrderSide.SELL
                action = 'SELL'
            else:
                logging.info(f"   📊 Neutral signal, no action")
                return False
            
            # Get current portfolio
            portfolio = self.paper_engine.get_portfolio_summary()
            
            # For SELL orders, check if we have the position
            if action == 'SELL':
                positions = self.paper_engine.get_positions()
                current_position = None
                for pos in positions:
                    if pos['symbol'] == signal.symbol and pos['is_open']:
                        current_position = pos
                        break
                
                if not current_position:
                    logging.info(f"   ⚠️ No position in {signal.symbol} to sell, converting to BUY")
                    # Convert SELL signal to BUY for testing
                    side = OrderSide.BUY
                    action = 'BUY'
                else:
                    # Sell partial position
                    quantity = min(5, current_position['quantity'])
                    price = signal.entry_price
            
            # For BUY orders, use small fixed quantities
            if action == 'BUY':
                price = signal.entry_price
                
                # Use small quantities for testing
                test_quantities = {
                    'AAPL': 3,
                    'TSLA': 2,
                    'MSFT': 3,
                    'NVDA': 2,
                    'GOOGL': 1,
                    'AMZN': 1,
                    'META': 2
                }
                
                quantity = test_quantities.get(signal.symbol, 2)
                
                # Check cash availability
                required_cash = quantity * price
                if required_cash > portfolio['cash']:
                    quantity = 1
                    required_cash = quantity * price
                    if required_cash > portfolio['cash']:
                        logging.info(f"   ❌ Insufficient cash for {signal.symbol}")
                        return False
            
            logging.info(f"   🎯 Executing {action} {quantity} {signal.symbol} @ ${price:.2f}")
            
            # Execute trade
            result = self.paper_engine.place_order(
                symbol=signal.symbol,
                quantity=quantity,
                side=side,
                price=price,
                order_type='stock'
            )
            
            if result.get('success'):
                logging.info(f"   ✅ SUCCESS: {action} {quantity} {signal.symbol} @ ${result['execution_price']:.2f}")
                
                # Save state
                self.paper_engine.save_state('current_portfolio_state.json')
                return True
            else:
                logging.error(f"   ❌ FAILED: {result.get('error')}")
                return False
                
        except Exception as e:
            logging.error(f"   ❌ Error executing signal for {signal.symbol}: {e}")
            return False
    
    def run(self):
        '''Main trading loop'''
        logging.info("🚀 Starting Production Mock Automated Trading System")
        logging.info("=" * 60)
        
        # Show initial portfolio
        portfolio = self.paper_engine.get_portfolio_summary()
        logging.info(f"📊 Starting Portfolio: ${portfolio['portfolio_value']:,.2f}")
        
        try:
            while True:
                current_time = datetime.now()
                
                if self.is_market_hours():
                    logging.info(f"\n📈 Market is OPEN - Processing signals at {current_time.strftime('%H:%M:%S')}")
                    
                    trades = self.process_signals()
                    
                    if trades > 0:
                        logging.info(f"✅ Executed {trades} trades this cycle")
                        
                        # Show updated portfolio
                        portfolio = self.paper_engine.get_portfolio_summary()
                        logging.info(f"💰 Portfolio Value: ${portfolio['portfolio_value']:,.2f}")
                        logging.info(f"💵 Available Cash: ${portfolio['cash']:,.2f}")
                        logging.info(f"📊 Open Positions: {portfolio['open_positions']}")
                    else:
                        logging.info("📊 No trades executed this cycle")
                    
                    # Wait 10 minutes before next signal generation
                    logging.info("⏰ Waiting 10 minutes for next signal cycle...")
                    time.sleep(600)  # 10 minutes
                    
                else:
                    logging.info(f"🕐 Market is CLOSED - Waiting... (Current time: {current_time.strftime('%H:%M:%S')})")
                    time.sleep(300)  # 5 minutes when market closed
                
        except KeyboardInterrupt:
            logging.info("\n🛑 Stopping mock automated trading")
            
            # Final portfolio summary
            portfolio = self.paper_engine.get_portfolio_summary()
            logging.info(f"\n📊 Final Portfolio Summary:")
            logging.info(f"   Value: ${portfolio['portfolio_value']:,.2f}")
            logging.info(f"   Cash: ${portfolio['cash']:,.2f}")
            logging.info(f"   Positions: {portfolio['open_positions']}")
            logging.info(f"   Total Cycles: {self.signal_cycle_count}")
            
        except Exception as e:
            logging.error(f"Error in main loop: {e}")
            time.sleep(60)  # Wait 1 minute on error

def main():
    """Main function"""
    print("🚀 PRODUCTION MOCK AUTOMATED TRADING SYSTEM")
    print("=" * 60)
    print("This system generates realistic mock signals and executes trades automatically")
    print("✅ Guaranteed to work - perfect for testing and demonstration")
    print("📊 Generates 1-4 signals every 10 minutes during market hours")
    print("💰 Uses small position sizes for safe testing")
    print()
    print("Press Ctrl+C to stop")
    print("=" * 60)
    
    trader = ProductionMockTrader()
    trader.run()

if __name__ == "__main__":
    main()
