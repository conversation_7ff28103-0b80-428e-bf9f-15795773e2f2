<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>AWOT Mobile Trading</title>
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#007AFF">
    <link rel="apple-touch-icon" href="icon-192.png">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 15px 15px;
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 1.5em;
            font-weight: 700;
            color: #007AFF;
            text-align: center;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #34C759;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .container {
            padding: 15px;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .portfolio-value {
            font-size: 2.5em;
            font-weight: 800;
            color: #007AFF;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .pnl-container {
            text-align: center;
            font-size: 1.2em;
            font-weight: 600;
        }
        
        .pnl-positive { color: #34C759; }
        .pnl-negative { color: #FF3B30; }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        
        .metric {
            text-align: center;
            padding: 15px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 12px;
        }
        
        .metric-value {
            font-size: 1.4em;
            font-weight: 700;
            color: #007AFF;
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .signal-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007AFF;
            position: relative;
        }
        
        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .signal-symbol {
            font-size: 1.2em;
            font-weight: 700;
            color: #007AFF;
        }
        
        .signal-type {
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 12px;
            background: #007AFF;
            color: white;
            text-transform: uppercase;
        }
        
        .signal-confidence {
            font-size: 1.1em;
            font-weight: 600;
            color: #34C759;
        }
        
        .signal-prices {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .price-item {
            text-align: center;
            padding: 8px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 8px;
        }
        
        .emergency-button {
            background: linear-gradient(135deg, #FF3B30, #FF6B6B);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 20px;
            font-size: 1.2em;
            font-weight: 700;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(255, 59, 48, 0.3);
            transition: transform 0.2s;
        }
        
        .emergency-button:active {
            transform: scale(0.95);
        }
        
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            padding: 10px 0 30px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            color: #666;
            text-decoration: none;
            font-size: 0.8em;
            transition: color 0.2s;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-icon {
            font-size: 1.5em;
            margin-bottom: 5px;
            display: block;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .sentiment-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 12px;
            background: rgba(52, 199, 89, 0.1);
            color: #34C759;
        }
        
        .sentiment-negative {
            background: rgba(255, 59, 48, 0.1);
            color: #FF3B30;
        }
        
        .hidden {
            display: none;
        }
        
        /* iOS Safari specific fixes */
        @supports (-webkit-touch-callout: none) {
            .container {
                padding-bottom: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 AWOT Trading</h1>
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span id="connectionStatus">Connected</span>
            </div>
            <div id="marketStatus">Market: Closed</div>
            <div id="lastUpdate">Updated: --:--</div>
        </div>
    </div>

    <div class="container">
        <!-- Portfolio Section -->
        <div id="portfolioTab" class="tab-content">
            <div class="card">
                <div class="portfolio-value" id="portfolioValue">$50,000.00</div>
                <div class="pnl-container">
                    <div id="dailyPnL" class="pnl-positive">+$0.00 (0.0%)</div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="openPositions">0</div>
                        <div class="metric-label">Positions</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="todayTrades">0</div>
                        <div class="metric-label">Today's Trades</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="winRate">--</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="availableCash">$50,000</div>
                        <div class="metric-label">Available Cash</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Signals Section -->
        <div id="signalsTab" class="tab-content hidden">
            <div class="card">
                <h2 style="margin-bottom: 15px; color: #007AFF;">🎯 Active Signals</h2>
                <div id="signalsList">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>Loading signals...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trading Controls -->
        <div id="tradingTab" class="tab-content hidden">
            <div class="card">
                <h2 style="margin-bottom: 15px; color: #007AFF;">🤖 Trading Engine</h2>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-value" id="engineStatus">Stopped</div>
                        <div class="metric-label">Status</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="tradingMode">Paper</div>
                        <div class="metric-label">Mode</div>
                    </div>
                </div>
                
                <button class="emergency-button" onclick="emergencyStop()">
                    🚨 EMERGENCY STOP
                </button>
            </div>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <a href="#" class="tab-item active" onclick="showTab('portfolio')">
            <span class="tab-icon">💰</span>
            Portfolio
        </a>
        <a href="#" class="tab-item" onclick="showTab('signals')">
            <span class="tab-icon">🎯</span>
            Signals
        </a>
        <a href="#" class="tab-item" onclick="showTab('trading')">
            <span class="tab-icon">🤖</span>
            Trading
        </a>
    </div>

    <script>
        // Configuration
        const API_BASE = window.location.origin.replace(':8502', ':8080');
        let ws = null;
        let reconnectInterval = null;

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            connectWebSocket();
            loadInitialData();
            
            // Register service worker for PWA
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('sw.js').catch(console.error);
            }
            
            // Request notification permission
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
        });

        function initializeApp() {
            updateLastUpdate();
            setInterval(updateLastUpdate, 30000); // Update every 30 seconds
        }

        function connectWebSocket() {
            const wsUrl = API_BASE.replace('http', 'ws') + '/ws';
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    console.log('WebSocket connected');
                    document.getElementById('connectionStatus').textContent = 'Connected';
                    if (reconnectInterval) {
                        clearInterval(reconnectInterval);
                        reconnectInterval = null;
                    }
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };
                
                ws.onclose = function() {
                    console.log('WebSocket disconnected');
                    document.getElementById('connectionStatus').textContent = 'Disconnected';
                    
                    // Attempt to reconnect
                    if (!reconnectInterval) {
                        reconnectInterval = setInterval(connectWebSocket, 5000);
                    }
                };
                
                ws.onerror = function(error) {
                    console.error('WebSocket error:', error);
                };
                
            } catch (error) {
                console.error('Failed to connect WebSocket:', error);
                document.getElementById('connectionStatus').textContent = 'Error';
            }
        }

        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'portfolio_update':
                    updatePortfolioDisplay(data.data);
                    break;
                case 'signal_update':
                    updateSignalsDisplay(data.data);
                    break;
                case 'price_update':
                    updatePrices(data.data);
                    break;
                case 'notification':
                    showNotification(data.data);
                    break;
            }
            updateLastUpdate();
        }

        async function loadInitialData() {
            try {
                // Load portfolio data
                const portfolioResponse = await fetch(`${API_BASE}/trading/status`);
                if (portfolioResponse.ok) {
                    const portfolioData = await portfolioResponse.json();
                    updatePortfolioDisplay(portfolioData);
                }
                
                // Load signals data
                loadSignals();
                
            } catch (error) {
                console.error('Error loading initial data:', error);
            }
        }

        async function loadSignals() {
            try {
                // For now, show sample signals since we don't have a signals endpoint yet
                const sampleSignals = [
                    {
                        symbol: 'AAPL',
                        type: 'momentum_bullish',
                        confidence: 0.85,
                        entry_price: 211.22,
                        target_price: 230.00,
                        stop_loss: 200.00,
                        sentiment: 'positive'
                    },
                    {
                        symbol: 'TSLA',
                        type: 'breakout_bullish',
                        confidence: 0.78,
                        entry_price: 329.55,
                        target_price: 365.00,
                        stop_loss: 310.00,
                        sentiment: 'very_positive'
                    }
                ];
                
                updateSignalsDisplay(sampleSignals);
                
            } catch (error) {
                console.error('Error loading signals:', error);
                document.getElementById('signalsList').innerHTML = 
                    '<div style="text-align: center; color: #666;">Error loading signals</div>';
            }
        }

        function updatePortfolioDisplay(data) {
            // Update portfolio value
            const portfolioValue = data.portfolio_value || 50000;
            document.getElementById('portfolioValue').textContent = 
                `$${portfolioValue.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
            
            // Update P&L
            const dailyPnL = data.daily_pnl || 0;
            const dailyPnLPercent = data.daily_pnl_percent || 0;
            const pnlElement = document.getElementById('dailyPnL');
            pnlElement.textContent = 
                `${dailyPnL >= 0 ? '+' : ''}$${Math.abs(dailyPnL).toLocaleString('en-US', {minimumFractionDigits: 2})} (${dailyPnLPercent >= 0 ? '+' : ''}${dailyPnLPercent.toFixed(1)}%)`;
            pnlElement.className = dailyPnL >= 0 ? 'pnl-positive' : 'pnl-negative';
            
            // Update metrics
            document.getElementById('openPositions').textContent = data.open_positions || 0;
            document.getElementById('todayTrades').textContent = data.todays_trades || 0;
            document.getElementById('winRate').textContent = data.win_rate ? `${(data.win_rate * 100).toFixed(1)}%` : '--';
            document.getElementById('availableCash').textContent = 
                `$${(data.available_cash || 50000).toLocaleString('en-US')}`;
            
            // Update trading engine status
            document.getElementById('engineStatus').textContent = data.state || 'Stopped';
            document.getElementById('tradingMode').textContent = data.trading_mode || 'Paper';
        }

        function updateSignalsDisplay(signals) {
            const signalsList = document.getElementById('signalsList');
            
            if (!signals || signals.length === 0) {
                signalsList.innerHTML = 
                    '<div style="text-align: center; color: #666;">No active signals</div>';
                return;
            }
            
            signalsList.innerHTML = signals.map(signal => `
                <div class="signal-card">
                    <div class="signal-header">
                        <div class="signal-symbol">${signal.symbol}</div>
                        <div class="signal-type">${signal.type.replace('_', ' ')}</div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <div class="signal-confidence">${(signal.confidence * 100).toFixed(0)}% confidence</div>
                        <div class="sentiment-indicator ${signal.sentiment === 'negative' || signal.sentiment === 'very_negative' ? 'sentiment-negative' : ''}">
                            ${signal.sentiment === 'very_positive' ? '🚀' : signal.sentiment === 'positive' ? '📈' : signal.sentiment === 'negative' ? '📉' : '😐'} ${signal.sentiment}
                        </div>
                    </div>
                    <div class="signal-prices">
                        <div class="price-item">
                            <div style="font-weight: 600;">Entry</div>
                            <div>$${signal.entry_price.toFixed(2)}</div>
                        </div>
                        <div class="price-item">
                            <div style="font-weight: 600;">Target</div>
                            <div>$${signal.target_price.toFixed(2)}</div>
                        </div>
                        <div class="price-item">
                            <div style="font-weight: 600;">Stop</div>
                            <div>$${signal.stop_loss.toFixed(2)}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updatePrices(prices) {
            // Update any displayed prices with real-time data
            console.log('Price update:', prices);
        }

        function showNotification(notification) {
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification(notification.title, {
                    body: notification.message,
                    icon: 'icon-192.png'
                });
            }
        }

        function updateLastUpdate() {
            const now = new Date();
            document.getElementById('lastUpdate').textContent = 
                `Updated: ${now.toLocaleTimeString('en-US', {hour12: false, hour: '2-digit', minute: '2-digit'})}`;
        }

        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.remove('hidden');
            
            // Update tab bar
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.tab-item').classList.add('active');
            
            // Load data for specific tabs
            if (tabName === 'signals') {
                loadSignals();
            }
        }

        async function emergencyStop() {
            if (confirm('⚠️ Are you sure you want to STOP ALL TRADING?\n\nThis will:\n• Stop the trading engine\n• Cancel pending orders\n• Prevent new trades')) {
                try {
                    const response = await fetch(`${API_BASE}/trading/emergency-stop`, {
                        method: 'POST'
                    });
                    
                    if (response.ok) {
                        alert('🚨 EMERGENCY STOP ACTIVATED!\n\nAll trading has been halted.');
                        loadInitialData(); // Refresh data
                    } else {
                        alert('❌ Failed to activate emergency stop. Please check manually.');
                    }
                } catch (error) {
                    console.error('Emergency stop error:', error);
                    alert('❌ Error activating emergency stop. Please check your connection.');
                }
            }
        }

        // Handle app lifecycle
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                loadInitialData();
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    connectWebSocket();
                }
            }
        });
    </script>
</body>
</html>
