#!/usr/bin/env python3
"""Simple local monitoring for AWOT platform"""

import time
import psutil
import requests
import json
from datetime import datetime

def check_services():
    """Check if services are running"""
    services = {
        'dashboard': 'http://localhost:8502',
        'api': 'http://localhost:8080/health'
    }
    
    status = {}
    
    for service, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            status[service] = response.status_code == 200
        except:
            status[service] = False
    
    return status

def log_metrics():
    """Log system metrics"""
    cpu_percent = psutil.cpu_percent()
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'disk_percent': disk.percent,
        'services': check_services()
    }
    
    # Log to file
    with open('logs/monitoring.log', 'a') as f:
        f.write(json.dumps(metrics) + '\n')
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] CPU: {cpu_percent:.1f}% | "
          f"Memory: {memory.percent:.1f}% | Disk: {disk.percent:.1f}%")

if __name__ == "__main__":
    print("🔍 AWOT Local Monitoring Started")
    
    while True:
        try:
            log_metrics()
            time.sleep(30)  # Check every 30 seconds
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped")
            break
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
            time.sleep(60)
