#!/usr/bin/env python3
"""
Check AWOT PWA and API server status
"""

import requests
import sys

def check_server(url, name):
    """Check if a server is responding"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print(f"✅ {name}: {url} - WORKING")
            return True
        else:
            print(f"❌ {name}: {url} - HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ {name}: {url} - CONNECTION REFUSED")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ {name}: {url} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {name}: {url} - ERROR: {e}")
        return False

def main():
    print("🔍 AWOT Server Status Check")
    print("=" * 50)
    
    # Check API server
    api_working = check_server("http://localhost:8080/mobile/health", "API Server")
    
    # Check PWA server on different ports
    pwa_ports = [8001, 3000, 8000, 8002]
    pwa_working = False
    
    for port in pwa_ports:
        if check_server(f"http://localhost:{port}/", f"PWA Server (port {port})"):
            pwa_working = True
            print(f"🎯 Use this URL: http://localhost:{port}")
            break
    
    print("\n" + "=" * 50)
    
    if api_working and pwa_working:
        print("🎉 BOTH SERVERS WORKING!")
        print("\n📱 Next Steps:")
        print("1. Open the PWA URL in your browser")
        print("2. On mobile: Add to Home Screen")
        print("3. Test portfolio and signals")
        
    elif api_working and not pwa_working:
        print("⚠️  API working, but PWA server not found")
        print("\n🔧 Start PWA server:")
        print("cd /home/<USER>/Documents/AWOT/pwa")
        print("python3 simple_server.py")
        
    elif not api_working and pwa_working:
        print("⚠️  PWA working, but API server not responding")
        print("\n🔧 Start API server:")
        print("cd /home/<USER>/Documents/AWOT")
        print("source venv/bin/activate")
        print("python -c \"import uvicorn; import sys; sys.path.append('src'); from api.main import app; uvicorn.run(app, host='0.0.0.0', port=8080)\"")
        
    else:
        print("❌ BOTH SERVERS NOT WORKING")
        print("\n🔧 Start both servers:")
        print("Terminal 1: cd /home/<USER>/Documents/AWOT && source venv/bin/activate && python -c \"import uvicorn; import sys; sys.path.append('src'); from api.main import app; uvicorn.run(app, host='0.0.0.0', port=8080)\"")
        print("Terminal 2: cd /home/<USER>/Documents/AWOT/pwa && python3 simple_server.py")

if __name__ == "__main__":
    try:
        import requests
        main()
    except ImportError:
        print("❌ requests module not found")
        print("📦 Install with: pip install requests")
        print("🔄 Or use curl to test manually:")
        print("curl http://localhost:8080/mobile/health")
        print("curl http://localhost:8001/")
