
import uvicorn
import sys
import os
sys.path.append('src')

try:
    from api.main import app
    print("🚀 Starting AWOT API with mobile endpoints...")
    print("📱 Mobile API: http://localhost:8080/mobile/")
    print("📊 API docs: http://localhost:8080/docs")
    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
except Exception as e:
    print(f"❌ Failed to start API: {e}")
    print("💡 Make sure you're in the AWOT directory")
