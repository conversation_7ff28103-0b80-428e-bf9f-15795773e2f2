#!/usr/bin/env python3
'''
Test Full Options Trading Flow
This tests the complete options trading flow from signal generation to execution
'''

import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_full_options_trading():
    """Test the complete options trading flow"""
    print("🎯 TESTING FULL OPTIONS TRADING FLOW")
    print("=" * 60)
    
    try:
        from signals.signal_generator import TradingSignal, SignalType, SignalStrength
        from trading.paper_trading import PaperTradingEngine
        from trading.robinhood_client import OrderSide, OptionType
        
        # Initialize paper trading engine
        print("📊 Initializing paper trading engine...")
        paper_engine = PaperTradingEngine()
        
        # Create a realistic weekly options signal
        print("\n📈 Creating weekly options signal...")
        
        weekly_options_signal = TradingSignal(
            symbol='AAPL',
            signal_type=SignalType.MOMENTUM_BULLISH,
            strength=SignalStrength.STRONG,
            confidence=0.75,
            entry_price=5.50,  # Option premium price
            target_price=8.00,  # Target option price
            stop_loss_price=3.00,  # Stop loss option price
            expiration_date=(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
            option_type='call',
            strike_price=215.00,  # Strike price
            reasoning="Weekly momentum breakout signal"
        )
        
        print("✅ Weekly options signal created:")
        print(f"   Symbol: {weekly_options_signal.symbol}")
        print(f"   Option Type: {weekly_options_signal.option_type}")
        print(f"   Strike Price: ${weekly_options_signal.strike_price}")
        print(f"   Expiration: {weekly_options_signal.expiration_date}")
        print(f"   Option Premium: ${weekly_options_signal.entry_price}")
        print(f"   Target Premium: ${weekly_options_signal.target_price}")
        
        # Test options order execution
        print("\n🔄 Testing options order execution...")
        
        result = paper_engine.place_order(
            symbol=weekly_options_signal.symbol,
            quantity=5,  # 5 contracts
            side=OrderSide.BUY,
            price=weekly_options_signal.entry_price,
            order_type='option',
            strike_price=weekly_options_signal.strike_price,
            expiration_date=weekly_options_signal.expiration_date,
            option_type=OptionType.CALL,
            strategy_name="weekly_options_test",
            signal_id="TEST_001"
        )
        
        if result.get('success'):
            print("✅ OPTIONS ORDER EXECUTED SUCCESSFULLY!")
            print(f"   Order ID: {result.get('order_id')}")
            print(f"   Execution Price: ${result.get('execution_price', 0):.2f}")
            print(f"   Total Cost: ${result.get('total_cost', 0):.2f}")
            
            # Check portfolio
            print("\n📊 Checking portfolio after options trade...")
            positions = paper_engine.get_positions()
            trade_history = paper_engine.get_trade_history()
            
            print(f"   Total Positions: {len(positions)}")
            print(f"   Total Trades: {len(trade_history)}")
            
            if positions:
                latest_position = positions[-1]
                print(f"\n   📈 Latest Position:")
                print(f"      Symbol: {latest_position['symbol']}")
                print(f"      Type: {latest_position['position_type']}")
                print(f"      Quantity: {latest_position['quantity']}")
                print(f"      Strike: ${latest_position.get('strike_price', 'N/A')}")
                print(f"      Expiry: {latest_position.get('expiration_date', 'N/A')}")
                print(f"      Option Type: {latest_position.get('option_type', 'N/A')}")
                
                if latest_position['position_type'] == 'option':
                    print("      ✅ CONFIRMED: OPTIONS POSITION CREATED!")
                else:
                    print("      ❌ UNEXPECTED: Stock position created instead")
            
            if trade_history:
                latest_trade = trade_history[-1]
                print(f"\n   📈 Latest Trade:")
                print(f"      Symbol: {latest_trade['symbol']}")
                print(f"      Type: {latest_trade['trade_type']}")
                print(f"      Side: {latest_trade['side']}")
                print(f"      Quantity: {latest_trade['quantity']}")
                print(f"      Price: ${latest_trade['price']:.2f}")
                print(f"      Strike: ${latest_trade.get('strike_price', 'N/A')}")
                print(f"      Expiry: {latest_trade.get('expiration_date', 'N/A')}")
                print(f"      Option Type: {latest_trade.get('option_type', 'N/A')}")
                
                if latest_trade['trade_type'] == 'option':
                    print("      ✅ CONFIRMED: OPTIONS TRADE RECORDED!")
                else:
                    print("      ❌ UNEXPECTED: Stock trade recorded instead")
        else:
            print("❌ OPTIONS ORDER FAILED!")
            print(f"   Error: {result.get('error', 'Unknown error')}")
            return False
        
        print("\n🎉 FULL OPTIONS TRADING TEST COMPLETE!")
        return True
        
    except Exception as e:
        print(f"❌ Error in full options trading test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_options_trading()
    if success:
        print("\n🎉 SUCCESS: Full options trading flow is working!")
        print("📈 Your AWOT system is ready for automated weekly options trading!")
        print("🎯 Next: Run the automated trading system to see live options trades!")
    else:
        print("\n❌ FAILED: Options trading flow needs more work")
