#!/usr/bin/env python3
'''
Test Fixed Automated Trading Script - Single Cycle
This tests the automated trading system with a single cycle
'''

import sys
import os
import time
import logging
from datetime import datetime, time as dt_time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Set environment variable to force signal generation for testing
os.environ['FORCE_SIGNAL_GENERATION'] = 'true'

from signals.enhanced_signal_generator import EnhancedSignalGenerator
from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automated_trading_test.log'),
        logging.StreamHandler()
    ]
)

class TestAutomatedTrader:
    def __init__(self):
        self.signal_generator = EnhancedSignalGenerator()
        self.paper_engine = PaperTradingEngine()
        self.confidence_threshold = float(os.getenv('SIGNAL_CONFIDENCE_THRESHOLD', '0.70'))
        self.max_position_size = float(os.getenv('MAX_POSITION_SIZE', '0.10'))
        self.symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL']
        
        # Load existing portfolio state
        if os.path.exists('current_portfolio_state.json'):
            try:
                self.paper_engine.load_state('current_portfolio_state.json')
                portfolio = self.paper_engine.get_portfolio_summary()
                logging.info(f"✅ Loaded existing portfolio: ${portfolio['portfolio_value']:,.2f} value, {portfolio['open_positions']} positions")
            except Exception as e:
                logging.error(f"Error loading portfolio state: {e}")
                logging.info("Starting with fresh portfolio")
        else:
            logging.info("No existing portfolio found, starting fresh")
    
    def process_signals(self):
        '''Generate and process signals for trading'''
        try:
            # Generate signals for each symbol
            logging.info(f"🎯 Generating signals for: {', '.join(self.symbols)}")
            signals = []

            for symbol in self.symbols:
                try:
                    symbol_signals = self.signal_generator.generate_signals(symbol)
                    if symbol_signals:
                        signals.extend(symbol_signals)
                        logging.info(f"   ✅ Generated {len(symbol_signals)} signals for {symbol}")
                    else:
                        logging.info(f"   ⚠️ No signals for {symbol}")
                except Exception as e:
                    logging.error(f"   ❌ Error generating signals for {symbol}: {e}")
            
            if not signals:
                logging.info("❌ No signals generated")
                return 0
            
            logging.info(f"✅ Generated {len(signals)} signals")
            trades_executed = 0
            
            for signal in signals:
                logging.info(f"\n📊 Processing signal: {signal.symbol}")
                logging.info(f"   Type: {signal.signal_type}")
                logging.info(f"   Confidence: {signal.confidence:.2%}")
                
                if signal.confidence >= self.confidence_threshold:
                    logging.info(f"   ✅ Signal meets threshold ({self.confidence_threshold:.0%})")
                    if self.execute_signal(signal):
                        trades_executed += 1
                else:
                    logging.info(f"   ❌ Signal below threshold ({signal.confidence:.2%} < {self.confidence_threshold:.0%})")
            
            return trades_executed
            
        except Exception as e:
            logging.error(f"Error processing signals: {e}")
            return 0
    
    def execute_signal(self, signal):
        '''Execute a trading signal'''
        try:
            # Determine action
            if 'bullish' in signal.signal_type.lower():
                side = OrderSide.BUY
                action = 'BUY'
            elif 'bearish' in signal.signal_type.lower():
                side = OrderSide.SELL
                action = 'SELL'
            else:
                logging.info(f"   📊 Neutral signal, no action")
                return False
            
            # Calculate position size
            portfolio = self.paper_engine.get_portfolio_summary()
            max_value = portfolio['portfolio_value'] * self.max_position_size
            
            # Use realistic price estimates
            price_estimates = {
                'AAPL': 213.25,
                'TSLA': 328.50,
                'MSFT': 412.00,
                'NVDA': 485.75,
                'GOOGL': 2750.00
            }
            
            estimated_price = price_estimates.get(signal.symbol, 200.00)
            quantity = max(1, int(max_value / estimated_price))
            
            # For SELL orders, check if we have the position
            if action == 'SELL':
                positions = self.paper_engine.get_positions()
                current_position = None
                for pos in positions:
                    if pos['symbol'] == signal.symbol and pos['is_open']:
                        current_position = pos
                        break
                
                if not current_position:
                    logging.info(f"   ⚠️ No position in {signal.symbol} to sell")
                    return False
                
                # Sell partial position
                quantity = min(quantity, current_position['quantity'])
            
            # Check cash for BUY orders
            if action == 'BUY':
                required_cash = quantity * estimated_price
                if required_cash > portfolio['cash']:
                    quantity = max(1, int(portfolio['cash'] / estimated_price))
                    if quantity < 1:
                        logging.info(f"   ❌ Insufficient cash for {signal.symbol}")
                        return False
            
            logging.info(f"   🎯 Executing {action} {quantity} {signal.symbol} @ ${estimated_price:.2f}")
            
            # Execute trade
            result = self.paper_engine.place_order(
                symbol=signal.symbol,
                quantity=quantity,
                side=side,
                price=estimated_price,
                order_type='market'
            )
            
            if result.get('success'):
                logging.info(f"   ✅ SUCCESS: {action} {quantity} {signal.symbol} @ ${result['execution_price']:.2f}")
                
                # Save state
                self.paper_engine.save_state('current_portfolio_state.json')
                return True
            else:
                logging.error(f"   ❌ FAILED: {result.get('error')}")
                return False
                
        except Exception as e:
            logging.error(f"   ❌ Error executing signal for {signal.symbol}: {e}")
            return False
    
    def run_single_test(self):
        '''Run a single test cycle'''
        logging.info("🚀 Starting Single Cycle Automated Trading Test")
        
        # Show current portfolio
        portfolio = self.paper_engine.get_portfolio_summary()
        logging.info(f"📊 Current Portfolio:")
        logging.info(f"   Value: ${portfolio['portfolio_value']:,.2f}")
        logging.info(f"   Cash: ${portfolio['cash']:,.2f}")
        logging.info(f"   Positions: {portfolio['open_positions']}")
        
        # Process signals
        trades = self.process_signals()
        
        # Show results
        logging.info(f"\n📈 Test Results:")
        logging.info(f"   Trades executed: {trades}")
        
        # Show updated portfolio
        portfolio = self.paper_engine.get_portfolio_summary()
        logging.info(f"   New portfolio value: ${portfolio['portfolio_value']:,.2f}")
        logging.info(f"   New cash: ${portfolio['cash']:,.2f}")
        logging.info(f"   New positions: {portfolio['open_positions']}")
        
        return trades

def main():
    """Main test function"""
    print("🧪 Testing Fixed Automated Trading System")
    print("=" * 60)
    
    trader = TestAutomatedTrader()
    trades_executed = trader.run_single_test()
    
    print("\n" + "=" * 60)
    print("🎯 TEST RESULTS:")
    
    if trades_executed > 0:
        print(f"✅ SUCCESS: {trades_executed} automated trades executed!")
        print("🎯 The automated trading pipeline is working!")
        print("📊 Check your dashboard to see the new trades")
        print("💰 Check logs/automated_trading_test.log for details")
    else:
        print("⚠️ No trades executed")
        print("🔍 Possible reasons:")
        print("   - No signals generated (check enhanced signal generator)")
        print("   - Signals below confidence threshold (70%)")
        print("   - Insufficient cash or positions for trades")
        print("   - Check logs/automated_trading_test.log for details")
    
    print("\n💡 Next Steps:")
    print("1. Check dashboard at http://localhost:8502")
    print("2. Review Trade Log tab for new automated trades")
    print("3. Monitor Portfolio tab for updated values")
    print("4. If working, start full automated system with:")
    print("   python run_fixed_automated_trading.py")

if __name__ == "__main__":
    main()
