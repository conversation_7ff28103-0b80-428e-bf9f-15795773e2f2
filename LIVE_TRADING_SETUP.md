# 🔧 AWOT Live Trading Setup Guide

## 🎯 Current Status
✅ **Paper trading is running successfully**
✅ **Dashboard active at http://localhost:8502**
✅ **Ready to configure live trading**

---

## 🔐 STEP B: Configure Live Trading Credentials

### **Required Credentials for Live Trading:**

#### 1. **Robinhood Account Setup**
```bash
# You'll need:
✅ Robinhood username
✅ Robinhood password  
✅ 2FA secret key (from authenticator app)
```

#### 2. **Optional Notification Setup**
```bash
# For alerts and notifications:
📧 Email SMTP settings (Gmail recommended)
📱 Telegram bot token (optional)
📲 Twilio SMS settings (optional)
```

#### 3. **Market Data API**
```bash
# For enhanced market data:
📊 Alpha Vantage API key (free)
```

---

## 🛠️ **STEP-BY-STEP CONFIGURATION**

### **Step 1: Robinhood 2FA Setup**

#### **Get Your 2FA Secret:**
1. **Open Robinhood app** on your phone
2. **Go to Settings** → **Security** → **Two-Factor Authentication**
3. **If 2FA is already enabled:**
   - Tap "Change Method" or "Backup Codes"
   - Choose "Authenticator App"
   - **IMPORTANT**: When it shows the QR code, tap "Can't scan?"
   - **Copy the secret key** (long string of letters/numbers)
4. **If 2FA is not enabled:**
   - Enable it and choose "Authenticator App"
   - **Copy the secret key** when shown

#### **Test Your 2FA:**
```bash
# You can test 2FA with Python:
python -c "
import pyotp
secret = 'YOUR_SECRET_HERE'  # Replace with your actual secret
totp = pyotp.TOTP(secret)
print(f'Current 2FA code: {totp.now()}')
"
```

### **Step 2: Configure .env File**

#### **Edit your credentials:**
```bash
# Open the configuration file
nano .env

# Update these required fields:
ROBINHOOD_USERNAME=your_actual_username
ROBINHOOD_PASSWORD=your_actual_password
ROBINHOOD_TOTP_SECRET=your_2fa_secret_key

# Change to live trading mode
PAPER_TRADING=false

# Optional: Configure notifications
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_gmail_app_password
EMAIL_TO=<EMAIL>
```

### **Step 3: Get Alpha Vantage API Key (Free)**
1. **Visit**: https://www.alphavantage.co/support/#api-key
2. **Sign up** for free account
3. **Copy your API key**
4. **Add to .env**:
   ```bash
   ALPHA_VANTAGE_API_KEY=your_api_key_here
   ```

### **Step 4: Gmail App Password (Optional)**
1. **Enable 2FA** on your Gmail account
2. **Go to**: Google Account Settings → Security → App Passwords
3. **Generate** an app password for "Mail"
4. **Use this password** (not your regular Gmail password) in .env

---

## ⚠️ **SAFETY CHECKLIST BEFORE LIVE TRADING**

### **Pre-Flight Checklist:**
- [ ] **✅ 2FA working** (test with Python script above)
- [ ] **✅ Small position sizes** configured (start with $100-500)
- [ ] **✅ Risk limits set** (max 2-3% per trade)
- [ ] **✅ Stop losses enabled**
- [ ] **✅ Emergency stop tested** in paper mode
- [ ] **✅ Notifications working** (test email/Telegram)
- [ ] **✅ Market hours understood** (9:30 AM - 4:00 PM ET)
- [ ] **✅ Paper trading validated** (profitable strategies)

### **Risk Management Settings:**
```bash
# Recommended conservative settings for live trading:
INITIAL_CAPITAL=1000          # Start small!
RISK_PER_TRADE=0.02          # 2% risk per trade
MAX_POSITIONS=2              # Max 2 positions
MAX_DAILY_TRADES=3           # Max 3 trades per day
STOP_LOSS=0.05              # 5% stop loss
PROFIT_TARGET=0.10          # 10% profit target
```

---

## 🚀 **DEPLOYMENT COMMANDS**

### **Test Configuration (Recommended):**
```bash
# Test your credentials without trading
python -c "
import sys
sys.path.append('src')
from trading.robinhood_client import RobinhoodClient

client = RobinhoodClient()
client.paper_trading = False  # Test real auth
result = client.authenticate()
print(f'Authentication: {\"SUCCESS\" if result else \"FAILED\"}')
"
```

### **Deploy Live Trading:**
```bash
# Stop paper trading first
python deploy_local.py stop

# Deploy in live mode
python deploy_local.py deploy live
```

### **Monitor Live Trading:**
```bash
# Check status
python deploy_local.py status

# View logs
python deploy_local.py logs trading

# Emergency stop (if needed)
python deploy_local.py stop
```

---

## 🔍 **MONITORING YOUR LIVE TRADING**

### **Dashboard Monitoring:**
- **📊 Portfolio tab**: Real-time P&L and positions
- **🎯 Signals tab**: Active trading signals
- **⚖️ Risk tab**: Risk metrics and limits
- **📋 Trades tab**: Complete trade history
- **🚨 Emergency button**: Instant stop all trading

### **API Monitoring:**
- **Health**: http://localhost:8080/health
- **Status**: http://localhost:8080/trading/status
- **Positions**: http://localhost:8080/trading/positions
- **Orders**: http://localhost:8080/trading/orders

### **Log Files:**
```bash
# Real-time log monitoring
tail -f logs/trading.log     # Trading activity
tail -f logs/api.log         # API requests
tail -f logs/monitoring.log  # System metrics
```

---

## 🚨 **EMERGENCY PROCEDURES**

### **If Something Goes Wrong:**
1. **🛑 Emergency Stop**: Click red button in dashboard
2. **📱 Stop via Command**: `python deploy_local.py stop`
3. **💰 Close Positions**: Use dashboard or Robinhood app
4. **📞 Contact Robinhood**: If needed for account issues

### **Common Issues:**
- **Authentication fails**: Check username/password/2FA
- **Orders rejected**: Check buying power and market hours
- **High losses**: Review strategy and risk settings
- **System errors**: Check logs and restart services

---

## 📋 **NEXT STEPS SUMMARY**

### **Right Now (Paper Trading Active):**
1. **🧪 Test paper trading** thoroughly (15-30 minutes)
2. **📊 Validate strategies** are working as expected
3. **🎯 Check signal quality** and execution
4. **⚖️ Verify risk controls** are functioning

### **When Ready for Live Trading:**
1. **🔐 Configure credentials** (follow steps above)
2. **⚠️ Start with small amounts** ($100-500)
3. **📊 Monitor closely** for first few trades
4. **📈 Gradually increase** position sizes if successful

### **Ongoing Operations:**
1. **📅 Daily monitoring** of performance
2. **📊 Weekly strategy review** and optimization
3. **🔄 Monthly risk assessment** and adjustments
4. **📚 Continuous learning** and improvement

---

## 🎯 **YOU'RE ALMOST READY!**

Your paper trading is running successfully. Take some time to:
- **Explore the dashboard**
- **Watch signals generate**
- **Test paper trades**
- **Verify all features work**

When you're comfortable with the paper trading performance, we'll configure live trading credentials and deploy with real money!

**Let me know when you're ready to proceed with live trading setup!** 🚀
