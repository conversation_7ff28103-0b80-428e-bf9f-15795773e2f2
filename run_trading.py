#!/usr/bin/env python3
"""
AWOT Trading Platform - Production Trading Runner
Standalone trading engine runner for production deployment
"""

import sys
import os
import signal
import time
import logging
from datetime import datetime
from typing import Optional

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from trading.live_trading_engine import LiveTradingEngine, TradingMode, TradingState
from notifications.notification_manager import NotificationManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingRunner:
    """Production trading runner with proper lifecycle management"""
    
    def __init__(self):
        self.trading_engine: Optional[LiveTradingEngine] = None
        self.notification_manager = NotificationManager()
        self.is_running = False
        self.shutdown_requested = False
        
        # Register signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Trading Runner initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_requested = True
        
        if self.trading_engine:
            self.trading_engine.stop()
    
    def start(self, trading_mode: str = "paper"):
        """Start the trading engine"""
        try:
            logger.info("🚀 Starting AWOT Trading Engine...")
            
            # Determine trading mode
            mode = TradingMode.PAPER if trading_mode.lower() == "paper" else TradingMode.LIVE
            
            # Initialize trading engine
            self.trading_engine = LiveTradingEngine(mode)
            
            # Start the engine
            if self.trading_engine.start():
                self.is_running = True
                logger.info(f"✅ Trading engine started in {mode.value} mode")
                
                # Send startup notification
                self.notification_manager.send_system_status(
                    "STARTED",
                    f"Production trading engine started in {mode.value} mode"
                )
                
                # Main monitoring loop
                self._monitoring_loop()
                
            else:
                logger.error("❌ Failed to start trading engine")
                return False
                
        except Exception as e:
            logger.error(f"Error starting trading runner: {e}")
            return False
        
        return True
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("📊 Starting monitoring loop...")
        
        last_status_report = time.time()
        status_report_interval = 300  # 5 minutes
        
        while self.is_running and not self.shutdown_requested:
            try:
                # Check if engine is still running
                if self.trading_engine:
                    status = self.trading_engine.get_status()
                    
                    # Log periodic status
                    current_time = time.time()
                    if current_time - last_status_report >= status_report_interval:
                        logger.info(f"📈 Status: {status['state']} | "
                                   f"Positions: {status['current_positions']} | "
                                   f"Daily Trades: {status['daily_trades']} | "
                                   f"Uptime: {status['stats']['uptime']:.0f}s")
                        last_status_report = current_time
                    
                    # Check for error state
                    if status['state'] == 'error':
                        logger.error("🚨 Trading engine in error state, attempting restart...")
                        self._restart_engine()
                
                # Sleep before next check
                time.sleep(10)
                
            except KeyboardInterrupt:
                logger.info("Keyboard interrupt received, shutting down...")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(30)  # Wait longer on error
        
        logger.info("📊 Monitoring loop ended")
    
    def _restart_engine(self):
        """Restart the trading engine"""
        try:
            logger.info("🔄 Restarting trading engine...")
            
            if self.trading_engine:
                old_mode = self.trading_engine.trading_mode
                self.trading_engine.stop()
                
                # Wait a moment
                time.sleep(5)
                
                # Create new engine
                self.trading_engine = LiveTradingEngine(old_mode)
                
                if self.trading_engine.start():
                    logger.info("✅ Trading engine restarted successfully")
                    
                    self.notification_manager.send_system_status(
                        "RESTARTED",
                        "Trading engine automatically restarted after error"
                    )
                else:
                    logger.error("❌ Failed to restart trading engine")
                    self.is_running = False
        
        except Exception as e:
            logger.error(f"Error restarting engine: {e}")
            self.is_running = False
    
    def stop(self):
        """Stop the trading runner"""
        logger.info("🛑 Stopping trading runner...")
        
        self.is_running = False
        
        if self.trading_engine:
            self.trading_engine.stop()
            
            # Send shutdown notification
            self.notification_manager.send_system_status(
                "STOPPED",
                "Production trading engine stopped"
            )
        
        logger.info("✅ Trading runner stopped")


def main():
    """Main entry point"""
    print("🚀 AWOT Trading Platform - Production Runner")
    print("=" * 60)
    
    # Get trading mode from environment or command line
    trading_mode = os.getenv('TRADING_MODE', 'paper')
    
    if len(sys.argv) > 1:
        trading_mode = sys.argv[1]
    
    print(f"Trading Mode: {trading_mode.upper()}")
    
    # Validate trading mode
    if trading_mode.lower() not in ['paper', 'live']:
        print("❌ Invalid trading mode. Use 'paper' or 'live'")
        return 1
    
    # Warning for live trading
    if trading_mode.lower() == 'live':
        print("⚠️  WARNING: LIVE TRADING MODE ENABLED")
        print("   This will use real money and execute real trades!")
        print("   Make sure you have:")
        print("   - Tested thoroughly in paper mode")
        print("   - Configured proper risk limits")
        print("   - Set up monitoring and alerts")
        print("   - Have emergency stop procedures ready")
        print()
        
        # Require confirmation for live trading
        confirmation = input("Type 'CONFIRM' to proceed with live trading: ")
        if confirmation != 'CONFIRM':
            print("❌ Live trading cancelled")
            return 1
    
    print("=" * 60)
    
    # Create and start trading runner
    runner = TradingRunner()
    
    try:
        success = runner.start(trading_mode)
        
        if not success:
            print("❌ Failed to start trading runner")
            return 1
        
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1
    finally:
        runner.stop()
    
    print("✅ Trading runner shutdown complete")
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
