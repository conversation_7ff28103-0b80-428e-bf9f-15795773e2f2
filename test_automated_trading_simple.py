#!/usr/bin/env python3
"""
Simple Test of Automated Trading System
Creates mock signals and tests the trading pipeline
"""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide
from signals.signal_generator import TradingSignal, SignalType, SignalStrength

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class MockSignalGenerator:
    """Mock signal generator for testing"""
    
    def generate_test_signals(self):
        """Generate test signals for current holdings and new opportunities"""
        signals = []
        
        # Create test signals for your current holdings
        test_signals = [
            {
                'symbol': 'AAPL',
                'signal_type': SignalType.MOMENTUM_BULLISH,
                'strength': SignalStrength.STRONG,
                'confidence': 0.85,
                'entry_price': 213.25,
                'target_price': 220.00,
                'stop_loss': 210.00,
                'reasoning': 'Strong momentum with positive sentiment'
            },
            {
                'symbol': 'TSLA',
                'signal_type': SignalType.MOMENTUM_BEARISH,
                'strength': SignalStrength.MODERATE,
                'confidence': 0.72,
                'entry_price': 328.50,
                'target_price': 320.00,
                'stop_loss': 335.00,
                'reasoning': 'Overbought conditions detected'
            },
            {
                'symbol': 'MSFT',
                'signal_type': SignalType.BREAKOUT_BULLISH,
                'strength': SignalStrength.STRONG,
                'confidence': 0.78,
                'entry_price': 412.00,
                'target_price': 420.00,
                'stop_loss': 408.00,
                'reasoning': 'Breakout above resistance'
            },
            {
                'symbol': 'NVDA',
                'signal_type': SignalType.MOMENTUM_BULLISH,
                'strength': SignalStrength.VERY_STRONG,
                'confidence': 0.92,
                'entry_price': 485.75,
                'target_price': 500.00,
                'stop_loss': 480.00,
                'reasoning': 'AI momentum with high volume'
            }
        ]
        
        for signal_data in test_signals:
            signal = TradingSignal(
                symbol=signal_data['symbol'],
                signal_type=signal_data['signal_type'],
                strength=signal_data['strength'],
                confidence=signal_data['confidence'],
                entry_price=signal_data['entry_price'],
                target_price=signal_data['target_price'],
                stop_loss_price=signal_data['stop_loss'],
                expiration_date=(datetime.now().strftime('%Y-%m-%d')),
                reasoning=signal_data['reasoning']
            )
            signals.append(signal)
        
        return signals

class SimpleAutomatedTrader:
    """Simple automated trader for testing"""
    
    def __init__(self):
        self.paper_engine = PaperTradingEngine()
        self.signal_generator = MockSignalGenerator()
        self.confidence_threshold = 0.70
        self.max_position_size = 0.10  # 10% of portfolio
        
        # Load existing portfolio state
        if os.path.exists('current_portfolio_state.json'):
            try:
                self.paper_engine.load_state('current_portfolio_state.json')
                portfolio = self.paper_engine.get_portfolio_summary()
                logging.info(f"✅ Loaded existing portfolio: ${portfolio['portfolio_value']:,.2f} value, {portfolio['open_positions']} positions")
            except Exception as e:
                logging.error(f"Error loading portfolio state: {e}")
                logging.info("Starting with fresh portfolio")
        else:
            logging.info("No existing portfolio found, starting fresh")
    
    def process_signals(self):
        """Process signals and execute trades"""
        signals = self.signal_generator.generate_test_signals()
        
        logging.info(f"📊 Processing {len(signals)} test signals...")
        
        trades_executed = 0
        
        for signal in signals:
            logging.info(f"\n🎯 Signal: {signal.symbol}")
            logging.info(f"   Type: {signal.signal_type}")
            logging.info(f"   Confidence: {signal.confidence:.2%}")
            logging.info(f"   Reasoning: {signal.reasoning}")
            
            if signal.confidence >= self.confidence_threshold:
                if self.execute_signal(signal):
                    trades_executed += 1
            else:
                logging.info(f"   ❌ Below threshold ({signal.confidence:.2%} < {self.confidence_threshold:.0%})")
        
        return trades_executed
    
    def execute_signal(self, signal):
        """Execute a trading signal"""
        try:
            # Determine action based on signal type
            signal_type_str = str(signal.signal_type.value).lower()

            if 'bullish' in signal_type_str or 'breakout_bullish' in signal_type_str:
                side = OrderSide.BUY
                action = 'BUY'
            elif 'bearish' in signal_type_str:
                side = OrderSide.SELL
                action = 'SELL'
            else:
                logging.info(f"   📊 Neutral signal, no action")
                return False
            
            # Get current portfolio
            portfolio = self.paper_engine.get_portfolio_summary()
            
            # For SELL orders, check if we have the position
            if action == 'SELL':
                positions = self.paper_engine.get_positions()
                current_position = None
                for pos in positions:
                    if pos['symbol'] == signal.symbol and pos['is_open']:
                        current_position = pos
                        break
                
                if not current_position:
                    logging.info(f"   ⚠️ No position in {signal.symbol} to sell")
                    return False
                
                # Sell partial position (50% or 10 shares, whichever is smaller)
                quantity = min(10, max(1, current_position['quantity'] // 2))
                price = signal.entry_price
            else:
                # For BUY orders, use small fixed quantities for testing
                price = signal.entry_price

                # Use small test quantities to avoid cash issues
                test_quantities = {
                    'AAPL': 10,
                    'TSLA': 5,
                    'MSFT': 5,
                    'NVDA': 3,
                    'GOOGL': 1,
                    'AMZN': 1,
                    'META': 5
                }

                quantity = test_quantities.get(signal.symbol, 5)

                # Check if we have enough cash
                required_cash = quantity * price
                if required_cash > portfolio['cash']:
                    quantity = max(1, int(portfolio['cash'] / price))
                    if quantity < 1:
                        logging.info(f"   ❌ Insufficient cash for {signal.symbol}")
                        return False
            
            logging.info(f"   🎯 Attempting {action} {quantity} {signal.symbol} @ ${price:.2f}")
            
            # Execute trade
            result = self.paper_engine.place_order(
                symbol=signal.symbol,
                quantity=quantity,
                side=side,
                price=price,
                order_type='market'
            )
            
            if result.get('success'):
                logging.info(f"   ✅ Executed {action} {quantity} {signal.symbol} @ ${price:.2f}")
                
                # Save state
                self.paper_engine.save_state('current_portfolio_state.json')
                return True
            else:
                logging.error(f"   ❌ Failed to execute {action} {signal.symbol}: {result.get('error')}")
                return False
                
        except Exception as e:
            logging.error(f"   ❌ Error executing signal for {signal.symbol}: {e}")
            return False
    
    def run_test(self):
        """Run a single test cycle"""
        logging.info("🚀 Starting Simple Automated Trading Test")
        
        # Show current portfolio
        portfolio = self.paper_engine.get_portfolio_summary()
        logging.info(f"📊 Current Portfolio:")
        logging.info(f"   Value: ${portfolio['portfolio_value']:,.2f}")
        logging.info(f"   Cash: ${portfolio['cash']:,.2f}")
        logging.info(f"   Positions: {portfolio['open_positions']}")
        
        # Process signals
        trades = self.process_signals()
        
        # Show results
        logging.info(f"\n📈 Test Results:")
        logging.info(f"   Trades executed: {trades}")
        
        # Show updated portfolio
        portfolio = self.paper_engine.get_portfolio_summary()
        logging.info(f"   New portfolio value: ${portfolio['portfolio_value']:,.2f}")
        logging.info(f"   New cash: ${portfolio['cash']:,.2f}")
        logging.info(f"   New positions: {portfolio['open_positions']}")
        
        return trades

def main():
    """Main test function"""
    print("🧪 Simple Automated Trading Test")
    print("=" * 50)
    
    trader = SimpleAutomatedTrader()
    trades_executed = trader.run_test()
    
    print("\n" + "=" * 50)
    print("🎯 TEST SUMMARY:")
    
    if trades_executed > 0:
        print(f"✅ SUCCESS: {trades_executed} trades executed")
        print("🎯 Automated trading pipeline is working!")
        print("📊 Check your dashboard to see the new trades")
    else:
        print("⚠️ No trades executed")
        print("🔍 Check signal confidence thresholds and portfolio state")
    
    print("\n💡 Next Steps:")
    print("1. Check dashboard at http://localhost:8502")
    print("2. Review Trade Log tab for new trades")
    print("3. Monitor Portfolio tab for updated values")
    print("4. Run during market hours for live testing")

if __name__ == "__main__":
    main()
