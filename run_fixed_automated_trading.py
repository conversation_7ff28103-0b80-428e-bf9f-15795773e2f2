#!/usr/bin/env python3
'''
Fixed Automated Trading Script
This integrates signal generation with paper trading execution
'''

import sys
import os
import time
import logging
from datetime import datetime, time as dt_time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from signals.enhanced_signal_generator import EnhancedSignalGenerator
from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automated_trading_fixed.log'),
        logging.StreamHandler()
    ]
)

class FixedAutomatedTrader:
    def __init__(self):
        self.signal_generator = EnhancedSignalGenerator()
        self.paper_engine = PaperTradingEngine()
        self.confidence_threshold = 0.70
        self.max_position_size = 0.10  # 10% of portfolio
        self.symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL', 'AMZN', 'META']
        
        # Load existing portfolio state
        if os.path.exists('current_portfolio_state.json'):
            self.paper_engine.load_state('current_portfolio_state.json')
            logging.info("Loaded existing portfolio state")
    
    def is_market_hours(self):
        '''Check if market is open'''
        now = datetime.now()
        current_time = now.time()
        market_open = dt_time(9, 30)
        market_close = dt_time(16, 0)
        is_weekday = now.weekday() < 5
        
        return market_open <= current_time <= market_close and is_weekday
    
    def process_signals(self):
        '''Generate and process signals for trading'''
        try:
            # Generate signals
            signals = self.signal_generator.generate_signals(self.symbols)
            
            if not signals:
                logging.info("No signals generated")
                return 0
            
            logging.info(f"Generated {len(signals)} signals")
            trades_executed = 0
            
            for signal in signals:
                if signal.confidence >= self.confidence_threshold:
                    if self.execute_signal(signal):
                        trades_executed += 1
            
            return trades_executed
            
        except Exception as e:
            logging.error(f"Error processing signals: {e}")
            return 0
    
    def execute_signal(self, signal):
        '''Execute a trading signal'''
        try:
            # Determine action
            if 'bullish' in signal.signal_type.lower():
                side = OrderSide.BUY
                action = 'BUY'
            elif 'bearish' in signal.signal_type.lower():
                side = OrderSide.SELL
                action = 'SELL'
            else:
                logging.info(f"Neutral signal for {signal.symbol}, no action")
                return False
            
            # Calculate position size
            portfolio = self.paper_engine.get_portfolio_summary()
            max_value = portfolio['portfolio_value'] * self.max_position_size
            
            # Estimate price (in real implementation, get current price)
            estimated_price = 200.00  # Placeholder
            quantity = int(max_value / estimated_price)
            
            if quantity < 1:
                logging.info(f"Position size too small for {signal.symbol}")
                return False
            
            # Execute trade
            result = self.paper_engine.place_order(
                symbol=signal.symbol,
                quantity=quantity,
                side=side,
                price=estimated_price,
                order_type='market'
            )
            
            if result.get('success'):
                logging.info(f"✅ Executed {action} {quantity} {signal.symbol} @ ${estimated_price}")
                
                # Save state
                self.paper_engine.save_state('current_portfolio_state.json')
                return True
            else:
                logging.error(f"❌ Failed to execute {action} {signal.symbol}: {result.get('error')}")
                return False
                
        except Exception as e:
            logging.error(f"Error executing signal for {signal.symbol}: {e}")
            return False
    
    def run(self):
        '''Main trading loop'''
        logging.info("🚀 Starting Fixed Automated Trading System")
        
        while True:
            try:
                if self.is_market_hours():
                    logging.info("📊 Market is open, processing signals...")
                    trades = self.process_signals()
                    
                    if trades > 0:
                        logging.info(f"✅ Executed {trades} trades")
                    else:
                        logging.info("📊 No trades executed this cycle")
                else:
                    logging.info("🕐 Market closed, waiting...")
                
                # Wait 5 minutes before next cycle
                time.sleep(300)
                
            except KeyboardInterrupt:
                logging.info("🛑 Stopping automated trading")
                break
            except Exception as e:
                logging.error(f"Error in main loop: {e}")
                time.sleep(60)  # Wait 1 minute on error

if __name__ == "__main__":
    trader = FixedAutomatedTrader()
    trader.run()
