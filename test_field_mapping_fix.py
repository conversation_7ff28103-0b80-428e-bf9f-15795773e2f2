#!/usr/bin/env python3
'''
Test Field Mapping Fix
This tests if the dashboard can handle the 'side' vs 'action' field mapping correctly
'''

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_field_mapping():
    """Test if the field mapping fix works"""
    print("🔍 TESTING FIELD MAPPING FIX")
    print("=" * 60)
    
    # Test 1: Check trade data format from paper engine
    print("🔍 Test 1: Paper Trading Engine Trade Format")
    from trading.paper_trading import PaperTradingEngine
    
    engine = PaperTradingEngine()
    trade_history = engine.get_trade_history()
    
    if trade_history:
        sample_trade = trade_history[0]
        print(f"   📊 Sample trade keys: {list(sample_trade.keys())}")
        print(f"   📈 Has 'side': {'side' in sample_trade}")
        print(f"   📈 Has 'action': {'action' in sample_trade}")
        print(f"   📈 Side value: {sample_trade.get('side', 'Not found')}")
        print(f"   📈 Action value: {sample_trade.get('action', 'Not found')}")
    else:
        print("   ❌ No trades found")
        return False
    
    # Test 2: Test the field mapping logic
    print("\n🔍 Test 2: Field Mapping Logic")
    
    # Simulate the dashboard logic
    test_trade = sample_trade
    
    # Test the get logic that should work
    action_value = test_trade.get('action', test_trade.get('side', 'Unknown')).upper()
    print(f"   📊 Mapped action value: {action_value}")
    
    if action_value in ['BUY', 'SELL']:
        print("   ✅ Field mapping working correctly!")
    else:
        print(f"   ❌ Field mapping failed: {action_value}")
        return False
    
    # Test 3: Test with multiple trades
    print("\n🔍 Test 3: Multiple Trades Test")
    
    success_count = 0
    for i, trade in enumerate(trade_history[:5]):  # Test first 5 trades
        try:
            action = trade.get('action', trade.get('side', 'Unknown')).upper()
            if action in ['BUY', 'SELL']:
                success_count += 1
            print(f"   Trade {i+1}: {action} {trade['quantity']} {trade['symbol']} ✅")
        except Exception as e:
            print(f"   Trade {i+1}: ERROR - {e} ❌")
    
    print(f"\n   📊 Success rate: {success_count}/{min(5, len(trade_history))} trades")
    
    if success_count == min(5, len(trade_history)):
        print("   ✅ All trades processed successfully!")
        return True
    else:
        print("   ❌ Some trades failed processing")
        return False

if __name__ == "__main__":
    success = test_field_mapping()
    if success:
        print("\n🎉 SUCCESS: Field mapping fix is working!")
        print("🚀 Your dashboard should now display trades without errors!")
        print("🌐 Go to: http://localhost:8502")
        print("📋 Click: Trade Log tab")
    else:
        print("\n❌ FAILED: Field mapping fix needs more work")
