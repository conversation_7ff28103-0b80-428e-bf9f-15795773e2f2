#!/usr/bin/env python3
'''
Test Trade Log Fix
This tests if the dashboard can now read real trades from the paper trading engine
'''

import sys
import os
import json
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from dashboard.data_manager import DashboardDataManager

def test_trade_log_fix():
    """Test if the trade log can read real trades"""
    print("🔍 TESTING TRADE LOG FIX")
    print("=" * 60)
    
    # Check if portfolio state file exists
    portfolio_file = 'current_portfolio_state.json'
    
    if os.path.exists(portfolio_file):
        print(f"✅ Portfolio state file found: {portfolio_file}")
        
        # Load and check the file
        with open(portfolio_file, 'r') as f:
            portfolio_state = json.load(f)
        
        trade_history = portfolio_state.get('trade_history', [])
        print(f"📊 Found {len(trade_history)} trades in portfolio state")
        
        if trade_history:
            print("\n📈 Sample trades:")
            for i, trade in enumerate(trade_history[-5:]):  # Show last 5 trades
                timestamp = trade.get('timestamp', 'Unknown')
                symbol = trade.get('symbol', 'Unknown')
                side = trade.get('side', 'Unknown')
                quantity = trade.get('quantity', 0)
                price = trade.get('price', 0)
                
                print(f"   {i+1}. {timestamp}: {side} {quantity} {symbol} @ ${price:.2f}")
        
        # Test the data manager
        print("\n🔍 Testing DashboardDataManager trade reading...")
        data_manager = DashboardDataManager()
        
        # Get portfolio data (which includes trades)
        portfolio_data = data_manager.get_portfolio_data()
        recent_trades = portfolio_data.get('recent_trades', [])
        
        print(f"📊 DashboardDataManager found {len(recent_trades)} recent trades")
        
        if recent_trades:
            print("\n📈 Recent trades from DataManager:")
            for i, trade in enumerate(recent_trades[:5]):  # Show first 5
                timestamp = trade.get('timestamp', 'Unknown')
                symbol = trade.get('symbol', 'Unknown')
                action = trade.get('action', 'Unknown')
                quantity = trade.get('quantity', 0)
                price = trade.get('price', 0)
                status = trade.get('status', 'Unknown')
                
                print(f"   {i+1}. {timestamp}: {action} {quantity} {symbol} @ ${price:.2f} ({status})")
            
            print("\n✅ SUCCESS: Trade log fix is working!")
            print("🎯 Your dashboard should now show real trades in the Trade Log tab")
            
        else:
            print("\n❌ DataManager is not reading trades correctly")
            print("🔍 Check the _get_real_trades method in data_manager.py")
        
    else:
        print(f"❌ Portfolio state file not found: {portfolio_file}")
        print("🔍 Make sure you've executed some trades first")
    
    print("\n💡 NEXT STEPS:")
    print("1. Refresh your dashboard at http://localhost:8502")
    print("2. Click on the 'Trade Log' tab")
    print("3. You should now see your real automated trades!")

if __name__ == "__main__":
    test_trade_log_fix()
