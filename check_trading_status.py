#!/usr/bin/env python3
"""
Check Trading Status - Why No Automated Trades During Market Hours
"""

import sys
import os
import subprocess
import glob
from datetime import datetime, time
import json

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def check_market_hours():
    """Check if we're currently in market hours"""
    now = datetime.now()
    current_time = now.time()
    
    # Market hours: 9:30 AM - 4:00 PM ET (simplified)
    market_open = time(9, 30)
    market_close = time(16, 0)
    
    is_market_hours = market_open <= current_time <= market_close
    is_weekday = now.weekday() < 5  # Monday = 0, Friday = 4
    
    return is_market_hours and is_weekday

def check_running_processes():
    """Check what trading-related processes are running"""
    print("🔍 Checking Running Processes...")
    
    processes = {
        'streamlit': False,
        'uvicorn': False,
        'trading': False,
        'python': []
    }
    
    try:
        # Check for Streamlit (dashboard)
        result = subprocess.run(['pgrep', '-f', 'streamlit'], capture_output=True, text=True)
        if result.stdout.strip():
            processes['streamlit'] = True
            print("✅ Streamlit dashboard is running")
        else:
            print("❌ Streamlit dashboard not running")
        
        # Check for API server (uvicorn)
        result = subprocess.run(['pgrep', '-f', 'uvicorn'], capture_output=True, text=True)
        if result.stdout.strip():
            processes['uvicorn'] = True
            print("✅ API server (uvicorn) is running")
        else:
            print("❌ API server not running")
        
        # Check for trading engine
        result = subprocess.run(['pgrep', '-f', 'run_trading'], capture_output=True, text=True)
        if result.stdout.strip():
            processes['trading'] = True
            print("✅ Trading engine is running")
        else:
            print("❌ Trading engine NOT running")
        
        # Check all Python processes
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        python_lines = [line for line in result.stdout.split('\n') if 'python' in line and 'AWOT' in line]
        
        if python_lines:
            print("\n📊 AWOT-related Python processes:")
            for line in python_lines:
                print(f"  {line}")
        
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
    
    return processes

def check_log_files():
    """Check log files for trading activity"""
    print("\n📋 Checking Log Files...")
    
    log_files = [
        'logs/trading.log',
        'logs/api.log',
        'logs/dashboard.log'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"✅ Found log file: {log_file}")
            
            # Check last few lines
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"   Last entry: {lines[-1].strip()}")
                    else:
                        print("   (Empty log file)")
            except Exception as e:
                print(f"   Error reading log: {e}")
        else:
            print(f"❌ Log file not found: {log_file}")

def check_deployment_status():
    """Check if the system was deployed properly"""
    print("\n🚀 Checking Deployment Status...")
    
    # Check if deployment script exists
    if os.path.exists('deploy_local.py'):
        print("✅ Deployment script found")
        
        # Check if system was deployed today
        try:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if 'deploy_local.py' in result.stdout:
                print("✅ Deployment script may have been run")
            else:
                print("❌ No evidence of deployment script running")
        except:
            pass
    else:
        print("❌ Deployment script not found")

def check_trading_configuration():
    """Check trading configuration"""
    print("\n⚙️ Checking Trading Configuration...")
    
    # Check .env file
    if os.path.exists('.env'):
        print("✅ .env file found")
        try:
            with open('.env', 'r') as f:
                env_content = f.read()
                if 'TRADING_MODE' in env_content:
                    print("✅ TRADING_MODE configured")
                else:
                    print("⚠️ TRADING_MODE not found in .env")
        except Exception as e:
            print(f"❌ Error reading .env: {e}")
    else:
        print("❌ .env file not found")
    
    # Check if trading engine can be imported
    try:
        from trading.live_trading_engine import LiveTradingEngine
        print("✅ Trading engine can be imported")
    except ImportError as e:
        print(f"❌ Cannot import trading engine: {e}")

def analyze_why_no_trades():
    """Analyze why no automated trades were made"""
    print("\n🔍 ANALYSIS: Why No Automated Trades?")
    print("=" * 60)
    
    reasons = []
    
    # Check if it's market hours
    if not check_market_hours():
        reasons.append("🕐 Not currently in market hours (9:30 AM - 4:00 PM ET, weekdays)")
    
    # Check processes
    processes = check_running_processes()
    
    if not processes['trading']:
        reasons.append("🤖 Trading engine is NOT running")
        reasons.append("   → Need to start with: python deploy_local.py deploy paper")
    
    if not processes['uvicorn']:
        reasons.append("🔌 API server is NOT running")
        reasons.append("   → Dashboard can't communicate with trading system")
    
    # Check if manual trades only
    try:
        from trading.paper_trading import PaperTradingEngine
        paper_engine = PaperTradingEngine()
        
        # Try to load current state
        if os.path.exists('current_portfolio_state.json'):
            paper_engine.load_state('current_portfolio_state.json')
        
        portfolio = paper_engine.get_portfolio_summary()
        trades = paper_engine.get_trade_history()
        
        if len(trades) > 0:
            print(f"\n📊 Found {len(trades)} trades in paper trading engine")
            print("   These appear to be MANUAL trades (from dashboard)")
            reasons.append("📱 Only manual trades found - no automated trading")
        else:
            reasons.append("📭 No trades found at all")
            
    except Exception as e:
        reasons.append(f"❌ Error checking paper trading: {e}")
    
    # Display reasons
    if reasons:
        print("\n🎯 LIKELY REASONS FOR NO AUTOMATED TRADES:")
        for i, reason in enumerate(reasons, 1):
            print(f"  {i}. {reason}")
    else:
        print("\n✅ No obvious issues found")
    
    return reasons

def provide_solutions():
    """Provide solutions to start automated trading"""
    print("\n💡 SOLUTIONS TO START AUTOMATED TRADING:")
    print("=" * 60)
    
    print("\n🚀 Option 1: Start Full Automated System")
    print("```bash")
    print("cd /home/<USER>/Documents/AWOT")
    print("source venv/bin/activate")
    print("python deploy_local.py deploy paper")
    print("```")
    print("This will start:")
    print("  ✅ Dashboard (port 8502)")
    print("  ✅ API server (port 8080)")
    print("  ✅ Automated trading engine")
    print("  ✅ Monitoring system")
    
    print("\n📱 Option 2: Manual Trading Only (Current Setup)")
    print("```bash")
    print("# Keep current setup for manual testing")
    print("# Use Live Trading tab in dashboard to place trades")
    print("# Good for testing and learning")
    print("```")
    
    print("\n⚠️ Option 3: Check Market Hours")
    market_status = "OPEN" if check_market_hours() else "CLOSED"
    print(f"Current market status: {market_status}")
    if market_status == "CLOSED":
        print("  → Market is closed, automated trading won't execute")
        print("  → Test with manual trades or wait for market open")
    
    print("\n🎯 RECOMMENDED NEXT STEPS:")
    print("1. If you want automated trading during market hours:")
    print("   → Run: python deploy_local.py deploy paper")
    print("2. If you want to test manually first:")
    print("   → Use Live Trading tab in your dashboard")
    print("3. Monitor results in all dashboard tabs")
    print("4. Check logs for any errors or issues")

def main():
    """Main analysis function"""
    print("🔍 AWOT Trading Status Analysis")
    print("=" * 60)
    print(f"Analysis time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check current market status
    market_open = check_market_hours()
    print(f"\n📊 Market Status: {'OPEN' if market_open else 'CLOSED'}")
    
    # Run analysis
    check_deployment_status()
    check_trading_configuration()
    check_log_files()
    
    # Analyze why no trades
    reasons = analyze_why_no_trades()
    
    # Provide solutions
    provide_solutions()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    
    if not market_open:
        print("📊 Market is currently CLOSED - no automated trading expected")
    
    processes = check_running_processes()
    if not processes['trading']:
        print("🤖 MAIN ISSUE: Automated trading engine is NOT running")
        print("   → This is why no automated trades were made")
        print("   → Your current trades are manual trades from the dashboard")
    else:
        print("✅ Trading engine is running - check logs for trading activity")
    
    print(f"\n📱 Your current portfolio has manual trades from dashboard testing")
    print(f"💡 To enable automated trading: python deploy_local.py deploy paper")

if __name__ == "__main__":
    main()
