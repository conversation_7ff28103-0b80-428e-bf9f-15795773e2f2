#!/usr/bin/env python3
'''
Test Weekly Options Trading
This tests the complete AWOT system to verify it's now trading weekly options instead of stocks
'''

import sys
import os
import time
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_weekly_options_trading():
    """Test the complete weekly options trading system"""
    print("🎯 TESTING WEEKLY OPTIONS TRADING SYSTEM")
    print("=" * 60)
    
    try:
        from signals.signal_generator import TradingSignal, SignalType, SignalStrength
        from trading.paper_trading import PaperTradingEngine
        from trading.robinhood_client import OrderSide, OptionType
        
        # Initialize paper trading engine
        print("📊 Initializing paper trading engine...")
        paper_engine = PaperTradingEngine()
        
        # Get current portfolio state
        initial_positions = paper_engine.get_positions()
        initial_trades = paper_engine.get_trade_history()
        
        print(f"   📈 Initial positions: {len(initial_positions)}")
        print(f"   📈 Initial trades: {len(initial_trades)}")
        
        # Create multiple weekly options signals to test
        print("\n📈 Creating weekly options signals...")
        
        weekly_signals = [
            TradingSignal(
                symbol='AAPL',
                signal_type=SignalType.MOMENTUM_BULLISH,
                strength=SignalStrength.STRONG,
                confidence=0.75,
                entry_price=4.50,  # Option premium
                target_price=7.00,
                stop_loss_price=2.50,
                expiration_date=(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
                option_type='call',
                strike_price=215.00,
                reasoning="Weekly momentum breakout - AAPL call"
            ),
            TradingSignal(
                symbol='TSLA',
                signal_type=SignalType.BREAKOUT_BULLISH,
                strength=SignalStrength.VERY_STRONG,
                confidence=0.80,
                entry_price=6.25,  # Option premium
                target_price=10.00,
                stop_loss_price=3.00,
                expiration_date=(datetime.now() + timedelta(days=5)).strftime('%Y-%m-%d'),
                option_type='call',
                strike_price=330.00,
                reasoning="Weekly breakout signal - TSLA call"
            ),
            TradingSignal(
                symbol='MSFT',
                signal_type=SignalType.REVERSAL_BULLISH,
                strength=SignalStrength.STRONG,
                confidence=0.70,
                entry_price=8.75,  # Option premium
                target_price=12.50,
                stop_loss_price=5.00,
                expiration_date=(datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d'),
                option_type='call',
                strike_price=520.00,
                reasoning="Weekly reversal signal - MSFT call"
            )
        ]
        
        print(f"✅ Created {len(weekly_signals)} weekly options signals")
        
        # Execute each options signal
        print("\n🔄 Executing weekly options trades...")
        
        options_trades_executed = 0
        
        for i, signal in enumerate(weekly_signals):
            print(f"\n   📈 Signal {i+1}: {signal.symbol}")
            print(f"      Option Type: {signal.option_type}")
            print(f"      Strike: ${signal.strike_price}")
            print(f"      Expiration: {signal.expiration_date}")
            print(f"      Premium: ${signal.entry_price}")
            
            # Execute the options trade
            result = paper_engine.place_order(
                symbol=signal.symbol,
                quantity=3,  # 3 contracts
                side=OrderSide.BUY,
                price=signal.entry_price,
                order_type='option',
                strike_price=signal.strike_price,
                expiration_date=signal.expiration_date,
                option_type=OptionType.CALL,
                strategy_name="weekly_options_awot",
                signal_id=f"AWOT_WEEKLY_{i+1}"
            )
            
            if result.get('success'):
                options_trades_executed += 1
                print(f"      ✅ OPTIONS TRADE EXECUTED!")
                print(f"         Execution Price: ${result.get('execution_price', 0):.2f}")
                print(f"         Total Cost: ${result.get('total_cost', 0):.2f}")
            else:
                print(f"      ❌ Trade failed: {result.get('error', 'Unknown error')}")
        
        # Check final portfolio state
        print(f"\n📊 FINAL PORTFOLIO ANALYSIS:")
        
        final_positions = paper_engine.get_positions()
        final_trades = paper_engine.get_trade_history()
        
        print(f"   📈 Final positions: {len(final_positions)}")
        print(f"   📈 Final trades: {len(final_trades)}")
        print(f"   📈 New trades executed: {len(final_trades) - len(initial_trades)}")
        print(f"   📈 Options trades executed: {options_trades_executed}")
        
        # Analyze the new trades
        new_trades = final_trades[len(initial_trades):]
        
        if new_trades:
            print(f"\n📈 NEW TRADES ANALYSIS:")
            
            options_count = 0
            stock_count = 0
            
            for trade in new_trades:
                print(f"\n   📈 Trade: {trade['symbol']}")
                print(f"      Type: {trade['trade_type']}")
                print(f"      Side: {trade['side']}")
                print(f"      Quantity: {trade['quantity']}")
                print(f"      Price: ${trade['price']:.2f}")
                
                if trade['trade_type'] == 'option':
                    options_count += 1
                    print(f"      Strike: ${trade.get('strike_price', 'N/A')}")
                    print(f"      Expiry: {trade.get('expiration_date', 'N/A')}")
                    print(f"      Option Type: {trade.get('option_type', 'N/A')}")
                    print(f"      ✅ WEEKLY OPTIONS TRADE!")
                elif trade['trade_type'] == 'stock':
                    stock_count += 1
                    print(f"      ❌ Stock trade (not options)")
            
            print(f"\n🎯 TRADE TYPE SUMMARY:")
            print(f"   📈 Options Trades: {options_count}")
            print(f"   📊 Stock Trades: {stock_count}")
            print(f"   📊 Total New Trades: {len(new_trades)}")
            
            if options_count > 0 and stock_count == 0:
                print(f"\n🎉 SUCCESS: ALL NEW TRADES ARE WEEKLY OPTIONS!")
                print(f"✅ AWOT is now properly trading weekly options!")
            elif options_count > stock_count:
                print(f"\n✅ MOSTLY SUCCESS: More options than stocks traded")
            else:
                print(f"\n⚠️ MIXED RESULTS: Still trading some stocks")
        
        return options_trades_executed > 0
        
    except Exception as e:
        print(f"❌ Error in weekly options trading test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_weekly_options_trading()
    if success:
        print("\n🎉 SUCCESS: Weekly options trading system is working!")
        print("📈 Your AWOT system is now executing weekly options trades!")
        print("🎯 Check your dashboard to see the new options positions!")
    else:
        print("\n❌ FAILED: Weekly options trading needs more work")
