#!/usr/bin/env python3
"""
AWOT Trading Platform - Master Test Runner
Comprehensive testing suite that runs all tests and generates detailed reports
"""

import sys
import os
import time
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Tuple
import argparse

# Add tests to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'tests'))

# Import test modules
from test_suite import run_comprehensive_tests
from test_performance import run_performance_tests
from test_security import run_security_tests


class TestRunner:
    """Master test runner for all AWOT test suites"""
    
    def __init__(self):
        self.results = {}
        self.start_time = None
        self.end_time = None
        self.total_duration = 0
        
    def run_all_tests(self, test_types: List[str] = None) -> Dict:
        """Run all or specified test suites"""
        if test_types is None:
            test_types = ['functional', 'performance', 'security']
        
        print("🧪 AWOT Trading Platform - Master Test Suite")
        print("=" * 100)
        print(f"🚀 Starting comprehensive testing at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 100)
        
        self.start_time = time.time()
        
        # Run each test suite
        if 'functional' in test_types:
            self._run_functional_tests()
        
        if 'performance' in test_types:
            self._run_performance_tests()
        
        if 'security' in test_types:
            self._run_security_tests()
        
        self.end_time = time.time()
        self.total_duration = self.end_time - self.start_time
        
        # Generate comprehensive report
        self._generate_final_report()
        
        return self.results
    
    def _run_functional_tests(self):
        """Run functional/unit tests"""
        print("\n" + "🔧 FUNCTIONAL TESTS".center(100, "="))
        print("Testing core functionality, algorithms, and integrations...")
        print("-" * 100)
        
        start_time = time.time()
        
        try:
            success = run_comprehensive_tests()
            duration = time.time() - start_time
            
            self.results['functional'] = {
                'success': success,
                'duration': duration,
                'status': 'PASS' if success else 'FAIL'
            }
            
            print(f"\n✅ Functional tests completed in {duration:.2f}s - {'PASS' if success else 'FAIL'}")
            
        except Exception as e:
            duration = time.time() - start_time
            self.results['functional'] = {
                'success': False,
                'duration': duration,
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"\n❌ Functional tests failed with error: {e}")
    
    def _run_performance_tests(self):
        """Run performance tests"""
        print("\n" + "🚀 PERFORMANCE TESTS".center(100, "="))
        print("Testing performance, scalability, and resource usage...")
        print("-" * 100)
        
        start_time = time.time()
        
        try:
            success = run_performance_tests()
            duration = time.time() - start_time
            
            self.results['performance'] = {
                'success': success,
                'duration': duration,
                'status': 'PASS' if success else 'FAIL'
            }
            
            print(f"\n✅ Performance tests completed in {duration:.2f}s - {'PASS' if success else 'FAIL'}")
            
        except Exception as e:
            duration = time.time() - start_time
            self.results['performance'] = {
                'success': False,
                'duration': duration,
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"\n❌ Performance tests failed with error: {e}")
    
    def _run_security_tests(self):
        """Run security tests"""
        print("\n" + "🔒 SECURITY TESTS".center(100, "="))
        print("Testing security vulnerabilities and protection measures...")
        print("-" * 100)
        
        start_time = time.time()
        
        try:
            success = run_security_tests()
            duration = time.time() - start_time
            
            self.results['security'] = {
                'success': success,
                'duration': duration,
                'status': 'PASS' if success else 'FAIL'
            }
            
            print(f"\n✅ Security tests completed in {duration:.2f}s - {'PASS' if success else 'FAIL'}")
            
        except Exception as e:
            duration = time.time() - start_time
            self.results['security'] = {
                'success': False,
                'duration': duration,
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"\n❌ Security tests failed with error: {e}")
    
    def _generate_final_report(self):
        """Generate comprehensive final report"""
        print("\n" + "📊 COMPREHENSIVE TEST REPORT".center(100, "="))
        
        # Overall summary
        total_suites = len(self.results)
        passed_suites = sum(1 for r in self.results.values() if r['success'])
        failed_suites = total_suites - passed_suites
        
        overall_success = all(r['success'] for r in self.results.values())
        
        print(f"🕐 Total Execution Time: {self.total_duration:.2f} seconds")
        print(f"📋 Test Suites Run: {total_suites}")
        print(f"✅ Passed: {passed_suites}")
        print(f"❌ Failed: {failed_suites}")
        print(f"📈 Success Rate: {(passed_suites/total_suites*100):.1f}%")
        
        # Detailed results
        print(f"\n📋 Detailed Results:")
        print("-" * 100)
        
        for suite_name, result in self.results.items():
            status_emoji = "✅" if result['success'] else "❌"
            status_text = result['status']
            duration = result['duration']
            
            print(f"{status_emoji} {suite_name.upper():15} | {status_text:6} | {duration:8.2f}s")
            
            if 'error' in result:
                print(f"   Error: {result['error']}")
        
        # Component assessment
        print(f"\n🎯 Component Assessment:")
        print("-" * 100)
        
        components = {
            'Technical Indicators': self.results.get('functional', {}).get('success', False),
            'Signal Generation': self.results.get('functional', {}).get('success', False),
            'Risk Management': self.results.get('functional', {}).get('success', False),
            'Paper Trading': self.results.get('functional', {}).get('success', False),
            'Live Trading Engine': self.results.get('functional', {}).get('success', False),
            'Order Management': self.results.get('functional', {}).get('success', False),
            'Notifications': self.results.get('functional', {}).get('success', False),
            'Performance': self.results.get('performance', {}).get('success', False),
            'Security': self.results.get('security', {}).get('success', False)
        }
        
        for component, status in components.items():
            status_emoji = "🟢" if status else "🔴"
            status_text = "READY" if status else "NEEDS WORK"
            print(f"{status_emoji} {component:20} | {status_text}")
        
        # Production readiness assessment
        print(f"\n🚀 Production Readiness Assessment:")
        print("-" * 100)
        
        if overall_success:
            print("🟢 PRODUCTION READY")
            print("✅ All test suites passed")
            print("✅ Platform meets quality standards")
            print("✅ Safe for production deployment")
        else:
            print("🔴 NOT PRODUCTION READY")
            print("❌ Some test suites failed")
            print("⚠️ Issues must be resolved before production")
            
            # Specific recommendations
            if not self.results.get('functional', {}).get('success', True):
                print("🔧 Fix functional issues in core components")
            
            if not self.results.get('performance', {}).get('success', True):
                print("🚀 Optimize performance bottlenecks")
            
            if not self.results.get('security', {}).get('success', True):
                print("🔒 Address security vulnerabilities")
        
        # Next steps
        print(f"\n📋 Next Steps:")
        print("-" * 100)
        
        if overall_success:
            print("1. 🚀 Deploy to staging environment")
            print("2. 📊 Run integration tests with real APIs")
            print("3. 🔍 Perform manual testing")
            print("4. 📈 Monitor performance in staging")
            print("5. 🎯 Deploy to production with monitoring")
        else:
            print("1. 🔧 Fix failing tests")
            print("2. 🧪 Re-run test suite")
            print("3. 📊 Verify all components pass")
            print("4. 🔍 Perform additional testing")
            print("5. 📋 Review and update documentation")
        
        # Save results to file
        self._save_results_to_file()
        
        print("=" * 100)
        
        return overall_success
    
    def _save_results_to_file(self):
        """Save test results to JSON file"""
        try:
            results_data = {
                'timestamp': datetime.now().isoformat(),
                'total_duration': self.total_duration,
                'results': self.results,
                'overall_success': all(r['success'] for r in self.results.values())
            }
            
            filename = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w') as f:
                json.dump(results_data, f, indent=2)
            
            print(f"📄 Test results saved to: {filename}")
            
        except Exception as e:
            print(f"⚠️ Failed to save results to file: {e}")
    
    def run_quick_tests(self) -> bool:
        """Run a quick subset of tests for rapid feedback"""
        print("⚡ AWOT Trading Platform - Quick Test Suite")
        print("=" * 80)
        print("Running essential tests for rapid feedback...")
        print("=" * 80)
        
        # Run only critical functional tests
        # This would be a subset of the full test suite
        # For now, we'll run the full functional suite but could be optimized
        
        start_time = time.time()
        
        try:
            success = run_comprehensive_tests()
            duration = time.time() - start_time
            
            print(f"\n⚡ Quick tests completed in {duration:.2f}s")
            
            if success:
                print("✅ Quick tests PASSED - Core functionality working")
            else:
                print("❌ Quick tests FAILED - Core issues detected")
            
            return success
            
        except Exception as e:
            print(f"❌ Quick tests failed with error: {e}")
            return False


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='AWOT Trading Platform Test Runner')
    parser.add_argument(
        '--tests',
        nargs='+',
        choices=['functional', 'performance', 'security', 'all'],
        default=['all'],
        help='Test suites to run'
    )
    parser.add_argument(
        '--quick',
        action='store_true',
        help='Run quick tests only'
    )
    parser.add_argument(
        '--output',
        help='Output file for results'
    )
    
    args = parser.parse_args()
    
    # Determine which tests to run
    if 'all' in args.tests:
        test_types = ['functional', 'performance', 'security']
    else:
        test_types = args.tests
    
    # Create test runner
    runner = TestRunner()
    
    try:
        if args.quick:
            success = runner.run_quick_tests()
        else:
            results = runner.run_all_tests(test_types)
            success = all(r['success'] for r in results.values())
        
        # Exit with appropriate code
        exit_code = 0 if success else 1
        
        print(f"\n🏁 Testing completed with exit code: {exit_code}")
        
        return exit_code
        
    except KeyboardInterrupt:
        print("\n⚠️ Testing interrupted by user")
        return 130
    except Exception as e:
        print(f"\n❌ Testing failed with unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
