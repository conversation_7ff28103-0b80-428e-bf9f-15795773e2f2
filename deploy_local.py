#!/usr/bin/env python3
"""
AWOT Trading Platform - Local Deployment Script
Sets up and manages local production deployment
"""

import os
import sys
import subprocess
import time
import signal
import json
from datetime import datetime
from pathlib import Path
import threading
import webbrowser

class LocalDeployment:
    """Manages local deployment of AWOT platform"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.processes = {}
        self.is_running = False
        
        # Create necessary directories
        self.create_directories()
        
        print("🚀 AWOT Local Deployment Manager")
        print("=" * 60)
    
    def create_directories(self):
        """Create necessary directories for local deployment"""
        dirs = ['logs', 'data', 'backups', 'monitoring']
        
        for dir_name in dirs:
            dir_path = self.base_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            print(f"📁 Created directory: {dir_name}")
    
    def check_environment(self):
        """Check if environment is properly configured"""
        print("\n🔍 Checking Environment...")
        
        # Check .env file
        env_file = self.base_dir / '.env'
        if not env_file.exists():
            print("❌ .env file not found")
            print("📝 Creating .env from template...")
            
            template_file = self.base_dir / '.env.template'
            if template_file.exists():
                import shutil
                shutil.copy(template_file, env_file)
                print("✅ .env file created from template")
                print("⚠️  Please edit .env file with your actual credentials")
                return False
            else:
                print("❌ .env.template not found")
                return False
        
        # Check Python dependencies
        try:
            import streamlit
            import pandas
            import numpy
            import plotly
            import robin_stocks
            print("✅ Core dependencies installed")
        except ImportError as e:
            print(f"❌ Missing dependency: {e}")
            print("📦 Run: pip install -r requirements.txt")
            return False
        
        # Check virtual environment
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            print("✅ Virtual environment active")
        else:
            print("⚠️  Virtual environment not detected (recommended)")
        
        print("✅ Environment check complete")
        return True
    
    def start_dashboard(self):
        """Start the Streamlit dashboard"""
        print("\n📊 Starting Dashboard...")
        
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            "dashboard/main_dashboard.py",
            "--server.port=8502",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false",
            "--server.headless=true"
        ]
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.base_dir
            )
            
            self.processes['dashboard'] = process
            print("✅ Dashboard started on http://localhost:8502")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start dashboard: {e}")
            return False
    
    def start_api(self):
        """Start the FastAPI server"""
        print("\n🔌 Starting API Server...")
        
        cmd = [
            sys.executable, "-m", "uvicorn",
            "api.main:app",
            "--host", "0.0.0.0",
            "--port", "8080",
            "--reload"
        ]
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.base_dir
            )
            
            self.processes['api'] = process
            print("✅ API server started on http://localhost:8080")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start API server: {e}")
            return False
    
    def start_trading_engine(self, mode='paper'):
        """Start the trading engine"""
        print(f"\n🤖 Starting Trading Engine ({mode} mode)...")
        
        cmd = [sys.executable, "run_trading.py", mode]
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.base_dir
            )
            
            self.processes['trading'] = process
            print(f"✅ Trading engine started in {mode} mode")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start trading engine: {e}")
            return False
    
    def start_monitoring(self):
        """Start monitoring services (simplified local version)"""
        print("\n📈 Starting Monitoring...")
        
        # Create a simple monitoring script
        monitoring_script = self.base_dir / 'monitoring' / 'local_monitor.py'
        
        if not monitoring_script.exists():
            self.create_monitoring_script(monitoring_script)
        
        cmd = [sys.executable, str(monitoring_script)]
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.base_dir
            )
            
            self.processes['monitoring'] = process
            print("✅ Monitoring started")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start monitoring: {e}")
            return False
    
    def create_monitoring_script(self, script_path):
        """Create a simple local monitoring script"""
        script_content = '''#!/usr/bin/env python3
"""Simple local monitoring for AWOT platform"""

import time
import psutil
import requests
import json
from datetime import datetime

def check_services():
    """Check if services are running"""
    services = {
        'dashboard': 'http://localhost:8502',
        'api': 'http://localhost:8080/health'
    }
    
    status = {}
    
    for service, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            status[service] = response.status_code == 200
        except:
            status[service] = False
    
    return status

def log_metrics():
    """Log system metrics"""
    cpu_percent = psutil.cpu_percent()
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'disk_percent': disk.percent,
        'services': check_services()
    }
    
    # Log to file
    with open('logs/monitoring.log', 'a') as f:
        f.write(json.dumps(metrics) + '\\n')
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] CPU: {cpu_percent:.1f}% | "
          f"Memory: {memory.percent:.1f}% | Disk: {disk.percent:.1f}%")

if __name__ == "__main__":
    print("🔍 AWOT Local Monitoring Started")
    
    while True:
        try:
            log_metrics()
            time.sleep(30)  # Check every 30 seconds
        except KeyboardInterrupt:
            print("\\n🛑 Monitoring stopped")
            break
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
            time.sleep(60)
'''
        
        script_path.parent.mkdir(exist_ok=True)
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(script_path, 0o755)
    
    def deploy_full(self, trading_mode='paper'):
        """Deploy the full AWOT platform locally"""
        print("\n🚀 Starting Full Local Deployment...")
        print("=" * 60)
        
        # Check environment first
        if not self.check_environment():
            print("\n❌ Environment check failed. Please fix issues and try again.")
            return False
        
        # Start services in order
        services = [
            ('Dashboard', self.start_dashboard),
            ('API Server', self.start_api),
            ('Monitoring', self.start_monitoring),
            ('Trading Engine', lambda: self.start_trading_engine(trading_mode))
        ]
        
        for service_name, start_func in services:
            if not start_func():
                print(f"\n❌ Failed to start {service_name}")
                self.stop_all()
                return False
            
            # Wait a moment between services
            time.sleep(2)
        
        self.is_running = True
        
        print("\n" + "=" * 60)
        print("🎉 AWOT Platform Successfully Deployed!")
        print("=" * 60)
        print("📊 Dashboard: http://localhost:8502")
        print("🔌 API: http://localhost:8080")
        print("📈 API Docs: http://localhost:8080/docs")
        print("📝 Logs: ./logs/")
        print("=" * 60)
        
        # Open dashboard in browser
        try:
            webbrowser.open('http://localhost:8502')
            print("🌐 Dashboard opened in browser")
        except:
            print("⚠️  Could not open browser automatically")
        
        return True
    
    def stop_all(self):
        """Stop all running services"""
        print("\n🛑 Stopping all services...")
        
        for service_name, process in self.processes.items():
            try:
                process.terminate()
                process.wait(timeout=10)
                print(f"✅ Stopped {service_name}")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"🔪 Force killed {service_name}")
            except Exception as e:
                print(f"❌ Error stopping {service_name}: {e}")
        
        self.processes.clear()
        self.is_running = False
        print("✅ All services stopped")
    
    def status(self):
        """Check status of all services"""
        print("\n📊 Service Status:")
        print("-" * 40)
        
        if not self.processes:
            print("❌ No services running")
            return
        
        for service_name, process in self.processes.items():
            if process.poll() is None:
                print(f"✅ {service_name}: Running (PID: {process.pid})")
            else:
                print(f"❌ {service_name}: Stopped")
    
    def logs(self, service=None):
        """Show logs for services"""
        log_dir = self.base_dir / 'logs'
        
        if service:
            log_file = log_dir / f'{service}.log'
            if log_file.exists():
                print(f"\n📝 {service} logs:")
                print("-" * 40)
                with open(log_file, 'r') as f:
                    print(f.read()[-1000:])  # Last 1000 characters
            else:
                print(f"❌ No logs found for {service}")
        else:
            print("\n📝 Available log files:")
            for log_file in log_dir.glob('*.log'):
                print(f"  - {log_file.name}")
    
    def run_tests(self):
        """Run the test suite"""
        print("\n🧪 Running Test Suite...")
        
        cmd = [sys.executable, "run_tests.py", "--quick"]
        
        try:
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            print("Test Output:")
            print("-" * 40)
            print(result.stdout)
            
            if result.stderr:
                print("Errors:")
                print(result.stderr)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Failed to run tests: {e}")
            return False


def main():
    """Main deployment interface"""
    deployment = LocalDeployment()
    
    if len(sys.argv) < 2:
        print("\nUsage:")
        print("  python deploy_local.py deploy [paper|live]  - Deploy full platform")
        print("  python deploy_local.py stop                - Stop all services")
        print("  python deploy_local.py status              - Check service status")
        print("  python deploy_local.py logs [service]      - Show logs")
        print("  python deploy_local.py test                - Run test suite")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == 'deploy':
            trading_mode = sys.argv[2] if len(sys.argv) > 2 else 'paper'
            
            if trading_mode not in ['paper', 'live']:
                print("❌ Trading mode must be 'paper' or 'live'")
                return
            
            if trading_mode == 'live':
                print("⚠️  WARNING: Live trading mode selected!")
                print("   This will use real money and execute real trades.")
                confirmation = input("   Type 'CONFIRM' to proceed: ")
                if confirmation != 'CONFIRM':
                    print("❌ Live trading cancelled")
                    return
            
            success = deployment.deploy_full(trading_mode)
            
            if success:
                print("\n⌨️  Press Ctrl+C to stop all services")
                try:
                    while deployment.is_running:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n🛑 Shutdown requested...")
                    deployment.stop_all()
        
        elif command == 'stop':
            deployment.stop_all()
        
        elif command == 'status':
            deployment.status()
        
        elif command == 'logs':
            service = sys.argv[2] if len(sys.argv) > 2 else None
            deployment.logs(service)
        
        elif command == 'test':
            deployment.run_tests()
        
        else:
            print(f"❌ Unknown command: {command}")
    
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        deployment.stop_all()
    except Exception as e:
        print(f"❌ Error: {e}")
        deployment.stop_all()


if __name__ == "__main__":
    main()
