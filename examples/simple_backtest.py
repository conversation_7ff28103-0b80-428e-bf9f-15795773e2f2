#!/usr/bin/env python3
"""
Simple Backtesting Example for AWOT Phase 1

This script demonstrates how to run a basic backtest using the implemented
technical analysis and strategy framework.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from indicators.technical_indicators import TechnicalIndicators
from strategies.strategy import MomentumStrategy
from risk.risk_manager import RiskManager


def create_sample_stock_data(symbol="DEMO", days=252, start_price=100):
    """Create realistic sample stock data for backtesting"""
    print(f"📊 Creating {days} days of sample data for {symbol}...")
    
    # Generate enough dates to get the required trading days
    all_dates = pd.date_range(start='2023-01-01', periods=days*2, freq='D')
    trading_dates = [d for d in all_dates if d.weekday() < 5][:days]  # Trading days only
    
    np.random.seed(42)  # For reproducible results
    
    # Generate realistic price movement with trend and volatility
    returns = np.random.normal(0.0008, 0.02, days)  # Slight upward bias
    prices = [start_price]
    
    for i in range(1, days):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(max(new_price, 1.0))  # Prevent negative prices
    
    # Create OHLCV data
    data = []
    for i, price in enumerate(prices):
        # Generate realistic OHLC from close price
        open_price = price * np.random.uniform(0.998, 1.002)
        high_price = max(open_price, price) * np.random.uniform(1.000, 1.012)
        low_price = min(open_price, price) * np.random.uniform(0.988, 1.000)
        volume = np.random.randint(1000000, 5000000)
        
        data.append({
            'Open': open_price,
            'High': high_price,
            'Low': low_price,
            'Close': price,
            'Volume': volume
        })
    
    df = pd.DataFrame(data, index=trading_dates[:len(data)])
    print(f"✅ Created {len(df)} days of sample data")
    return df


def run_simple_backtest():
    """Run a simple backtest demonstration"""
    print("\n" + "="*60)
    print("📈 SIMPLE BACKTESTING DEMONSTRATION")
    print("="*60)
    
    # Create sample data
    stock_data = create_sample_stock_data("DEMO_STOCK", days=100, start_price=150)
    
    # Initialize components
    indicators = TechnicalIndicators()
    strategy = MomentumStrategy()
    risk_manager = RiskManager(initial_capital=50000)
    
    print(f"\n🔧 Calculating technical indicators...")
    # Calculate technical indicators
    analysis = indicators.calculate_all_indicators(stock_data)
    
    if analysis.empty:
        print("❌ Failed to calculate indicators")
        return
    
    print(f"✅ Calculated {len(analysis.columns)} technical indicators")
    
    # Simulate simple trading logic
    print(f"\n🎯 Simulating trading decisions...")
    
    trades = []
    portfolio_value = strategy.capital
    
    # Simple momentum strategy simulation
    for i in range(20, len(analysis)):  # Start after indicators warm up
        current_data = analysis.iloc[i]
        
        # Simple entry condition: RSI oversold + MACD bullish
        if (current_data.get('RSI', 50) < 35 and 
            current_data.get('MACD_Bullish', 0) == 1 and
            current_data.get('High_Volume', 0) == 1):
            
            entry_price = current_data['Close']
            target_price = entry_price * 1.15  # 15% target
            stop_price = entry_price * 0.92    # 8% stop
            
            # Calculate position size
            risk_amount = portfolio_value * 0.05  # 5% risk
            risk_per_share = entry_price - stop_price
            position_size = int(risk_amount / risk_per_share) if risk_per_share > 0 else 0
            
            if position_size > 0:
                # Simulate exit after 5-10 days
                exit_day = min(i + np.random.randint(5, 11), len(analysis) - 1)
                exit_price = analysis.iloc[exit_day]['Close']
                
                # Calculate P&L
                pnl = (exit_price - entry_price) * position_size
                portfolio_value += pnl
                
                trades.append({
                    'entry_date': analysis.index[i],
                    'exit_date': analysis.index[exit_day],
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'position_size': position_size,
                    'pnl': pnl,
                    'return_pct': (exit_price - entry_price) / entry_price
                })
    
    # Calculate results
    if trades:
        total_pnl = sum(t['pnl'] for t in trades)
        winning_trades = len([t for t in trades if t['pnl'] > 0])
        total_trades = len(trades)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_return = (portfolio_value - strategy.capital) / strategy.capital
        
        print(f"\n📊 Backtest Results:")
        print(f"   Initial Capital: ${strategy.capital:,.2f}")
        print(f"   Final Portfolio Value: ${portfolio_value:,.2f}")
        print(f"   Total Return: {total_return:.2%}")
        print(f"   Total P&L: ${total_pnl:,.2f}")
        print(f"   Total Trades: {total_trades}")
        print(f"   Winning Trades: {winning_trades}")
        print(f"   Win Rate: {win_rate:.1%}")
        
        if total_trades > 0:
            avg_return = np.mean([t['return_pct'] for t in trades])
            print(f"   Average Return per Trade: {avg_return:.2%}")
        
        print(f"\n📈 Sample Trades:")
        for i, trade in enumerate(trades[:5]):  # Show first 5 trades
            print(f"   Trade {i+1}: {trade['entry_date'].strftime('%Y-%m-%d')} -> "
                  f"{trade['exit_date'].strftime('%Y-%m-%d')}, "
                  f"${trade['entry_price']:.2f} -> ${trade['exit_price']:.2f}, "
                  f"P&L: ${trade['pnl']:.2f} ({trade['return_pct']:.1%})")
        
        if len(trades) > 5:
            print(f"   ... and {len(trades) - 5} more trades")
    
    else:
        print(f"❌ No trades generated with current criteria")
    
    # Show latest technical analysis
    latest = analysis.iloc[-1]
    print(f"\n🔍 Latest Market Analysis:")
    print(f"   Current Price: ${latest['Close']:.2f}")
    print(f"   RSI: {latest.get('RSI', 0):.1f}")
    print(f"   MACD Signal: {'Bullish' if latest.get('MACD_Bullish', 0) else 'Bearish'}")
    print(f"   Volume Ratio: {latest.get('Volume_Ratio', 0):.1f}x")
    print(f"   Price vs SMA20: {((latest['Close'] / latest.get('SMA_20', latest['Close'])) - 1) * 100:+.1f}%")
    
    # Signal strength analysis
    signal_strength = indicators.get_signal_strength(analysis)
    print(f"\n🎯 Current Signal Strength:")
    print(f"   Momentum: {signal_strength.get('momentum', 0):.2f}")
    print(f"   Volatility: {signal_strength.get('volatility', 0):.2f}")
    print(f"   Breakout: {signal_strength.get('breakout', 0):.2f}")
    print(f"   Overall: {signal_strength.get('overall', 0):.2f}")


def main():
    """Main function"""
    print("🚀 AWOT Phase 1 - Simple Backtesting Example")
    print("=" * 60)
    print("This example demonstrates basic backtesting capabilities")
    print("using simulated market data and momentum strategy logic.")
    print("=" * 60)
    
    try:
        run_simple_backtest()
        
        print(f"\n" + "="*60)
        print("✅ SIMPLE BACKTEST COMPLETE")
        print("="*60)
        print("🎯 This demonstrates:")
        print("   ✅ Technical indicator calculation")
        print("   ✅ Simple trading signal generation")
        print("   ✅ Position sizing and risk management")
        print("   ✅ Trade simulation and P&L calculation")
        print("   ✅ Performance metrics and analysis")
        
        print(f"\n📚 Next Steps:")
        print("   1. Try different strategy parameters")
        print("   2. Add more sophisticated entry/exit rules")
        print("   3. Implement proper backtesting with the BacktestEngine")
        print("   4. Test with real historical data")
        print("   5. Add more technical indicators and filters")
        
        print(f"\n⚠️ Note:")
        print("   This is a simplified example for demonstration.")
        print("   Real backtesting should use the full BacktestEngine")
        print("   with proper historical data and realistic constraints.")
        
    except Exception as e:
        print(f"\n❌ Error during backtest: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
