#!/usr/bin/env python3
"""
Live Trading Demo for AWOT Phase 3
Demonstrates the live trading engine with paper trading mode
"""

import sys
import os
import time
from datetime import datetime

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from trading.live_trading_engine import LiveTradingEngine, TradingMode
from trading.paper_trading import PaperTradingEngine
from trading.robinhood_client import OrderSide, OptionType
from signals.signal_generator import TradingSignal, SignalType, SignalStrength


def demo_paper_trading():
    """Demonstrate paper trading functionality"""
    print("\n" + "="*60)
    print("📝 PAPER TRADING DEMONSTRATION")
    print("="*60)
    
    # Initialize paper trading engine
    paper_engine = PaperTradingEngine(initial_capital=50000)
    
    print(f"💰 Initial Capital: ${paper_engine.initial_capital:,.2f}")
    print(f"💵 Available Cash: ${paper_engine.cash:,.2f}")
    
    # Simulate some trades
    print(f"\n📈 Executing Sample Trades...")
    
    # Buy some stock
    result1 = paper_engine.place_order(
        symbol="AAPL",
        quantity=100,
        side=OrderSide.BUY,
        price=150.0,
        order_type="stock",
        strategy_name="demo"
    )
    
    if result1['success']:
        print(f"✅ Stock trade: BUY 100 AAPL at ${result1['execution_price']:.2f}")
    else:
        print(f"❌ Stock trade failed: {result1['error']}")
    
    # Buy an option
    result2 = paper_engine.place_order(
        symbol="TSLA",
        quantity=5,
        side=OrderSide.BUY,
        price=8.50,
        order_type="option",
        strike_price=200.0,
        expiration_date="2024-01-19",
        option_type=OptionType.CALL,
        strategy_name="demo"
    )
    
    if result2['success']:
        print(f"✅ Option trade: BUY 5 TSLA 200C 01/19 at ${result2['execution_price']:.2f}")
    else:
        print(f"❌ Option trade failed: {result2['error']}")
    
    # Update positions with mock price changes
    print(f"\n📊 Updating Positions with Market Data...")
    
    market_data = {
        "AAPL": 155.0,  # Stock went up
        "TSLA": 210.0   # Underlying went up, option should be worth more
    }
    
    paper_engine.update_positions(market_data)
    
    # Show portfolio summary
    summary = paper_engine.get_portfolio_summary()
    
    print(f"\n📋 Portfolio Summary:")
    print(f"   Portfolio Value: ${summary['portfolio_value']:,.2f}")
    print(f"   Cash: ${summary['cash']:,.2f}")
    print(f"   Total P&L: ${summary['total_pnl']:+,.2f}")
    print(f"   Total Return: {summary['total_return']:+.2%}")
    print(f"   Unrealized P&L: ${summary['unrealized_pnl']:+,.2f}")
    print(f"   Open Positions: {summary['open_positions']}")
    
    # Show positions
    positions = paper_engine.get_positions()
    if positions:
        print(f"\n📍 Current Positions:")
        for position in positions:
            if position['is_open']:
                print(f"   {position['symbol']} {position['position_type']}: "
                      f"{position['quantity']} @ ${position['entry_price']:.2f} "
                      f"(Current: ${position['current_price']:.2f}, "
                      f"P&L: ${position['unrealized_pnl']:+.2f})")
    
    # Show trade history
    trades = paper_engine.get_trade_history()
    if trades:
        print(f"\n📜 Trade History:")
        for trade in trades:
            print(f"   {trade['timestamp'][:19]}: {trade['side'].upper()} "
                  f"{trade['quantity']} {trade['symbol']} at ${trade['price']:.2f}")
    
    return paper_engine


def demo_live_trading_engine():
    """Demonstrate live trading engine functionality"""
    print("\n" + "="*60)
    print("🤖 LIVE TRADING ENGINE DEMONSTRATION")
    print("="*60)
    
    # Initialize live trading engine in paper mode
    engine = LiveTradingEngine(TradingMode.PAPER)
    
    print(f"🔧 Trading Engine initialized in {engine.trading_mode.value} mode")
    
    # Show initial status
    status = engine.get_status()
    print(f"📊 Initial Status:")
    print(f"   State: {status['state']}")
    print(f"   Active Strategies: {status['active_strategies']}")
    print(f"   Max Positions: {status['config']['max_positions']}")
    print(f"   Risk per Trade: {status['config']['risk_per_trade']:.1%}")
    
    # Start the engine
    print(f"\n▶️ Starting Trading Engine...")
    if engine.start():
        print(f"✅ Trading Engine started successfully!")
        
        # Let it run for a few seconds
        print(f"⏳ Running for 5 seconds...")
        time.sleep(5)
        
        # Check status
        status = engine.get_status()
        print(f"📊 Running Status:")
        print(f"   State: {status['state']}")
        print(f"   Uptime: {status['stats']['uptime']:.1f} seconds")
        print(f"   Signals Generated: {status['stats']['signals_generated']}")
        print(f"   Signals Executed: {status['stats']['signals_executed']}")
        
        # Test pause/resume
        print(f"\n⏸️ Pausing engine...")
        engine.pause()
        
        status = engine.get_status()
        print(f"   State after pause: {status['state']}")
        
        print(f"▶️ Resuming engine...")
        engine.resume()
        
        status = engine.get_status()
        print(f"   State after resume: {status['state']}")
        
        # Test strategy controls
        print(f"\n🎯 Testing Strategy Controls...")
        print(f"   Disabling momentum strategy...")
        engine.disable_strategy('momentum')
        
        status = engine.get_status()
        print(f"   Active strategies: {status['active_strategies']}")
        
        print(f"   Re-enabling momentum strategy...")
        engine.enable_strategy('momentum')
        
        status = engine.get_status()
        print(f"   Active strategies: {status['active_strategies']}")
        
        # Test configuration update
        print(f"\n⚙️ Testing Configuration Update...")
        new_config = {
            'max_positions': 5,
            'risk_per_trade': 0.15,
            'max_daily_trades': 20
        }
        engine.update_config(new_config)
        
        status = engine.get_status()
        print(f"   Updated max positions: {status['config']['max_positions']}")
        print(f"   Updated risk per trade: {status['config']['risk_per_trade']:.1%}")
        
        # Stop the engine
        print(f"\n⏹️ Stopping Trading Engine...")
        engine.stop()
        
        status = engine.get_status()
        print(f"   Final state: {status['state']}")
        
    else:
        print(f"❌ Failed to start Trading Engine")
    
    return engine


def demo_order_management():
    """Demonstrate order management functionality"""
    print("\n" + "="*60)
    print("📋 ORDER MANAGEMENT DEMONSTRATION")
    print("="*60)
    
    from trading.robinhood_client import RobinhoodClient
    from trading.order_manager import OrderManager, OrderPriority
    
    # Initialize components
    client = RobinhoodClient()  # Will use paper trading mode
    order_manager = OrderManager(client)
    
    print(f"🔧 Order Manager initialized")
    
    # Start order manager
    order_manager.start()
    print(f"▶️ Order Manager started")
    
    # Submit some test orders
    print(f"\n📝 Submitting Test Orders...")
    
    # Stock order
    stock_order_id = order_manager.submit_stock_order(
        symbol="AAPL",
        quantity=100,
        side=OrderSide.BUY,
        price=150.0,
        priority=OrderPriority.HIGH,
        strategy_name="demo"
    )
    
    if stock_order_id:
        print(f"✅ Stock order submitted: {stock_order_id}")
    
    # Option order
    option_order_id = order_manager.submit_option_order(
        symbol="TSLA",
        strike_price=200.0,
        expiration_date="2024-01-19",
        option_type=OptionType.CALL,
        quantity=5,
        side=OrderSide.BUY,
        price=8.50,
        priority=OrderPriority.NORMAL,
        strategy_name="demo"
    )
    
    if option_order_id:
        print(f"✅ Option order submitted: {option_order_id}")
    
    # Wait for orders to process
    print(f"⏳ Waiting for orders to process...")
    time.sleep(3)
    
    # Check order status
    print(f"\n📊 Order Status:")
    
    if stock_order_id:
        status = order_manager.get_order_status(stock_order_id)
        if status:
            print(f"   Stock Order {stock_order_id}: {status.status.value}")
    
    if option_order_id:
        status = order_manager.get_order_status(option_order_id)
        if status:
            print(f"   Option Order {option_order_id}: {status.status.value}")
    
    # Show statistics
    stats = order_manager.get_statistics()
    print(f"\n📈 Order Manager Statistics:")
    print(f"   Total Orders: {stats['total_orders']}")
    print(f"   Successful Orders: {stats['successful_orders']}")
    print(f"   Failed Orders: {stats['failed_orders']}")
    print(f"   Retry Count: {stats['retry_count']}")
    
    # Stop order manager
    print(f"\n⏹️ Stopping Order Manager...")
    order_manager.stop()
    
    return order_manager


def main():
    """Main demonstration function"""
    print("🚀 AWOT Phase 3 - Live Trading Integration Demo")
    print("=" * 80)
    print("This demonstration showcases the live trading capabilities")
    print("including paper trading, order management, and trading engine.")
    print("=" * 80)
    
    try:
        # Demo 1: Paper Trading
        paper_engine = demo_paper_trading()
        
        # Demo 2: Live Trading Engine
        trading_engine = demo_live_trading_engine()
        
        # Demo 3: Order Management
        order_manager = demo_order_management()
        
        print("\n" + "="*80)
        print("✅ PHASE 3 LIVE TRADING DEMO COMPLETE")
        print("="*80)
        print("🎯 Key Features Demonstrated:")
        print("   ✅ Paper Trading Engine - Realistic simulation")
        print("   ✅ Live Trading Engine - Full automation")
        print("   ✅ Order Management - Robust execution")
        print("   ✅ Robinhood Integration - API connectivity")
        print("   ✅ Risk Management - Position sizing")
        print("   ✅ Strategy Control - Enable/disable strategies")
        print("   ✅ Real-time Monitoring - Status tracking")
        
        print(f"\n🔄 Next Steps:")
        print("   1. Configure Robinhood credentials for live trading")
        print("   2. Test with small positions in paper mode")
        print("   3. Validate strategies with historical data")
        print("   4. Enable live trading with proper risk limits")
        print("   5. Monitor performance and adjust parameters")
        
        print(f"\n📚 Integration Points:")
        print("   • Dashboard: Real-time trading controls")
        print("   • Notifications: Trade alerts and status updates")
        print("   • Risk Management: Dynamic position sizing")
        print("   • Signal Generation: Automated trade execution")
        print("   • Portfolio Monitoring: Live P&L tracking")
        
        print(f"\n⚠️ Important Notes:")
        print("   • Always test thoroughly in paper mode first")
        print("   • Start with small position sizes")
        print("   • Monitor risk metrics continuously")
        print("   • Have emergency stop procedures ready")
        print("   • Keep detailed logs of all trading activity")
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
