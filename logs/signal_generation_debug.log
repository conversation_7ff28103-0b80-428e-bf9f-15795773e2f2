2025-07-21 21:20:02,017 - INFO - 🔍 COMPREHENSIVE SIGNAL GENERATION DEBUG
2025-07-21 21:20:02,017 - INFO - ============================================================
2025-07-21 21:20:02,018 - INFO - 
============================================================
2025-07-21 21:20:02,018 - INFO - DEBUGGING AAPL
2025-07-21 21:20:02,018 - INFO - ============================================================
2025-07-21 21:20:02,018 - INFO - 
🔍 DEBUGGING BASIC SIGNAL GENERATION FOR AAPL
2025-07-21 21:20:02,018 - INFO - ==================================================
2025-07-21 21:20:02,018 - INFO - 
🔍 DEBUGGING MARKET DATA FOR AAPL
2025-07-21 21:20:02,018 - INFO - ==================================================
2025-07-21 21:20:02,018 - DEBUG - Entering history()
2025-07-21 21:20:02,020 - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-07-21 21:20:02,020 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-07-21 21:20:02,045 - DEBUG -  Entering history()
2025-07-21 21:20:02,046 - DEBUG - AAPL: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:02,046 - DEBUG -   Entering get()
2025-07-21 21:20:02,046 - DEBUG -    Entering _make_request()
2025-07-21 21:20:02,046 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-07-21 21:20:02,046 - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:02,046 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:02,046 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:02,046 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,046 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:02,046 - DEBUG -        Entering _load_cookie_curlCffi()
2025-07-21 21:20:02,047 - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-07-21 21:20:02,048 - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-07-21 21:20:02,048 - DEBUG -        Exiting _load_cookie_curlCffi()
2025-07-21 21:20:02,048 - DEBUG - reusing persistent cookie
2025-07-21 21:20:02,048 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:02,049 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:02,049 - DEBUG -        Entering _get_cookie_basic()
2025-07-21 21:20:02,049 - DEBUG - reusing cookie
2025-07-21 21:20:02,049 - DEBUG -        Exiting _get_cookie_basic()
2025-07-21 21:20:02,349 - DEBUG - crumb = 'wM.98OtspMf'
2025-07-21 21:20:02,349 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:02,349 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,350 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:02,696 - DEBUG - response code=200
2025-07-21 21:20:02,697 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:02,697 - DEBUG -   Exiting get()
2025-07-21 21:20:02,699 - DEBUG - AAPL: yfinance received OHLC data: 2025-06-23 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:02,702 - DEBUG - AAPL: OHLC after cleaning: 2025-06-23 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:02,708 - DEBUG - AAPL: OHLC after combining events: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:02,716 - DEBUG - AAPL: yfinance returning OHLC: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:02,717 - DEBUG -  Exiting history()
2025-07-21 21:20:02,717 - DEBUG - Exiting history()
2025-07-21 21:20:02,717 - INFO - ✅ Basic stock data: (20, 7)
2025-07-21 21:20:02,718 - INFO -    Date range: 2025-06-23 00:00:00-04:00 to 2025-07-21 00:00:00-04:00
2025-07-21 21:20:02,718 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-07-21 21:20:02,718 - INFO -    Last close: $212.48
2025-07-21 21:20:02,719 - INFO -    Volume: 51,064,253
2025-07-21 21:20:02,719 - DEBUG - Entering history()
2025-07-21 21:20:02,720 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-07-21 21:20:02,720 - DEBUG -  Entering history()
2025-07-21 21:20:02,720 - DEBUG - AAPL: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:02,721 - DEBUG -   Entering get()
2025-07-21 21:20:02,721 - DEBUG -    Entering _make_request()
2025-07-21 21:20:02,721 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-07-21 21:20:02,722 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:02,722 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:02,723 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:02,723 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,723 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:02,724 - DEBUG - reusing cookie
2025-07-21 21:20:02,724 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:02,725 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:02,725 - DEBUG - reusing crumb
2025-07-21 21:20:02,725 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:02,726 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,726 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:02,818 - DEBUG - response code=200
2025-07-21 21:20:02,819 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:02,819 - DEBUG -   Exiting get()
2025-07-21 21:20:02,821 - DEBUG - AAPL: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:02,823 - DEBUG - AAPL: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:02,835 - DEBUG - AAPL: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:02,845 - DEBUG - AAPL: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:02,846 - DEBUG -  Exiting history()
2025-07-21 21:20:02,846 - DEBUG - Exiting history()
2025-07-21 21:20:02,846 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/AAPL
2025-07-21 21:20:02,847 - DEBUG - Entering get()
2025-07-21 21:20:02,847 - DEBUG -  Entering _make_request()
2025-07-21 21:20:02,847 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/AAPL
2025-07-21 21:20:02,847 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'AAPL'}
2025-07-21 21:20:02,847 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:02,848 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:02,848 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,848 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:02,848 - DEBUG - reusing cookie
2025-07-21 21:20:02,848 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:02,849 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:02,849 - DEBUG - reusing crumb
2025-07-21 21:20:02,849 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:02,849 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,849 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:02,906 - DEBUG - response code=200
2025-07-21 21:20:02,908 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:02,908 - DEBUG - Exiting get()
2025-07-21 21:20:02,909 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:02,909 - DEBUG - Entering get()
2025-07-21 21:20:02,909 - DEBUG -  Entering _make_request()
2025-07-21 21:20:02,909 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:02,909 - DEBUG - params={'symbols': 'AAPL', 'formatted': 'false'}
2025-07-21 21:20:02,910 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:02,910 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:02,910 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,910 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:02,910 - DEBUG - reusing cookie
2025-07-21 21:20:02,911 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:02,911 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:02,911 - DEBUG - reusing crumb
2025-07-21 21:20:02,911 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:02,911 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,911 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:02,962 - DEBUG - response code=200
2025-07-21 21:20:02,962 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:02,962 - DEBUG - Exiting get()
2025-07-21 21:20:02,964 - DEBUG - Entering get()
2025-07-21 21:20:02,964 - DEBUG -  Entering _make_request()
2025-07-21 21:20:02,965 - DEBUG - url=https://query1.finance.yahoo.com/ws/fundamentals-timeseries/v1/finance/timeseries/AAPL?symbol=AAPL&type=trailingPegRatio&period1=1737417600&period2=1753228800
2025-07-21 21:20:02,965 - DEBUG - params=None
2025-07-21 21:20:02,965 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:02,965 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:02,965 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,966 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:02,966 - DEBUG - reusing cookie
2025-07-21 21:20:02,966 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:02,966 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:02,971 - DEBUG - reusing crumb
2025-07-21 21:20:02,971 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:02,971 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:02,971 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,022 - DEBUG - response code=200
2025-07-21 21:20:03,022 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,022 - DEBUG - Exiting get()
2025-07-21 21:20:03,028 - DEBUG - Entering get()
2025-07-21 21:20:03,028 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,028 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/AAPL
2025-07-21 21:20:03,029 - DEBUG - params=None
2025-07-21 21:20:03,029 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,030 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,030 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,030 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,031 - DEBUG - reusing cookie
2025-07-21 21:20:03,031 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,032 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,032 - DEBUG - reusing crumb
2025-07-21 21:20:03,035 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,035 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,035 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,098 - DEBUG - response code=200
2025-07-21 21:20:03,101 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,101 - DEBUG - Exiting get()
2025-07-21 21:20:03,102 - DEBUG - Entering get()
2025-07-21 21:20:03,103 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,103 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/AAPL?date=1753401600
2025-07-21 21:20:03,103 - DEBUG - params=None
2025-07-21 21:20:03,103 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,103 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,103 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,104 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,104 - DEBUG - reusing cookie
2025-07-21 21:20:03,104 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,104 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,104 - DEBUG - reusing crumb
2025-07-21 21:20:03,104 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,105 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,105 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,177 - DEBUG - response code=200
2025-07-21 21:20:03,178 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,178 - DEBUG - Exiting get()
2025-07-21 21:20:03,195 - INFO - ✅ Comprehensive data keys: ['symbol', 'timestamp', 'price_data', 'volume_analysis', 'options_flow', 'historical_data', 'data_quality']
2025-07-21 21:20:03,196 - INFO -    Historical data length: 50
2025-07-21 21:20:03,196 - INFO -    Sample data point: {'Open': 197.46106353953903, 'High': 199.78801395815432, 'Low': 194.42503624466005, 'Close': 197.2313690185547, 'Volume': 50478900, 'Dividends': 0.0, 'Stock Splits': 0.0}
2025-07-21 21:20:03,196 - INFO -    Data quality score: 100
2025-07-21 21:20:03,196 - INFO -    Data issues: []
2025-07-21 21:20:03,197 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/AAPL
2025-07-21 21:20:03,197 - DEBUG - Entering get()
2025-07-21 21:20:03,197 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,197 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/AAPL
2025-07-21 21:20:03,197 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'AAPL'}
2025-07-21 21:20:03,197 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,198 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,198 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,198 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,198 - DEBUG - reusing cookie
2025-07-21 21:20:03,198 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,199 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,199 - DEBUG - reusing crumb
2025-07-21 21:20:03,199 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,199 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,199 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,207 - DEBUG - response code=200
2025-07-21 21:20:03,208 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,208 - DEBUG - Exiting get()
2025-07-21 21:20:03,208 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:03,208 - DEBUG - Entering get()
2025-07-21 21:20:03,209 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,209 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:03,209 - DEBUG - params={'symbols': 'AAPL', 'formatted': 'false'}
2025-07-21 21:20:03,209 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,209 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,210 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,210 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,210 - DEBUG - reusing cookie
2025-07-21 21:20:03,210 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,210 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,210 - DEBUG - reusing crumb
2025-07-21 21:20:03,211 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,211 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,211 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,217 - DEBUG - response code=200
2025-07-21 21:20:03,217 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,217 - DEBUG - Exiting get()
2025-07-21 21:20:03,219 - INFO - ✅ Real-time data: True
2025-07-21 21:20:03,219 - INFO -    Current price: $212.48
2025-07-21 21:20:03,219 - INFO -    Volume: 51,064,253
2025-07-21 21:20:03,219 - INFO -    Beta: 1.211
2025-07-21 21:20:03,220 - INFO - ✅ Historical DataFrame: (50, 7)
2025-07-21 21:20:03,220 - INFO - 
🔍 DEBUGGING TECHNICAL INDICATORS FOR AAPL
2025-07-21 21:20:03,220 - INFO - ==================================================
2025-07-21 21:20:03,282 - INFO - ✅ Technical indicators shape: (50, 66)
2025-07-21 21:20:03,282 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits', 'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'SMA_200', 'EMA_5', 'EMA_10', 'EMA_20', 'EMA_50', 'SMA_Cross_5_20', 'SMA_Cross_20_50', 'EMA_Cross_5_20', 'RSI', 'RSI_Oversold', 'RSI_Overbought', 'MACD', 'MACD_Signal', 'MACD_Histogram', 'MACD_Bullish', 'Stoch_K', 'Stoch_D', 'Williams_R', 'ROC', 'BB_Upper', 'BB_Middle', 'BB_Lower', 'BB_Width', 'BB_Position', 'ATR', 'ATR_Percent', 'KC_Upper', 'KC_Middle', 'KC_Lower', 'DC_Upper', 'DC_Lower', 'DC_Middle', 'Volume_SMA_20', 'Volume_Ratio', 'High_Volume', 'OBV', 'VPT', 'ADL', 'CMF', 'ADX', 'ADX_Strong_Trend', 'PSAR', 'CCI', 'DPO', 'Price_Change_1D', 'Price_Change_5D', 'Price_Change_20D', 'ATR_Expansion', 'Vol_Expansion', 'Breakout_High', 'Breakout_Low', 'Resistance_20', 'Support_20', 'Near_Resistance', 'Near_Support']
2025-07-21 21:20:03,283 - INFO -    RSI: 63.029778760468844
2025-07-21 21:20:03,283 - INFO -    MACD: 2.4701537500686754
2025-07-21 21:20:03,283 - INFO -    BB_upper: N/A
2025-07-21 21:20:03,283 - INFO -    SMA_20: 208.03300018310546
2025-07-21 21:20:03,283 - INFO -    Volume_SMA: N/A
2025-07-21 21:20:03,283 - INFO - 
🎯 Testing signal generation methods...
2025-07-21 21:20:03,284 - INFO - ✅ Momentum signals: 0
2025-07-21 21:20:03,284 - INFO - ✅ Breakout signals: 0
2025-07-21 21:20:03,284 - INFO - ✅ Reversal signals: 0
2025-07-21 21:20:03,284 - INFO - 
📊 Total basic signals generated: 0
2025-07-21 21:20:03,285 - INFO - 
🔍 DEBUGGING ENHANCED SIGNAL GENERATION FOR AAPL
2025-07-21 21:20:03,285 - INFO - ==================================================
2025-07-21 21:20:03,285 - DEBUG - Entering history()
2025-07-21 21:20:03,286 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-07-21 21:20:03,286 - DEBUG -  Entering history()
2025-07-21 21:20:03,286 - DEBUG - AAPL: Yahoo GET parameters: {'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,286 - DEBUG -   Entering get()
2025-07-21 21:20:03,286 - DEBUG -    Entering _make_request()
2025-07-21 21:20:03,287 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-07-21 21:20:03,287 - DEBUG - params={'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,287 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,287 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,287 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,287 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:03,287 - DEBUG - reusing cookie
2025-07-21 21:20:03,287 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:03,287 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:03,288 - DEBUG - reusing crumb
2025-07-21 21:20:03,288 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:03,288 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,288 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,341 - DEBUG - response code=200
2025-07-21 21:20:03,341 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:03,341 - DEBUG -   Exiting get()
2025-07-21 21:20:03,343 - DEBUG - AAPL: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:03,351 - DEBUG - AAPL: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:03,353 - DEBUG - AAPL: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:03,360 - DEBUG - AAPL: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:03,360 - DEBUG -  Exiting history()
2025-07-21 21:20:03,361 - DEBUG - Exiting history()
2025-07-21 21:20:03,361 - DEBUG - Entering history()
2025-07-21 21:20:03,361 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-07-21 21:20:03,362 - DEBUG -  Entering history()
2025-07-21 21:20:03,362 - DEBUG - AAPL: Yahoo GET parameters: {'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,362 - DEBUG -   Entering get()
2025-07-21 21:20:03,362 - DEBUG -    Entering _make_request()
2025-07-21 21:20:03,362 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-07-21 21:20:03,362 - DEBUG - params={'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,362 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,363 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,363 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,363 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:03,363 - DEBUG - reusing cookie
2025-07-21 21:20:03,363 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:03,363 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:03,363 - DEBUG - reusing crumb
2025-07-21 21:20:03,363 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:03,363 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,363 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,413 - DEBUG - response code=200
2025-07-21 21:20:03,413 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:03,413 - DEBUG -   Exiting get()
2025-07-21 21:20:03,414 - DEBUG - AAPL: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:03,422 - DEBUG - AAPL: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:03,425 - DEBUG - AAPL: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:03,431 - DEBUG - AAPL: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:03,432 - DEBUG -  Exiting history()
2025-07-21 21:20:03,432 - DEBUG - Exiting history()
2025-07-21 21:20:03,432 - DEBUG - Entering history()
2025-07-21 21:20:03,432 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-07-21 21:20:03,433 - DEBUG -  Entering history()
2025-07-21 21:20:03,433 - DEBUG - AAPL: Yahoo GET parameters: {'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,433 - DEBUG -   Entering get()
2025-07-21 21:20:03,433 - DEBUG -    Entering _make_request()
2025-07-21 21:20:03,433 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-07-21 21:20:03,433 - DEBUG - params={'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,433 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,434 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,434 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,434 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:03,434 - DEBUG - reusing cookie
2025-07-21 21:20:03,434 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:03,434 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:03,434 - DEBUG - reusing crumb
2025-07-21 21:20:03,434 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:03,434 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,434 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,479 - DEBUG - response code=200
2025-07-21 21:20:03,479 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:03,479 - DEBUG -   Exiting get()
2025-07-21 21:20:03,480 - DEBUG - AAPL: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:03,488 - DEBUG - AAPL: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:03,490 - DEBUG - AAPL: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:03,497 - DEBUG - AAPL: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:03,497 - DEBUG -  Exiting history()
2025-07-21 21:20:03,497 - DEBUG - Exiting history()
2025-07-21 21:20:03,497 - DEBUG - Entering history()
2025-07-21 21:20:03,498 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-07-21 21:20:03,498 - DEBUG -  Entering history()
2025-07-21 21:20:03,498 - DEBUG - AAPL: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,498 - DEBUG -   Entering get()
2025-07-21 21:20:03,498 - DEBUG -    Entering _make_request()
2025-07-21 21:20:03,498 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-07-21 21:20:03,498 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,498 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,499 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,499 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,499 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:03,499 - DEBUG - reusing cookie
2025-07-21 21:20:03,499 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:03,499 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:03,499 - DEBUG - reusing crumb
2025-07-21 21:20:03,499 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:03,499 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,499 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,507 - DEBUG - response code=200
2025-07-21 21:20:03,507 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:03,507 - DEBUG -   Exiting get()
2025-07-21 21:20:03,509 - DEBUG - AAPL: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:03,510 - DEBUG - AAPL: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:03,516 - DEBUG - AAPL: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:03,522 - DEBUG - AAPL: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:03,523 - DEBUG -  Exiting history()
2025-07-21 21:20:03,523 - DEBUG - Exiting history()
2025-07-21 21:20:03,523 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/AAPL
2025-07-21 21:20:03,523 - DEBUG - Entering get()
2025-07-21 21:20:03,523 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,523 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/AAPL
2025-07-21 21:20:03,524 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'AAPL'}
2025-07-21 21:20:03,524 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,524 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,524 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,524 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,524 - DEBUG - reusing cookie
2025-07-21 21:20:03,524 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,524 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,524 - DEBUG - reusing crumb
2025-07-21 21:20:03,524 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,525 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,525 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,533 - DEBUG - response code=200
2025-07-21 21:20:03,533 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,534 - DEBUG - Exiting get()
2025-07-21 21:20:03,534 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:03,534 - DEBUG - Entering get()
2025-07-21 21:20:03,534 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,534 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:03,534 - DEBUG - params={'symbols': 'AAPL', 'formatted': 'false'}
2025-07-21 21:20:03,534 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,535 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,535 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,535 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,535 - DEBUG - reusing cookie
2025-07-21 21:20:03,535 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,535 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,535 - DEBUG - reusing crumb
2025-07-21 21:20:03,535 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,535 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,535 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,543 - DEBUG - response code=200
2025-07-21 21:20:03,543 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,543 - DEBUG - Exiting get()
2025-07-21 21:20:03,547 - DEBUG - Entering get()
2025-07-21 21:20:03,548 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,548 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/AAPL
2025-07-21 21:20:03,548 - DEBUG - params=None
2025-07-21 21:20:03,548 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,548 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,548 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,548 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,548 - DEBUG - reusing cookie
2025-07-21 21:20:03,548 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,549 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,549 - DEBUG - reusing crumb
2025-07-21 21:20:03,549 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,549 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,549 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,558 - DEBUG - response code=200
2025-07-21 21:20:03,558 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,558 - DEBUG - Exiting get()
2025-07-21 21:20:03,559 - DEBUG - Entering get()
2025-07-21 21:20:03,560 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,560 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/AAPL?date=1753401600
2025-07-21 21:20:03,560 - DEBUG - params=None
2025-07-21 21:20:03,560 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,560 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,560 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,560 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,560 - DEBUG - reusing cookie
2025-07-21 21:20:03,560 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,560 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,561 - DEBUG - reusing crumb
2025-07-21 21:20:03,561 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,561 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,561 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,568 - DEBUG - response code=200
2025-07-21 21:20:03,568 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,568 - DEBUG - Exiting get()
2025-07-21 21:20:03,634 - DEBUG - Starting new HTTPS connection (1): www.alphavantage.co:443
2025-07-21 21:20:03,682 - DEBUG - https://www.alphavantage.co:443 "GET /query?function=EARNINGS_CALENDAR&horizon=3month&apikey=your_alpha_vantage_key_here HTTP/1.1" 200 72
2025-07-21 21:20:03,684 - DEBUG - Encoding detection: ascii is most likely the one.
2025-07-21 21:20:03,685 - INFO - ✅ Enhanced signals generated: 0
2025-07-21 21:20:03,685 - INFO - 
============================================================
2025-07-21 21:20:03,685 - INFO - DEBUGGING TSLA
2025-07-21 21:20:03,685 - INFO - ============================================================
2025-07-21 21:20:03,685 - INFO - 
🔍 DEBUGGING BASIC SIGNAL GENERATION FOR TSLA
2025-07-21 21:20:03,686 - INFO - ==================================================
2025-07-21 21:20:03,686 - INFO - 
🔍 DEBUGGING MARKET DATA FOR TSLA
2025-07-21 21:20:03,686 - INFO - ==================================================
2025-07-21 21:20:03,686 - DEBUG - Entering history()
2025-07-21 21:20:03,687 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['TSLA', 1, 0])
2025-07-21 21:20:03,687 - DEBUG -  Entering history()
2025-07-21 21:20:03,687 - DEBUG - TSLA: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,687 - DEBUG -   Entering get()
2025-07-21 21:20:03,687 - DEBUG -    Entering _make_request()
2025-07-21 21:20:03,687 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/TSLA
2025-07-21 21:20:03,688 - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,688 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,688 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,688 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,688 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:03,688 - DEBUG - reusing cookie
2025-07-21 21:20:03,688 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:03,688 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:03,688 - DEBUG - reusing crumb
2025-07-21 21:20:03,688 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:03,689 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,689 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,729 - DEBUG - response code=200
2025-07-21 21:20:03,729 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:03,729 - DEBUG -   Exiting get()
2025-07-21 21:20:03,730 - DEBUG - TSLA: yfinance received OHLC data: 2025-06-23 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:03,732 - DEBUG - TSLA: OHLC after cleaning: 2025-06-23 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:03,735 - DEBUG - TSLA: OHLC after combining events: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:03,743 - DEBUG - TSLA: yfinance returning OHLC: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:03,743 - DEBUG -  Exiting history()
2025-07-21 21:20:03,744 - DEBUG - Exiting history()
2025-07-21 21:20:03,744 - INFO - ✅ Basic stock data: (20, 7)
2025-07-21 21:20:03,744 - INFO -    Date range: 2025-06-23 00:00:00-04:00 to 2025-07-21 00:00:00-04:00
2025-07-21 21:20:03,744 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-07-21 21:20:03,744 - INFO -    Last close: $328.49
2025-07-21 21:20:03,745 - INFO -    Volume: 73,624,128
2025-07-21 21:20:03,745 - DEBUG - Entering history()
2025-07-21 21:20:03,745 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['TSLA', 1, 0])
2025-07-21 21:20:03,745 - DEBUG -  Entering history()
2025-07-21 21:20:03,746 - DEBUG - TSLA: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,746 - DEBUG -   Entering get()
2025-07-21 21:20:03,746 - DEBUG -    Entering _make_request()
2025-07-21 21:20:03,746 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/TSLA
2025-07-21 21:20:03,746 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:03,746 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,746 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,746 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,747 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:03,747 - DEBUG - reusing cookie
2025-07-21 21:20:03,747 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:03,747 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:03,747 - DEBUG - reusing crumb
2025-07-21 21:20:03,747 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:03,747 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,747 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,782 - DEBUG - response code=200
2025-07-21 21:20:03,783 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:03,783 - DEBUG -   Exiting get()
2025-07-21 21:20:03,784 - DEBUG - TSLA: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:03,785 - DEBUG - TSLA: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:03,789 - DEBUG - TSLA: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:03,795 - DEBUG - TSLA: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:03,795 - DEBUG -  Exiting history()
2025-07-21 21:20:03,795 - DEBUG - Exiting history()
2025-07-21 21:20:03,796 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/TSLA
2025-07-21 21:20:03,796 - DEBUG - Entering get()
2025-07-21 21:20:03,796 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,796 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/TSLA
2025-07-21 21:20:03,796 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'TSLA'}
2025-07-21 21:20:03,796 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,796 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,797 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,797 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,797 - DEBUG - reusing cookie
2025-07-21 21:20:03,797 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,797 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,797 - DEBUG - reusing crumb
2025-07-21 21:20:03,797 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,797 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,797 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,855 - DEBUG - response code=200
2025-07-21 21:20:03,855 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,855 - DEBUG - Exiting get()
2025-07-21 21:20:03,855 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:03,856 - DEBUG - Entering get()
2025-07-21 21:20:03,856 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,856 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:03,856 - DEBUG - params={'symbols': 'TSLA', 'formatted': 'false'}
2025-07-21 21:20:03,856 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,856 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,856 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,856 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,856 - DEBUG - reusing cookie
2025-07-21 21:20:03,857 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,857 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,857 - DEBUG - reusing crumb
2025-07-21 21:20:03,860 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,860 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,860 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,924 - DEBUG - response code=200
2025-07-21 21:20:03,924 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,924 - DEBUG - Exiting get()
2025-07-21 21:20:03,926 - DEBUG - Entering get()
2025-07-21 21:20:03,926 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,926 - DEBUG - url=https://query1.finance.yahoo.com/ws/fundamentals-timeseries/v1/finance/timeseries/TSLA?symbol=TSLA&type=trailingPegRatio&period1=1737417600&period2=1753228800
2025-07-21 21:20:03,927 - DEBUG - params=None
2025-07-21 21:20:03,927 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,927 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,927 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,927 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,928 - DEBUG - reusing cookie
2025-07-21 21:20:03,928 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,928 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,928 - DEBUG - reusing crumb
2025-07-21 21:20:03,928 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,929 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,929 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:03,976 - DEBUG - response code=200
2025-07-21 21:20:03,976 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:03,977 - DEBUG - Exiting get()
2025-07-21 21:20:03,983 - DEBUG - Entering get()
2025-07-21 21:20:03,983 - DEBUG -  Entering _make_request()
2025-07-21 21:20:03,983 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/TSLA
2025-07-21 21:20:03,983 - DEBUG - params=None
2025-07-21 21:20:03,983 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:03,983 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:03,984 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,984 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:03,984 - DEBUG - reusing cookie
2025-07-21 21:20:03,984 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:03,984 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:03,984 - DEBUG - reusing crumb
2025-07-21 21:20:03,984 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:03,984 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:03,984 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,052 - DEBUG - response code=200
2025-07-21 21:20:04,052 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,053 - DEBUG - Exiting get()
2025-07-21 21:20:04,055 - DEBUG - Entering get()
2025-07-21 21:20:04,055 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,055 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/TSLA?date=1753401600
2025-07-21 21:20:04,055 - DEBUG - params=None
2025-07-21 21:20:04,055 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,055 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,055 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,056 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,056 - DEBUG - reusing cookie
2025-07-21 21:20:04,056 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,056 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,056 - DEBUG - reusing crumb
2025-07-21 21:20:04,056 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,056 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,056 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,124 - DEBUG - response code=200
2025-07-21 21:20:04,125 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,125 - DEBUG - Exiting get()
2025-07-21 21:20:04,141 - INFO - ✅ Comprehensive data keys: ['symbol', 'timestamp', 'price_data', 'volume_analysis', 'options_flow', 'historical_data', 'data_quality']
2025-07-21 21:20:04,141 - INFO -    Historical data length: 50
2025-07-21 21:20:04,142 - INFO -    Sample data point: {'Open': 279.6300048828125, 'High': 289.79998779296875, 'Low': 279.4100036621094, 'Close': 284.82000732421875, 'Volume': 97539400, 'Dividends': 0.0, 'Stock Splits': 0.0}
2025-07-21 21:20:04,142 - INFO -    Data quality score: 100
2025-07-21 21:20:04,142 - INFO -    Data issues: []
2025-07-21 21:20:04,142 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/TSLA
2025-07-21 21:20:04,142 - DEBUG - Entering get()
2025-07-21 21:20:04,142 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,142 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/TSLA
2025-07-21 21:20:04,142 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'TSLA'}
2025-07-21 21:20:04,143 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,143 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,143 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,143 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,143 - DEBUG - reusing cookie
2025-07-21 21:20:04,143 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,143 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,143 - DEBUG - reusing crumb
2025-07-21 21:20:04,143 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,144 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,144 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,151 - DEBUG - response code=200
2025-07-21 21:20:04,151 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,152 - DEBUG - Exiting get()
2025-07-21 21:20:04,152 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:04,152 - DEBUG - Entering get()
2025-07-21 21:20:04,152 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,152 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:04,152 - DEBUG - params={'symbols': 'TSLA', 'formatted': 'false'}
2025-07-21 21:20:04,152 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,152 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,153 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,153 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,153 - DEBUG - reusing cookie
2025-07-21 21:20:04,153 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,153 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,153 - DEBUG - reusing crumb
2025-07-21 21:20:04,153 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,153 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,153 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,161 - DEBUG - response code=200
2025-07-21 21:20:04,162 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,162 - DEBUG - Exiting get()
2025-07-21 21:20:04,163 - INFO - ✅ Real-time data: True
2025-07-21 21:20:04,163 - INFO -    Current price: $328.49
2025-07-21 21:20:04,163 - INFO -    Volume: 73,624,128
2025-07-21 21:20:04,163 - INFO -    Beta: 2.461
2025-07-21 21:20:04,164 - INFO - ✅ Historical DataFrame: (50, 7)
2025-07-21 21:20:04,164 - INFO - 
🔍 DEBUGGING TECHNICAL INDICATORS FOR TSLA
2025-07-21 21:20:04,164 - INFO - ==================================================
2025-07-21 21:20:04,213 - INFO - ✅ Technical indicators shape: (50, 66)
2025-07-21 21:20:04,213 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits', 'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'SMA_200', 'EMA_5', 'EMA_10', 'EMA_20', 'EMA_50', 'SMA_Cross_5_20', 'SMA_Cross_20_50', 'EMA_Cross_5_20', 'RSI', 'RSI_Oversold', 'RSI_Overbought', 'MACD', 'MACD_Signal', 'MACD_Histogram', 'MACD_Bullish', 'Stoch_K', 'Stoch_D', 'Williams_R', 'ROC', 'BB_Upper', 'BB_Middle', 'BB_Lower', 'BB_Width', 'BB_Position', 'ATR', 'ATR_Percent', 'KC_Upper', 'KC_Middle', 'KC_Lower', 'DC_Upper', 'DC_Lower', 'DC_Middle', 'Volume_SMA_20', 'Volume_Ratio', 'High_Volume', 'OBV', 'VPT', 'ADL', 'CMF', 'ADX', 'ADX_Strong_Trend', 'PSAR', 'CCI', 'DPO', 'Price_Change_1D', 'Price_Change_5D', 'Price_Change_20D', 'ATR_Expansion', 'Vol_Expansion', 'Breakout_High', 'Breakout_Low', 'Resistance_20', 'Support_20', 'Near_Resistance', 'Near_Support']
2025-07-21 21:20:04,213 - INFO -    RSI: 55.22866570588155
2025-07-21 21:20:04,213 - INFO -    MACD: 0.25379857560125174
2025-07-21 21:20:04,214 - INFO -    BB_upper: N/A
2025-07-21 21:20:04,214 - INFO -    SMA_20: 317.669499206543
2025-07-21 21:20:04,214 - INFO -    Volume_SMA: N/A
2025-07-21 21:20:04,214 - INFO - 
🎯 Testing signal generation methods...
2025-07-21 21:20:04,214 - INFO - ✅ Momentum signals: 0
2025-07-21 21:20:04,215 - INFO - ✅ Breakout signals: 0
2025-07-21 21:20:04,215 - INFO - ✅ Reversal signals: 0
2025-07-21 21:20:04,215 - INFO - 
📊 Total basic signals generated: 0
2025-07-21 21:20:04,215 - INFO - 
🔍 DEBUGGING ENHANCED SIGNAL GENERATION FOR TSLA
2025-07-21 21:20:04,216 - INFO - ==================================================
2025-07-21 21:20:04,216 - DEBUG - Entering history()
2025-07-21 21:20:04,216 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['TSLA', 1, 0])
2025-07-21 21:20:04,217 - DEBUG -  Entering history()
2025-07-21 21:20:04,217 - DEBUG - TSLA: Yahoo GET parameters: {'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,217 - DEBUG -   Entering get()
2025-07-21 21:20:04,217 - DEBUG -    Entering _make_request()
2025-07-21 21:20:04,217 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/TSLA
2025-07-21 21:20:04,217 - DEBUG - params={'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,217 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,217 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,218 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,218 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:04,218 - DEBUG - reusing cookie
2025-07-21 21:20:04,218 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:04,218 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:04,218 - DEBUG - reusing crumb
2025-07-21 21:20:04,218 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:04,218 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,218 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,281 - DEBUG - response code=200
2025-07-21 21:20:04,281 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:04,281 - DEBUG -   Exiting get()
2025-07-21 21:20:04,283 - DEBUG - TSLA: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:04,290 - DEBUG - TSLA: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:04,293 - DEBUG - TSLA: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:04,299 - DEBUG - TSLA: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:04,299 - DEBUG -  Exiting history()
2025-07-21 21:20:04,300 - DEBUG - Exiting history()
2025-07-21 21:20:04,300 - DEBUG - Entering history()
2025-07-21 21:20:04,300 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['TSLA', 1, 0])
2025-07-21 21:20:04,301 - DEBUG -  Entering history()
2025-07-21 21:20:04,301 - DEBUG - TSLA: Yahoo GET parameters: {'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,301 - DEBUG -   Entering get()
2025-07-21 21:20:04,301 - DEBUG -    Entering _make_request()
2025-07-21 21:20:04,301 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/TSLA
2025-07-21 21:20:04,301 - DEBUG - params={'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,301 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,301 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,301 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,302 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:04,302 - DEBUG - reusing cookie
2025-07-21 21:20:04,302 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:04,302 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:04,302 - DEBUG - reusing crumb
2025-07-21 21:20:04,302 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:04,302 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,302 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,347 - DEBUG - response code=200
2025-07-21 21:20:04,347 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:04,348 - DEBUG -   Exiting get()
2025-07-21 21:20:04,349 - DEBUG - TSLA: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:04,358 - DEBUG - TSLA: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:04,361 - DEBUG - TSLA: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:04,367 - DEBUG - TSLA: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:04,368 - DEBUG -  Exiting history()
2025-07-21 21:20:04,368 - DEBUG - Exiting history()
2025-07-21 21:20:04,368 - DEBUG - Entering history()
2025-07-21 21:20:04,369 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['TSLA', 1, 0])
2025-07-21 21:20:04,369 - DEBUG -  Entering history()
2025-07-21 21:20:04,369 - DEBUG - TSLA: Yahoo GET parameters: {'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,369 - DEBUG -   Entering get()
2025-07-21 21:20:04,369 - DEBUG -    Entering _make_request()
2025-07-21 21:20:04,370 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/TSLA
2025-07-21 21:20:04,370 - DEBUG - params={'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,370 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,370 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,370 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,370 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:04,370 - DEBUG - reusing cookie
2025-07-21 21:20:04,370 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:04,371 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:04,371 - DEBUG - reusing crumb
2025-07-21 21:20:04,371 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:04,371 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,371 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,424 - DEBUG - response code=200
2025-07-21 21:20:04,424 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:04,425 - DEBUG -   Exiting get()
2025-07-21 21:20:04,426 - DEBUG - TSLA: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:04,433 - DEBUG - TSLA: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:04,436 - DEBUG - TSLA: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:04,442 - DEBUG - TSLA: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:04,443 - DEBUG -  Exiting history()
2025-07-21 21:20:04,443 - DEBUG - Exiting history()
2025-07-21 21:20:04,443 - DEBUG - Entering history()
2025-07-21 21:20:04,444 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['TSLA', 1, 0])
2025-07-21 21:20:04,444 - DEBUG -  Entering history()
2025-07-21 21:20:04,444 - DEBUG - TSLA: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,444 - DEBUG -   Entering get()
2025-07-21 21:20:04,444 - DEBUG -    Entering _make_request()
2025-07-21 21:20:04,444 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/TSLA
2025-07-21 21:20:04,444 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,445 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,445 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,445 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,445 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:04,445 - DEBUG - reusing cookie
2025-07-21 21:20:04,445 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:04,445 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:04,445 - DEBUG - reusing crumb
2025-07-21 21:20:04,445 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:04,446 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,446 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,461 - DEBUG - response code=200
2025-07-21 21:20:04,464 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:04,465 - DEBUG -   Exiting get()
2025-07-21 21:20:04,466 - DEBUG - TSLA: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:04,467 - DEBUG - TSLA: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:04,470 - DEBUG - TSLA: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:04,478 - DEBUG - TSLA: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:04,478 - DEBUG -  Exiting history()
2025-07-21 21:20:04,478 - DEBUG - Exiting history()
2025-07-21 21:20:04,479 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/TSLA
2025-07-21 21:20:04,479 - DEBUG - Entering get()
2025-07-21 21:20:04,479 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,479 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/TSLA
2025-07-21 21:20:04,479 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'TSLA'}
2025-07-21 21:20:04,479 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,479 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,479 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,480 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,480 - DEBUG - reusing cookie
2025-07-21 21:20:04,480 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,480 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,480 - DEBUG - reusing crumb
2025-07-21 21:20:04,480 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,480 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,480 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,487 - DEBUG - response code=200
2025-07-21 21:20:04,488 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,488 - DEBUG - Exiting get()
2025-07-21 21:20:04,488 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:04,488 - DEBUG - Entering get()
2025-07-21 21:20:04,489 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,489 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:04,489 - DEBUG - params={'symbols': 'TSLA', 'formatted': 'false'}
2025-07-21 21:20:04,489 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,489 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,490 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,490 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,490 - DEBUG - reusing cookie
2025-07-21 21:20:04,490 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,490 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,490 - DEBUG - reusing crumb
2025-07-21 21:20:04,490 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,490 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,490 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,497 - DEBUG - response code=200
2025-07-21 21:20:04,497 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,498 - DEBUG - Exiting get()
2025-07-21 21:20:04,502 - DEBUG - Entering get()
2025-07-21 21:20:04,503 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,503 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/TSLA
2025-07-21 21:20:04,503 - DEBUG - params=None
2025-07-21 21:20:04,503 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,503 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,503 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,503 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,504 - DEBUG - reusing cookie
2025-07-21 21:20:04,504 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,504 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,504 - DEBUG - reusing crumb
2025-07-21 21:20:04,504 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,504 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,504 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,576 - DEBUG - response code=200
2025-07-21 21:20:04,576 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,576 - DEBUG - Exiting get()
2025-07-21 21:20:04,579 - DEBUG - Entering get()
2025-07-21 21:20:04,579 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,579 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/TSLA?date=1753401600
2025-07-21 21:20:04,579 - DEBUG - params=None
2025-07-21 21:20:04,579 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,579 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,579 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,579 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,579 - DEBUG - reusing cookie
2025-07-21 21:20:04,579 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,580 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,580 - DEBUG - reusing crumb
2025-07-21 21:20:04,580 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,580 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,580 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,589 - DEBUG - response code=200
2025-07-21 21:20:04,589 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,590 - DEBUG - Exiting get()
2025-07-21 21:20:04,658 - DEBUG - Starting new HTTPS connection (1): www.alphavantage.co:443
2025-07-21 21:20:04,698 - DEBUG - https://www.alphavantage.co:443 "GET /query?function=EARNINGS_CALENDAR&horizon=3month&apikey=your_alpha_vantage_key_here HTTP/1.1" 200 72
2025-07-21 21:20:04,698 - DEBUG - Encoding detection: ascii is most likely the one.
2025-07-21 21:20:04,699 - INFO - ✅ Enhanced signals generated: 0
2025-07-21 21:20:04,699 - INFO - 
============================================================
2025-07-21 21:20:04,699 - INFO - DEBUGGING MSFT
2025-07-21 21:20:04,699 - INFO - ============================================================
2025-07-21 21:20:04,700 - INFO - 
🔍 DEBUGGING BASIC SIGNAL GENERATION FOR MSFT
2025-07-21 21:20:04,700 - INFO - ==================================================
2025-07-21 21:20:04,700 - INFO - 
🔍 DEBUGGING MARKET DATA FOR MSFT
2025-07-21 21:20:04,700 - INFO - ==================================================
2025-07-21 21:20:04,700 - DEBUG - Entering history()
2025-07-21 21:20:04,701 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-07-21 21:20:04,702 - DEBUG -  Entering history()
2025-07-21 21:20:04,702 - DEBUG - MSFT: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,702 - DEBUG -   Entering get()
2025-07-21 21:20:04,702 - DEBUG -    Entering _make_request()
2025-07-21 21:20:04,702 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-07-21 21:20:04,702 - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,703 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,703 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,703 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,703 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:04,703 - DEBUG - reusing cookie
2025-07-21 21:20:04,703 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:04,704 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:04,704 - DEBUG - reusing crumb
2025-07-21 21:20:04,704 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:04,704 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,704 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,778 - DEBUG - response code=200
2025-07-21 21:20:04,778 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:04,778 - DEBUG -   Exiting get()
2025-07-21 21:20:04,779 - DEBUG - MSFT: yfinance received OHLC data: 2025-06-23 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:04,780 - DEBUG - MSFT: OHLC after cleaning: 2025-06-23 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:04,784 - DEBUG - MSFT: OHLC after combining events: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:04,794 - DEBUG - MSFT: yfinance returning OHLC: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:04,794 - DEBUG -  Exiting history()
2025-07-21 21:20:04,794 - DEBUG - Exiting history()
2025-07-21 21:20:04,794 - INFO - ✅ Basic stock data: (20, 7)
2025-07-21 21:20:04,794 - INFO -    Date range: 2025-06-23 00:00:00-04:00 to 2025-07-21 00:00:00-04:00
2025-07-21 21:20:04,795 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-07-21 21:20:04,795 - INFO -    Last close: $510.06
2025-07-21 21:20:04,795 - INFO -    Volume: 14,038,994
2025-07-21 21:20:04,795 - DEBUG - Entering history()
2025-07-21 21:20:04,796 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-07-21 21:20:04,796 - DEBUG -  Entering history()
2025-07-21 21:20:04,796 - DEBUG - MSFT: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,796 - DEBUG -   Entering get()
2025-07-21 21:20:04,796 - DEBUG -    Entering _make_request()
2025-07-21 21:20:04,796 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-07-21 21:20:04,796 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:04,796 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,796 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,797 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,797 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:04,797 - DEBUG - reusing cookie
2025-07-21 21:20:04,797 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:04,797 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:04,797 - DEBUG - reusing crumb
2025-07-21 21:20:04,797 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:04,797 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,797 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,870 - DEBUG - response code=200
2025-07-21 21:20:04,870 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:04,870 - DEBUG -   Exiting get()
2025-07-21 21:20:04,872 - DEBUG - MSFT: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:04,873 - DEBUG - MSFT: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:04,879 - DEBUG - MSFT: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:04,886 - DEBUG - MSFT: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:04,886 - DEBUG -  Exiting history()
2025-07-21 21:20:04,886 - DEBUG - Exiting history()
2025-07-21 21:20:04,886 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/MSFT
2025-07-21 21:20:04,886 - DEBUG - Entering get()
2025-07-21 21:20:04,887 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,887 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/MSFT
2025-07-21 21:20:04,887 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'MSFT'}
2025-07-21 21:20:04,887 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,887 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,887 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,887 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,887 - DEBUG - reusing cookie
2025-07-21 21:20:04,887 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,888 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,888 - DEBUG - reusing crumb
2025-07-21 21:20:04,888 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,888 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,888 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:04,954 - DEBUG - response code=200
2025-07-21 21:20:04,954 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:04,954 - DEBUG - Exiting get()
2025-07-21 21:20:04,954 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:04,955 - DEBUG - Entering get()
2025-07-21 21:20:04,955 - DEBUG -  Entering _make_request()
2025-07-21 21:20:04,955 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:04,955 - DEBUG - params={'symbols': 'MSFT', 'formatted': 'false'}
2025-07-21 21:20:04,955 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:04,955 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:04,955 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,955 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:04,955 - DEBUG - reusing cookie
2025-07-21 21:20:04,956 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:04,956 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:04,956 - DEBUG - reusing crumb
2025-07-21 21:20:04,956 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:04,956 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:04,956 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,006 - DEBUG - response code=200
2025-07-21 21:20:05,007 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,008 - DEBUG - Exiting get()
2025-07-21 21:20:05,009 - DEBUG - Entering get()
2025-07-21 21:20:05,009 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,009 - DEBUG - url=https://query1.finance.yahoo.com/ws/fundamentals-timeseries/v1/finance/timeseries/MSFT?symbol=MSFT&type=trailingPegRatio&period1=1737417600&period2=1753228800
2025-07-21 21:20:05,009 - DEBUG - params=None
2025-07-21 21:20:05,009 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,009 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,009 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,009 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,009 - DEBUG - reusing cookie
2025-07-21 21:20:05,009 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,009 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,009 - DEBUG - reusing crumb
2025-07-21 21:20:05,010 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,010 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,010 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,062 - DEBUG - response code=200
2025-07-21 21:20:05,064 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,065 - DEBUG - Exiting get()
2025-07-21 21:20:05,069 - DEBUG - Entering get()
2025-07-21 21:20:05,070 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,070 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/MSFT
2025-07-21 21:20:05,070 - DEBUG - params=None
2025-07-21 21:20:05,070 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,070 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,070 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,070 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,070 - DEBUG - reusing cookie
2025-07-21 21:20:05,070 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,071 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,071 - DEBUG - reusing crumb
2025-07-21 21:20:05,071 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,071 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,071 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,134 - DEBUG - response code=200
2025-07-21 21:20:05,134 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,134 - DEBUG - Exiting get()
2025-07-21 21:20:05,136 - DEBUG - Entering get()
2025-07-21 21:20:05,136 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,136 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/MSFT?date=1753401600
2025-07-21 21:20:05,136 - DEBUG - params=None
2025-07-21 21:20:05,136 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,136 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,136 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,136 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,137 - DEBUG - reusing cookie
2025-07-21 21:20:05,137 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,137 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,137 - DEBUG - reusing crumb
2025-07-21 21:20:05,137 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,137 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,137 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,198 - DEBUG - response code=200
2025-07-21 21:20:05,198 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,199 - DEBUG - Exiting get()
2025-07-21 21:20:05,218 - INFO - ✅ Comprehensive data keys: ['symbol', 'timestamp', 'price_data', 'volume_analysis', 'options_flow', 'historical_data', 'data_quality']
2025-07-21 21:20:05,219 - INFO -    Historical data length: 50
2025-07-21 21:20:05,219 - INFO -    Sample data point: {'Open': 437.12748451655574, 'High': 442.85698666150637, 'Low': 434.86165526525525, 'Close': 437.3670654296875, 'Volume': 23491300, 'Dividends': 0.0, 'Stock Splits': 0.0}
2025-07-21 21:20:05,219 - INFO -    Data quality score: 100
2025-07-21 21:20:05,219 - INFO -    Data issues: []
2025-07-21 21:20:05,219 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/MSFT
2025-07-21 21:20:05,219 - DEBUG - Entering get()
2025-07-21 21:20:05,219 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,220 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/MSFT
2025-07-21 21:20:05,220 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'MSFT'}
2025-07-21 21:20:05,220 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,220 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,220 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,220 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,220 - DEBUG - reusing cookie
2025-07-21 21:20:05,220 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,221 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,221 - DEBUG - reusing crumb
2025-07-21 21:20:05,221 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,221 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,221 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,229 - DEBUG - response code=200
2025-07-21 21:20:05,229 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,229 - DEBUG - Exiting get()
2025-07-21 21:20:05,230 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:05,230 - DEBUG - Entering get()
2025-07-21 21:20:05,230 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,230 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:05,230 - DEBUG - params={'symbols': 'MSFT', 'formatted': 'false'}
2025-07-21 21:20:05,230 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,230 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,230 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,231 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,231 - DEBUG - reusing cookie
2025-07-21 21:20:05,231 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,231 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,231 - DEBUG - reusing crumb
2025-07-21 21:20:05,231 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,231 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,231 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,285 - DEBUG - response code=200
2025-07-21 21:20:05,285 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,285 - DEBUG - Exiting get()
2025-07-21 21:20:05,287 - INFO - ✅ Real-time data: True
2025-07-21 21:20:05,287 - INFO -    Current price: $510.06
2025-07-21 21:20:05,287 - INFO -    Volume: 14,038,994
2025-07-21 21:20:05,287 - INFO -    Beta: 1.026
2025-07-21 21:20:05,288 - INFO - ✅ Historical DataFrame: (50, 7)
2025-07-21 21:20:05,288 - INFO - 
🔍 DEBUGGING TECHNICAL INDICATORS FOR MSFT
2025-07-21 21:20:05,288 - INFO - ==================================================
2025-07-21 21:20:05,337 - INFO - ✅ Technical indicators shape: (50, 66)
2025-07-21 21:20:05,337 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits', 'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'SMA_200', 'EMA_5', 'EMA_10', 'EMA_20', 'EMA_50', 'SMA_Cross_5_20', 'SMA_Cross_20_50', 'EMA_Cross_5_20', 'RSI', 'RSI_Oversold', 'RSI_Overbought', 'MACD', 'MACD_Signal', 'MACD_Histogram', 'MACD_Bullish', 'Stoch_K', 'Stoch_D', 'Williams_R', 'ROC', 'BB_Upper', 'BB_Middle', 'BB_Lower', 'BB_Width', 'BB_Position', 'ATR', 'ATR_Percent', 'KC_Upper', 'KC_Middle', 'KC_Lower', 'DC_Upper', 'DC_Lower', 'DC_Middle', 'Volume_SMA_20', 'Volume_Ratio', 'High_Volume', 'OBV', 'VPT', 'ADL', 'CMF', 'ADX', 'ADX_Strong_Trend', 'PSAR', 'CCI', 'DPO', 'Price_Change_1D', 'Price_Change_5D', 'Price_Change_20D', 'ATR_Expansion', 'Vol_Expansion', 'Breakout_High', 'Breakout_Low', 'Resistance_20', 'Support_20', 'Near_Resistance', 'Near_Support']
2025-07-21 21:20:05,338 - INFO -    RSI: 73.65677374933935
2025-07-21 21:20:05,338 - INFO -    MACD: 9.458973117955395
2025-07-21 21:20:05,338 - INFO -    BB_upper: N/A
2025-07-21 21:20:05,338 - INFO -    SMA_20: 499.5039993286133
2025-07-21 21:20:05,338 - INFO -    Volume_SMA: N/A
2025-07-21 21:20:05,339 - INFO - 
🎯 Testing signal generation methods...
2025-07-21 21:20:05,339 - INFO - ✅ Momentum signals: 0
2025-07-21 21:20:05,339 - INFO - ✅ Breakout signals: 0
2025-07-21 21:20:05,340 - INFO - ✅ Reversal signals: 0
2025-07-21 21:20:05,340 - INFO - 
📊 Total basic signals generated: 0
2025-07-21 21:20:05,341 - INFO - 
🔍 DEBUGGING ENHANCED SIGNAL GENERATION FOR MSFT
2025-07-21 21:20:05,341 - INFO - ==================================================
2025-07-21 21:20:05,341 - DEBUG - Entering history()
2025-07-21 21:20:05,342 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-07-21 21:20:05,342 - DEBUG -  Entering history()
2025-07-21 21:20:05,342 - DEBUG - MSFT: Yahoo GET parameters: {'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,342 - DEBUG -   Entering get()
2025-07-21 21:20:05,342 - DEBUG -    Entering _make_request()
2025-07-21 21:20:05,343 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-07-21 21:20:05,343 - DEBUG - params={'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,343 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,343 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,343 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,344 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:05,344 - DEBUG - reusing cookie
2025-07-21 21:20:05,344 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:05,344 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:05,344 - DEBUG - reusing crumb
2025-07-21 21:20:05,344 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:05,345 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,345 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,458 - DEBUG - response code=200
2025-07-21 21:20:05,459 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:05,459 - DEBUG -   Exiting get()
2025-07-21 21:20:05,460 - DEBUG - MSFT: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:05,468 - DEBUG - MSFT: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:05,471 - DEBUG - MSFT: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:05,477 - DEBUG - MSFT: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:05,478 - DEBUG -  Exiting history()
2025-07-21 21:20:05,478 - DEBUG - Exiting history()
2025-07-21 21:20:05,478 - DEBUG - Entering history()
2025-07-21 21:20:05,479 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-07-21 21:20:05,479 - DEBUG -  Entering history()
2025-07-21 21:20:05,479 - DEBUG - MSFT: Yahoo GET parameters: {'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,479 - DEBUG -   Entering get()
2025-07-21 21:20:05,479 - DEBUG -    Entering _make_request()
2025-07-21 21:20:05,479 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-07-21 21:20:05,479 - DEBUG - params={'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,480 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,480 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,480 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,480 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:05,480 - DEBUG - reusing cookie
2025-07-21 21:20:05,480 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:05,480 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:05,480 - DEBUG - reusing crumb
2025-07-21 21:20:05,480 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:05,480 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,481 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,545 - DEBUG - response code=200
2025-07-21 21:20:05,545 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:05,545 - DEBUG -   Exiting get()
2025-07-21 21:20:05,546 - DEBUG - MSFT: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:05,554 - DEBUG - MSFT: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:05,557 - DEBUG - MSFT: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:05,563 - DEBUG - MSFT: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:05,563 - DEBUG -  Exiting history()
2025-07-21 21:20:05,564 - DEBUG - Exiting history()
2025-07-21 21:20:05,564 - DEBUG - Entering history()
2025-07-21 21:20:05,564 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-07-21 21:20:05,565 - DEBUG -  Entering history()
2025-07-21 21:20:05,565 - DEBUG - MSFT: Yahoo GET parameters: {'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,565 - DEBUG -   Entering get()
2025-07-21 21:20:05,565 - DEBUG -    Entering _make_request()
2025-07-21 21:20:05,565 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-07-21 21:20:05,565 - DEBUG - params={'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,565 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,565 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,565 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,565 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:05,566 - DEBUG - reusing cookie
2025-07-21 21:20:05,566 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:05,566 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:05,566 - DEBUG - reusing crumb
2025-07-21 21:20:05,566 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:05,566 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,566 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,621 - DEBUG - response code=200
2025-07-21 21:20:05,621 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:05,621 - DEBUG -   Exiting get()
2025-07-21 21:20:05,622 - DEBUG - MSFT: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:05,630 - DEBUG - MSFT: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:05,633 - DEBUG - MSFT: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:05,639 - DEBUG - MSFT: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:05,640 - DEBUG -  Exiting history()
2025-07-21 21:20:05,640 - DEBUG - Exiting history()
2025-07-21 21:20:05,640 - DEBUG - Entering history()
2025-07-21 21:20:05,641 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-07-21 21:20:05,641 - DEBUG -  Entering history()
2025-07-21 21:20:05,641 - DEBUG - MSFT: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,641 - DEBUG -   Entering get()
2025-07-21 21:20:05,641 - DEBUG -    Entering _make_request()
2025-07-21 21:20:05,641 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-07-21 21:20:05,642 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,642 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,642 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,642 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,642 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:05,642 - DEBUG - reusing cookie
2025-07-21 21:20:05,642 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:05,642 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:05,642 - DEBUG - reusing crumb
2025-07-21 21:20:05,643 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:05,643 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,643 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,652 - DEBUG - response code=200
2025-07-21 21:20:05,652 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:05,652 - DEBUG -   Exiting get()
2025-07-21 21:20:05,654 - DEBUG - MSFT: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:05,655 - DEBUG - MSFT: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:05,661 - DEBUG - MSFT: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:05,667 - DEBUG - MSFT: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:05,668 - DEBUG -  Exiting history()
2025-07-21 21:20:05,668 - DEBUG - Exiting history()
2025-07-21 21:20:05,668 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/MSFT
2025-07-21 21:20:05,668 - DEBUG - Entering get()
2025-07-21 21:20:05,668 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,668 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/MSFT
2025-07-21 21:20:05,669 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'MSFT'}
2025-07-21 21:20:05,669 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,669 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,669 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,669 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,669 - DEBUG - reusing cookie
2025-07-21 21:20:05,669 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,669 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,669 - DEBUG - reusing crumb
2025-07-21 21:20:05,669 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,670 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,670 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,677 - DEBUG - response code=200
2025-07-21 21:20:05,677 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,678 - DEBUG - Exiting get()
2025-07-21 21:20:05,678 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:05,678 - DEBUG - Entering get()
2025-07-21 21:20:05,678 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,678 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:05,678 - DEBUG - params={'symbols': 'MSFT', 'formatted': 'false'}
2025-07-21 21:20:05,679 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,679 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,679 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,679 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,679 - DEBUG - reusing cookie
2025-07-21 21:20:05,679 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,679 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,679 - DEBUG - reusing crumb
2025-07-21 21:20:05,679 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,680 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,680 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,685 - DEBUG - response code=200
2025-07-21 21:20:05,685 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,686 - DEBUG - Exiting get()
2025-07-21 21:20:05,689 - DEBUG - Entering get()
2025-07-21 21:20:05,690 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,690 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/MSFT
2025-07-21 21:20:05,690 - DEBUG - params=None
2025-07-21 21:20:05,690 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,690 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,690 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,690 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,690 - DEBUG - reusing cookie
2025-07-21 21:20:05,690 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,691 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,691 - DEBUG - reusing crumb
2025-07-21 21:20:05,691 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,691 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,691 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,739 - DEBUG - response code=200
2025-07-21 21:20:05,739 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,739 - DEBUG - Exiting get()
2025-07-21 21:20:05,740 - DEBUG - Entering get()
2025-07-21 21:20:05,740 - DEBUG -  Entering _make_request()
2025-07-21 21:20:05,741 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/MSFT?date=1753401600
2025-07-21 21:20:05,741 - DEBUG - params=None
2025-07-21 21:20:05,741 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,741 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,741 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,741 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:05,741 - DEBUG - reusing cookie
2025-07-21 21:20:05,741 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:05,741 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:05,742 - DEBUG - reusing crumb
2025-07-21 21:20:05,742 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:05,742 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,742 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:05,834 - DEBUG - response code=200
2025-07-21 21:20:05,834 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:05,834 - DEBUG - Exiting get()
2025-07-21 21:20:05,929 - DEBUG - Starting new HTTPS connection (1): www.alphavantage.co:443
2025-07-21 21:20:05,970 - DEBUG - https://www.alphavantage.co:443 "GET /query?function=EARNINGS_CALENDAR&horizon=3month&apikey=your_alpha_vantage_key_here HTTP/1.1" 200 72
2025-07-21 21:20:05,971 - DEBUG - Encoding detection: ascii is most likely the one.
2025-07-21 21:20:05,971 - INFO - ✅ Enhanced signals generated: 0
2025-07-21 21:20:05,972 - INFO - 
============================================================
2025-07-21 21:20:05,972 - INFO - DEBUGGING NVDA
2025-07-21 21:20:05,972 - INFO - ============================================================
2025-07-21 21:20:05,972 - INFO - 
🔍 DEBUGGING BASIC SIGNAL GENERATION FOR NVDA
2025-07-21 21:20:05,972 - INFO - ==================================================
2025-07-21 21:20:05,973 - INFO - 
🔍 DEBUGGING MARKET DATA FOR NVDA
2025-07-21 21:20:05,973 - INFO - ==================================================
2025-07-21 21:20:05,973 - DEBUG - Entering history()
2025-07-21 21:20:05,974 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['NVDA', 1, 0])
2025-07-21 21:20:05,974 - DEBUG -  Entering history()
2025-07-21 21:20:05,974 - DEBUG - NVDA: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,975 - DEBUG -   Entering get()
2025-07-21 21:20:05,975 - DEBUG -    Entering _make_request()
2025-07-21 21:20:05,975 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/NVDA
2025-07-21 21:20:05,975 - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:05,976 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:05,976 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:05,976 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,976 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:05,976 - DEBUG - reusing cookie
2025-07-21 21:20:05,977 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:05,977 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:05,977 - DEBUG - reusing crumb
2025-07-21 21:20:05,977 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:05,977 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:05,978 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,056 - DEBUG - response code=200
2025-07-21 21:20:06,056 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:06,056 - DEBUG -   Exiting get()
2025-07-21 21:20:06,058 - DEBUG - NVDA: yfinance received OHLC data: 2025-06-23 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:06,059 - DEBUG - NVDA: OHLC after cleaning: 2025-06-23 09:30:00-04:00 -> 2025-07-21 16:00:00-04:00
2025-07-21 21:20:06,069 - DEBUG - NVDA: OHLC after combining events: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:06,080 - DEBUG - NVDA: yfinance returning OHLC: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:06,081 - DEBUG -  Exiting history()
2025-07-21 21:20:06,081 - DEBUG - Exiting history()
2025-07-21 21:20:06,081 - INFO - ✅ Basic stock data: (20, 7)
2025-07-21 21:20:06,081 - INFO -    Date range: 2025-06-23 00:00:00-04:00 to 2025-07-21 00:00:00-04:00
2025-07-21 21:20:06,082 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-07-21 21:20:06,082 - INFO -    Last close: $171.38
2025-07-21 21:20:06,082 - INFO -    Volume: 118,678,641
2025-07-21 21:20:06,082 - DEBUG - Entering history()
2025-07-21 21:20:06,083 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['NVDA', 1, 0])
2025-07-21 21:20:06,083 - DEBUG -  Entering history()
2025-07-21 21:20:06,083 - DEBUG - NVDA: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,083 - DEBUG -   Entering get()
2025-07-21 21:20:06,083 - DEBUG -    Entering _make_request()
2025-07-21 21:20:06,083 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/NVDA
2025-07-21 21:20:06,083 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,084 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,084 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,084 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,084 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:06,084 - DEBUG - reusing cookie
2025-07-21 21:20:06,084 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:06,084 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:06,084 - DEBUG - reusing crumb
2025-07-21 21:20:06,084 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:06,084 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,085 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,156 - DEBUG - response code=200
2025-07-21 21:20:06,156 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:06,156 - DEBUG -   Exiting get()
2025-07-21 21:20:06,158 - DEBUG - NVDA: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:06,159 - DEBUG - NVDA: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:00-04:00
2025-07-21 21:20:06,166 - DEBUG - NVDA: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:06,173 - DEBUG - NVDA: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:06,173 - DEBUG -  Exiting history()
2025-07-21 21:20:06,173 - DEBUG - Exiting history()
2025-07-21 21:20:06,173 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/NVDA
2025-07-21 21:20:06,173 - DEBUG - Entering get()
2025-07-21 21:20:06,174 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,174 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/NVDA
2025-07-21 21:20:06,174 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'NVDA'}
2025-07-21 21:20:06,174 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,174 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,174 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,174 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,174 - DEBUG - reusing cookie
2025-07-21 21:20:06,175 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,175 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,175 - DEBUG - reusing crumb
2025-07-21 21:20:06,175 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,175 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,175 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,228 - DEBUG - response code=200
2025-07-21 21:20:06,229 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,229 - DEBUG - Exiting get()
2025-07-21 21:20:06,229 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:06,229 - DEBUG - Entering get()
2025-07-21 21:20:06,229 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,230 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:06,230 - DEBUG - params={'symbols': 'NVDA', 'formatted': 'false'}
2025-07-21 21:20:06,230 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,230 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,230 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,231 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,231 - DEBUG - reusing cookie
2025-07-21 21:20:06,231 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,231 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,231 - DEBUG - reusing crumb
2025-07-21 21:20:06,232 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,232 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,232 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,281 - DEBUG - response code=200
2025-07-21 21:20:06,281 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,281 - DEBUG - Exiting get()
2025-07-21 21:20:06,282 - DEBUG - Entering get()
2025-07-21 21:20:06,282 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,283 - DEBUG - url=https://query1.finance.yahoo.com/ws/fundamentals-timeseries/v1/finance/timeseries/NVDA?symbol=NVDA&type=trailingPegRatio&period1=1737417600&period2=1753228800
2025-07-21 21:20:06,283 - DEBUG - params=None
2025-07-21 21:20:06,283 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,283 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,284 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,284 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,284 - DEBUG - reusing cookie
2025-07-21 21:20:06,284 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,284 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,284 - DEBUG - reusing crumb
2025-07-21 21:20:06,285 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,285 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,285 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,341 - DEBUG - response code=200
2025-07-21 21:20:06,342 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,342 - DEBUG - Exiting get()
2025-07-21 21:20:06,345 - DEBUG - Entering get()
2025-07-21 21:20:06,346 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,346 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/NVDA
2025-07-21 21:20:06,346 - DEBUG - params=None
2025-07-21 21:20:06,346 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,346 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,346 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,347 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,347 - DEBUG - reusing cookie
2025-07-21 21:20:06,347 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,347 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,347 - DEBUG - reusing crumb
2025-07-21 21:20:06,347 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,348 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,348 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,421 - DEBUG - response code=200
2025-07-21 21:20:06,422 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,422 - DEBUG - Exiting get()
2025-07-21 21:20:06,423 - DEBUG - Entering get()
2025-07-21 21:20:06,424 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,424 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/NVDA?date=1753401600
2025-07-21 21:20:06,424 - DEBUG - params=None
2025-07-21 21:20:06,424 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,424 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,425 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,425 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,425 - DEBUG - reusing cookie
2025-07-21 21:20:06,425 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,425 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,425 - DEBUG - reusing crumb
2025-07-21 21:20:06,426 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,426 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,426 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,488 - DEBUG - response code=200
2025-07-21 21:20:06,488 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,489 - DEBUG - Exiting get()
2025-07-21 21:20:06,506 - INFO - ✅ Comprehensive data keys: ['symbol', 'timestamp', 'price_data', 'volume_analysis', 'options_flow', 'historical_data', 'data_quality']
2025-07-21 21:20:06,507 - INFO -    Historical data length: 50
2025-07-21 21:20:06,507 - INFO -    Sample data point: {'Open': 118.24179071447391, 'High': 118.67176116768113, 'Low': 115.84195580422112, 'Close': 117.36185455322266, 'Volume': 198428100, 'Dividends': 0.0, 'Stock Splits': 0.0}
2025-07-21 21:20:06,507 - INFO -    Data quality score: 100
2025-07-21 21:20:06,507 - INFO -    Data issues: []
2025-07-21 21:20:06,507 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/NVDA
2025-07-21 21:20:06,508 - DEBUG - Entering get()
2025-07-21 21:20:06,508 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,508 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/NVDA
2025-07-21 21:20:06,508 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'NVDA'}
2025-07-21 21:20:06,508 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,508 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,508 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,508 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,508 - DEBUG - reusing cookie
2025-07-21 21:20:06,508 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,509 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,509 - DEBUG - reusing crumb
2025-07-21 21:20:06,509 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,509 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,509 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,518 - DEBUG - response code=200
2025-07-21 21:20:06,519 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,519 - DEBUG - Exiting get()
2025-07-21 21:20:06,520 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:06,521 - DEBUG - Entering get()
2025-07-21 21:20:06,521 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,522 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:06,522 - DEBUG - params={'symbols': 'NVDA', 'formatted': 'false'}
2025-07-21 21:20:06,522 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,522 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,522 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,522 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,522 - DEBUG - reusing cookie
2025-07-21 21:20:06,522 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,522 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,523 - DEBUG - reusing crumb
2025-07-21 21:20:06,523 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,523 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,523 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,532 - DEBUG - response code=200
2025-07-21 21:20:06,532 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,532 - DEBUG - Exiting get()
2025-07-21 21:20:06,533 - INFO - ✅ Real-time data: True
2025-07-21 21:20:06,534 - INFO -    Current price: $171.38
2025-07-21 21:20:06,534 - INFO -    Volume: 118,678,641
2025-07-21 21:20:06,534 - INFO -    Beta: 2.122
2025-07-21 21:20:06,535 - INFO - ✅ Historical DataFrame: (50, 7)
2025-07-21 21:20:06,535 - INFO - 
🔍 DEBUGGING TECHNICAL INDICATORS FOR NVDA
2025-07-21 21:20:06,535 - INFO - ==================================================
2025-07-21 21:20:06,587 - INFO - ✅ Technical indicators shape: (50, 66)
2025-07-21 21:20:06,587 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits', 'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'SMA_200', 'EMA_5', 'EMA_10', 'EMA_20', 'EMA_50', 'SMA_Cross_5_20', 'SMA_Cross_20_50', 'EMA_Cross_5_20', 'RSI', 'RSI_Oversold', 'RSI_Overbought', 'MACD', 'MACD_Signal', 'MACD_Histogram', 'MACD_Bullish', 'Stoch_K', 'Stoch_D', 'Williams_R', 'ROC', 'BB_Upper', 'BB_Middle', 'BB_Lower', 'BB_Width', 'BB_Position', 'ATR', 'ATR_Percent', 'KC_Upper', 'KC_Middle', 'KC_Lower', 'DC_Upper', 'DC_Lower', 'DC_Middle', 'Volume_SMA_20', 'Volume_Ratio', 'High_Volume', 'OBV', 'VPT', 'ADL', 'CMF', 'ADX', 'ADX_Strong_Trend', 'PSAR', 'CCI', 'DPO', 'Price_Change_1D', 'Price_Change_5D', 'Price_Change_20D', 'ATR_Expansion', 'Vol_Expansion', 'Breakout_High', 'Breakout_Low', 'Resistance_20', 'Support_20', 'Near_Resistance', 'Near_Support']
2025-07-21 21:20:06,587 - INFO -    RSI: 76.0680658259154
2025-07-21 21:20:06,588 - INFO -    MACD: 7.7158237704234125
2025-07-21 21:20:06,588 - INFO -    BB_upper: N/A
2025-07-21 21:20:06,588 - INFO -    SMA_20: 161.00500106811523
2025-07-21 21:20:06,588 - INFO -    Volume_SMA: N/A
2025-07-21 21:20:06,588 - INFO - 
🎯 Testing signal generation methods...
2025-07-21 21:20:06,588 - INFO - ✅ Momentum signals: 0
2025-07-21 21:20:06,589 - INFO - ✅ Breakout signals: 0
2025-07-21 21:20:06,589 - INFO - ✅ Reversal signals: 0
2025-07-21 21:20:06,589 - INFO - 
📊 Total basic signals generated: 0
2025-07-21 21:20:06,589 - INFO - 
🔍 DEBUGGING ENHANCED SIGNAL GENERATION FOR NVDA
2025-07-21 21:20:06,589 - INFO - ==================================================
2025-07-21 21:20:06,590 - DEBUG - Entering history()
2025-07-21 21:20:06,590 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['NVDA', 1, 0])
2025-07-21 21:20:06,590 - DEBUG -  Entering history()
2025-07-21 21:20:06,591 - DEBUG - NVDA: Yahoo GET parameters: {'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,591 - DEBUG -   Entering get()
2025-07-21 21:20:06,591 - DEBUG -    Entering _make_request()
2025-07-21 21:20:06,591 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/NVDA
2025-07-21 21:20:06,591 - DEBUG - params={'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,591 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,591 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,591 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,592 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:06,592 - DEBUG - reusing cookie
2025-07-21 21:20:06,592 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:06,592 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:06,592 - DEBUG - reusing crumb
2025-07-21 21:20:06,593 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:06,593 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,593 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,682 - DEBUG - response code=200
2025-07-21 21:20:06,682 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:06,682 - DEBUG -   Exiting get()
2025-07-21 21:20:06,684 - DEBUG - NVDA: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:06,691 - DEBUG - NVDA: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:06,694 - DEBUG - NVDA: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:06,701 - DEBUG - NVDA: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:06,701 - DEBUG -  Exiting history()
2025-07-21 21:20:06,701 - DEBUG - Exiting history()
2025-07-21 21:20:06,701 - DEBUG - Entering history()
2025-07-21 21:20:06,702 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['NVDA', 1, 0])
2025-07-21 21:20:06,702 - DEBUG -  Entering history()
2025-07-21 21:20:06,702 - DEBUG - NVDA: Yahoo GET parameters: {'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,702 - DEBUG -   Entering get()
2025-07-21 21:20:06,702 - DEBUG -    Entering _make_request()
2025-07-21 21:20:06,703 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/NVDA
2025-07-21 21:20:06,703 - DEBUG - params={'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,703 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,703 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,703 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,703 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:06,703 - DEBUG - reusing cookie
2025-07-21 21:20:06,703 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:06,703 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:06,704 - DEBUG - reusing crumb
2025-07-21 21:20:06,704 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:06,704 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,704 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,786 - DEBUG - response code=200
2025-07-21 21:20:06,786 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:06,786 - DEBUG -   Exiting get()
2025-07-21 21:20:06,787 - DEBUG - NVDA: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:06,795 - DEBUG - NVDA: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:06,798 - DEBUG - NVDA: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:06,805 - DEBUG - NVDA: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:06,805 - DEBUG -  Exiting history()
2025-07-21 21:20:06,805 - DEBUG - Exiting history()
2025-07-21 21:20:06,805 - DEBUG - Entering history()
2025-07-21 21:20:06,806 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['NVDA', 1, 0])
2025-07-21 21:20:06,806 - DEBUG -  Entering history()
2025-07-21 21:20:06,806 - DEBUG - NVDA: Yahoo GET parameters: {'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,806 - DEBUG -   Entering get()
2025-07-21 21:20:06,807 - DEBUG -    Entering _make_request()
2025-07-21 21:20:06,807 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/NVDA
2025-07-21 21:20:06,807 - DEBUG - params={'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,807 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,807 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,807 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,808 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:06,808 - DEBUG - reusing cookie
2025-07-21 21:20:06,808 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:06,808 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:06,808 - DEBUG - reusing crumb
2025-07-21 21:20:06,808 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:06,808 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,808 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,857 - DEBUG - response code=200
2025-07-21 21:20:06,858 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:06,858 - DEBUG -   Exiting get()
2025-07-21 21:20:06,859 - DEBUG - NVDA: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:06,867 - DEBUG - NVDA: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:06,870 - DEBUG - NVDA: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:06,877 - DEBUG - NVDA: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:06,877 - DEBUG -  Exiting history()
2025-07-21 21:20:06,878 - DEBUG - Exiting history()
2025-07-21 21:20:06,878 - DEBUG - Entering history()
2025-07-21 21:20:06,878 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['NVDA', 1, 0])
2025-07-21 21:20:06,879 - DEBUG -  Entering history()
2025-07-21 21:20:06,879 - DEBUG - NVDA: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,879 - DEBUG -   Entering get()
2025-07-21 21:20:06,879 - DEBUG -    Entering _make_request()
2025-07-21 21:20:06,879 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/NVDA
2025-07-21 21:20:06,879 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:06,879 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,880 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,880 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,880 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:06,880 - DEBUG - reusing cookie
2025-07-21 21:20:06,880 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:06,880 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:06,880 - DEBUG - reusing crumb
2025-07-21 21:20:06,880 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:06,880 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,880 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,890 - DEBUG - response code=200
2025-07-21 21:20:06,890 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:06,890 - DEBUG -   Exiting get()
2025-07-21 21:20:06,892 - DEBUG - NVDA: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:06,893 - DEBUG - NVDA: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:00-04:00
2025-07-21 21:20:06,901 - DEBUG - NVDA: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:06,910 - DEBUG - NVDA: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:06,910 - DEBUG -  Exiting history()
2025-07-21 21:20:06,910 - DEBUG - Exiting history()
2025-07-21 21:20:06,910 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/NVDA
2025-07-21 21:20:06,910 - DEBUG - Entering get()
2025-07-21 21:20:06,910 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,911 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/NVDA
2025-07-21 21:20:06,911 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'NVDA'}
2025-07-21 21:20:06,911 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,911 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,911 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,911 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,911 - DEBUG - reusing cookie
2025-07-21 21:20:06,911 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,911 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,912 - DEBUG - reusing crumb
2025-07-21 21:20:06,912 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,912 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,912 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,922 - DEBUG - response code=200
2025-07-21 21:20:06,923 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,923 - DEBUG - Exiting get()
2025-07-21 21:20:06,923 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:06,923 - DEBUG - Entering get()
2025-07-21 21:20:06,923 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,923 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:06,924 - DEBUG - params={'symbols': 'NVDA', 'formatted': 'false'}
2025-07-21 21:20:06,924 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,924 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,924 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,924 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,924 - DEBUG - reusing cookie
2025-07-21 21:20:06,924 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,924 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,924 - DEBUG - reusing crumb
2025-07-21 21:20:06,924 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,925 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,925 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,932 - DEBUG - response code=200
2025-07-21 21:20:06,932 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,932 - DEBUG - Exiting get()
2025-07-21 21:20:06,937 - DEBUG - Entering get()
2025-07-21 21:20:06,937 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,937 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/NVDA
2025-07-21 21:20:06,937 - DEBUG - params=None
2025-07-21 21:20:06,937 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,937 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,937 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,937 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,938 - DEBUG - reusing cookie
2025-07-21 21:20:06,938 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,938 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,938 - DEBUG - reusing crumb
2025-07-21 21:20:06,938 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,938 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,938 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,950 - DEBUG - response code=200
2025-07-21 21:20:06,951 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,951 - DEBUG - Exiting get()
2025-07-21 21:20:06,953 - DEBUG - Entering get()
2025-07-21 21:20:06,953 - DEBUG -  Entering _make_request()
2025-07-21 21:20:06,954 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/NVDA?date=1753401600
2025-07-21 21:20:06,954 - DEBUG - params=None
2025-07-21 21:20:06,955 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:06,955 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:06,955 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,956 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:06,956 - DEBUG - reusing cookie
2025-07-21 21:20:06,956 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:06,956 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:06,957 - DEBUG - reusing crumb
2025-07-21 21:20:06,957 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:06,957 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:06,957 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:06,972 - DEBUG - response code=200
2025-07-21 21:20:06,972 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:06,972 - DEBUG - Exiting get()
2025-07-21 21:20:07,046 - DEBUG - Starting new HTTPS connection (1): www.alphavantage.co:443
2025-07-21 21:20:07,087 - DEBUG - https://www.alphavantage.co:443 "GET /query?function=EARNINGS_CALENDAR&horizon=3month&apikey=your_alpha_vantage_key_here HTTP/1.1" 200 72
2025-07-21 21:20:07,087 - DEBUG - Encoding detection: ascii is most likely the one.
2025-07-21 21:20:07,088 - INFO - ✅ Enhanced signals generated: 0
2025-07-21 21:20:07,088 - INFO - 
============================================================
2025-07-21 21:20:07,088 - INFO - DEBUGGING GOOGL
2025-07-21 21:20:07,088 - INFO - ============================================================
2025-07-21 21:20:07,088 - INFO - 
🔍 DEBUGGING BASIC SIGNAL GENERATION FOR GOOGL
2025-07-21 21:20:07,088 - INFO - ==================================================
2025-07-21 21:20:07,089 - INFO - 
🔍 DEBUGGING MARKET DATA FOR GOOGL
2025-07-21 21:20:07,089 - INFO - ==================================================
2025-07-21 21:20:07,089 - DEBUG - Entering history()
2025-07-21 21:20:07,089 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-07-21 21:20:07,090 - DEBUG -  Entering history()
2025-07-21 21:20:07,090 - DEBUG - GOOGL: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,090 - DEBUG -   Entering get()
2025-07-21 21:20:07,090 - DEBUG -    Entering _make_request()
2025-07-21 21:20:07,090 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-07-21 21:20:07,090 - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,090 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,091 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,091 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,091 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:07,091 - DEBUG - reusing cookie
2025-07-21 21:20:07,091 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:07,091 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:07,091 - DEBUG - reusing crumb
2025-07-21 21:20:07,091 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:07,091 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,092 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,138 - DEBUG - response code=200
2025-07-21 21:20:07,138 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:07,138 - DEBUG -   Exiting get()
2025-07-21 21:20:07,140 - DEBUG - GOOGL: yfinance received OHLC data: 2025-06-23 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:07,141 - DEBUG - GOOGL: OHLC after cleaning: 2025-06-23 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:07,148 - DEBUG - GOOGL: OHLC after combining events: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:07,160 - DEBUG - GOOGL: yfinance returning OHLC: 2025-06-23 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:07,160 - DEBUG -  Exiting history()
2025-07-21 21:20:07,161 - DEBUG - Exiting history()
2025-07-21 21:20:07,161 - INFO - ✅ Basic stock data: (20, 7)
2025-07-21 21:20:07,161 - INFO -    Date range: 2025-06-23 00:00:00-04:00 to 2025-07-21 00:00:00-04:00
2025-07-21 21:20:07,161 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-07-21 21:20:07,161 - INFO -    Last close: $190.10
2025-07-21 21:20:07,162 - INFO -    Volume: 45,398,571
2025-07-21 21:20:07,162 - DEBUG - Entering history()
2025-07-21 21:20:07,162 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-07-21 21:20:07,163 - DEBUG -  Entering history()
2025-07-21 21:20:07,163 - DEBUG - GOOGL: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,163 - DEBUG -   Entering get()
2025-07-21 21:20:07,163 - DEBUG -    Entering _make_request()
2025-07-21 21:20:07,163 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-07-21 21:20:07,163 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,163 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,163 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,164 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,164 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:07,164 - DEBUG - reusing cookie
2025-07-21 21:20:07,164 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:07,164 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:07,164 - DEBUG - reusing crumb
2025-07-21 21:20:07,164 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:07,164 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,164 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,208 - DEBUG - response code=200
2025-07-21 21:20:07,208 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:07,208 - DEBUG -   Exiting get()
2025-07-21 21:20:07,209 - DEBUG - GOOGL: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:07,211 - DEBUG - GOOGL: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:07,217 - DEBUG - GOOGL: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:07,224 - DEBUG - GOOGL: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:07,224 - DEBUG -  Exiting history()
2025-07-21 21:20:07,224 - DEBUG - Exiting history()
2025-07-21 21:20:07,225 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/GOOGL
2025-07-21 21:20:07,225 - DEBUG - Entering get()
2025-07-21 21:20:07,225 - DEBUG -  Entering _make_request()
2025-07-21 21:20:07,225 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/GOOGL
2025-07-21 21:20:07,225 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'GOOGL'}
2025-07-21 21:20:07,225 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,225 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,225 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,226 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:07,226 - DEBUG - reusing cookie
2025-07-21 21:20:07,226 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:07,226 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:07,226 - DEBUG - reusing crumb
2025-07-21 21:20:07,226 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:07,226 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,226 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,283 - DEBUG - response code=200
2025-07-21 21:20:07,283 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:07,283 - DEBUG - Exiting get()
2025-07-21 21:20:07,284 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:07,284 - DEBUG - Entering get()
2025-07-21 21:20:07,284 - DEBUG -  Entering _make_request()
2025-07-21 21:20:07,285 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:07,285 - DEBUG - params={'symbols': 'GOOGL', 'formatted': 'false'}
2025-07-21 21:20:07,285 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,285 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,286 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,286 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:07,286 - DEBUG - reusing cookie
2025-07-21 21:20:07,287 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:07,287 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:07,287 - DEBUG - reusing crumb
2025-07-21 21:20:07,287 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:07,287 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,287 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,339 - DEBUG - response code=200
2025-07-21 21:20:07,339 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:07,339 - DEBUG - Exiting get()
2025-07-21 21:20:07,340 - DEBUG - Entering get()
2025-07-21 21:20:07,340 - DEBUG -  Entering _make_request()
2025-07-21 21:20:07,340 - DEBUG - url=https://query1.finance.yahoo.com/ws/fundamentals-timeseries/v1/finance/timeseries/GOOGL?symbol=GOOGL&type=trailingPegRatio&period1=1737417600&period2=1753228800
2025-07-21 21:20:07,341 - DEBUG - params=None
2025-07-21 21:20:07,342 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,342 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,342 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,342 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:07,342 - DEBUG - reusing cookie
2025-07-21 21:20:07,342 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:07,342 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:07,342 - DEBUG - reusing crumb
2025-07-21 21:20:07,344 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:07,344 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,344 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,412 - DEBUG - response code=200
2025-07-21 21:20:07,413 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:07,413 - DEBUG - Exiting get()
2025-07-21 21:20:07,416 - DEBUG - Entering get()
2025-07-21 21:20:07,416 - DEBUG -  Entering _make_request()
2025-07-21 21:20:07,417 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/GOOGL
2025-07-21 21:20:07,417 - DEBUG - params=None
2025-07-21 21:20:07,417 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,417 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,417 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,417 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:07,417 - DEBUG - reusing cookie
2025-07-21 21:20:07,417 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:07,417 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:07,418 - DEBUG - reusing crumb
2025-07-21 21:20:07,418 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:07,418 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,418 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,476 - DEBUG - response code=200
2025-07-21 21:20:07,476 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:07,476 - DEBUG - Exiting get()
2025-07-21 21:20:07,477 - DEBUG - Entering get()
2025-07-21 21:20:07,477 - DEBUG -  Entering _make_request()
2025-07-21 21:20:07,478 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/GOOGL?date=1753401600
2025-07-21 21:20:07,478 - DEBUG - params=None
2025-07-21 21:20:07,478 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,478 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,478 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,478 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:07,478 - DEBUG - reusing cookie
2025-07-21 21:20:07,478 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:07,478 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:07,478 - DEBUG - reusing crumb
2025-07-21 21:20:07,479 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:07,479 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,479 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,535 - DEBUG - response code=200
2025-07-21 21:20:07,536 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:07,536 - DEBUG - Exiting get()
2025-07-21 21:20:07,562 - INFO - ✅ Comprehensive data keys: ['symbol', 'timestamp', 'price_data', 'volume_analysis', 'options_flow', 'historical_data', 'data_quality']
2025-07-21 21:20:07,566 - INFO -    Historical data length: 50
2025-07-21 21:20:07,566 - INFO -    Sample data point: {'Open': 154.81259075606965, 'High': 155.74145898524296, 'Low': 152.715123752916, 'Close': 154.0934600830078, 'Volume': 57498700, 'Dividends': 0.0, 'Stock Splits': 0.0}
2025-07-21 21:20:07,566 - INFO -    Data quality score: 100
2025-07-21 21:20:07,566 - INFO -    Data issues: []
2025-07-21 21:20:07,569 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/GOOGL
2025-07-21 21:20:07,570 - DEBUG - Entering get()
2025-07-21 21:20:07,570 - DEBUG -  Entering _make_request()
2025-07-21 21:20:07,570 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/GOOGL
2025-07-21 21:20:07,570 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'GOOGL'}
2025-07-21 21:20:07,570 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,570 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,570 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,570 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:07,570 - DEBUG - reusing cookie
2025-07-21 21:20:07,571 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:07,571 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:07,571 - DEBUG - reusing crumb
2025-07-21 21:20:07,571 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:07,571 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,571 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,580 - DEBUG - response code=200
2025-07-21 21:20:07,583 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:07,583 - DEBUG - Exiting get()
2025-07-21 21:20:07,583 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:07,583 - DEBUG - Entering get()
2025-07-21 21:20:07,584 - DEBUG -  Entering _make_request()
2025-07-21 21:20:07,584 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:07,585 - DEBUG - params={'symbols': 'GOOGL', 'formatted': 'false'}
2025-07-21 21:20:07,585 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,586 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,586 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,587 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:07,587 - DEBUG - reusing cookie
2025-07-21 21:20:07,590 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:07,591 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:07,591 - DEBUG - reusing crumb
2025-07-21 21:20:07,591 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:07,591 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,591 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,600 - DEBUG - response code=200
2025-07-21 21:20:07,600 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:07,602 - DEBUG - Exiting get()
2025-07-21 21:20:07,603 - INFO - ✅ Real-time data: True
2025-07-21 21:20:07,605 - INFO -    Current price: $190.1
2025-07-21 21:20:07,605 - INFO -    Volume: 45,398,571
2025-07-21 21:20:07,605 - INFO -    Beta: 1.014
2025-07-21 21:20:07,606 - INFO - ✅ Historical DataFrame: (50, 7)
2025-07-21 21:20:07,610 - INFO - 
🔍 DEBUGGING TECHNICAL INDICATORS FOR GOOGL
2025-07-21 21:20:07,610 - INFO - ==================================================
2025-07-21 21:20:07,712 - INFO - ✅ Technical indicators shape: (50, 66)
2025-07-21 21:20:07,717 - INFO -    Columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits', 'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'SMA_200', 'EMA_5', 'EMA_10', 'EMA_20', 'EMA_50', 'SMA_Cross_5_20', 'SMA_Cross_20_50', 'EMA_Cross_5_20', 'RSI', 'RSI_Oversold', 'RSI_Overbought', 'MACD', 'MACD_Signal', 'MACD_Histogram', 'MACD_Bullish', 'Stoch_K', 'Stoch_D', 'Williams_R', 'ROC', 'BB_Upper', 'BB_Middle', 'BB_Lower', 'BB_Width', 'BB_Position', 'ATR', 'ATR_Percent', 'KC_Upper', 'KC_Middle', 'KC_Lower', 'DC_Upper', 'DC_Lower', 'DC_Middle', 'Volume_SMA_20', 'Volume_Ratio', 'High_Volume', 'OBV', 'VPT', 'ADL', 'CMF', 'ADX', 'ADX_Strong_Trend', 'PSAR', 'CCI', 'DPO', 'Price_Change_1D', 'Price_Change_5D', 'Price_Change_20D', 'ATR_Expansion', 'Vol_Expansion', 'Breakout_High', 'Breakout_Low', 'Resistance_20', 'Support_20', 'Near_Resistance', 'Near_Support']
2025-07-21 21:20:07,717 - INFO -    RSI: 73.37962780312228
2025-07-21 21:20:07,717 - INFO -    MACD: 3.9943939298605073
2025-07-21 21:20:07,717 - INFO -    BB_upper: N/A
2025-07-21 21:20:07,718 - INFO -    SMA_20: 177.78999862670898
2025-07-21 21:20:07,718 - INFO -    Volume_SMA: N/A
2025-07-21 21:20:07,718 - INFO - 
🎯 Testing signal generation methods...
2025-07-21 21:20:07,719 - INFO - ✅ Momentum signals: 0
2025-07-21 21:20:07,720 - INFO - ✅ Breakout signals: 0
2025-07-21 21:20:07,720 - INFO - ✅ Reversal signals: 0
2025-07-21 21:20:07,721 - INFO - 
📊 Total basic signals generated: 0
2025-07-21 21:20:07,721 - INFO - 
🔍 DEBUGGING ENHANCED SIGNAL GENERATION FOR GOOGL
2025-07-21 21:20:07,722 - INFO - ==================================================
2025-07-21 21:20:07,722 - DEBUG - Entering history()
2025-07-21 21:20:07,723 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-07-21 21:20:07,728 - DEBUG -  Entering history()
2025-07-21 21:20:07,728 - DEBUG - GOOGL: Yahoo GET parameters: {'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,728 - DEBUG -   Entering get()
2025-07-21 21:20:07,728 - DEBUG -    Entering _make_request()
2025-07-21 21:20:07,728 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-07-21 21:20:07,728 - DEBUG - params={'range': '1d', 'interval': '5m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,729 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,729 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,729 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,729 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:07,729 - DEBUG - reusing cookie
2025-07-21 21:20:07,729 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:07,729 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:07,729 - DEBUG - reusing crumb
2025-07-21 21:20:07,729 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:07,729 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,730 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,809 - DEBUG - response code=200
2025-07-21 21:20:07,813 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:07,813 - DEBUG -   Exiting get()
2025-07-21 21:20:07,814 - DEBUG - GOOGL: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:07,827 - DEBUG - GOOGL: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:07,836 - DEBUG - GOOGL: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:07,845 - DEBUG - GOOGL: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:55:00-04:00
2025-07-21 21:20:07,846 - DEBUG -  Exiting history()
2025-07-21 21:20:07,846 - DEBUG - Exiting history()
2025-07-21 21:20:07,846 - DEBUG - Entering history()
2025-07-21 21:20:07,847 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-07-21 21:20:07,847 - DEBUG -  Entering history()
2025-07-21 21:20:07,847 - DEBUG - GOOGL: Yahoo GET parameters: {'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,847 - DEBUG -   Entering get()
2025-07-21 21:20:07,847 - DEBUG -    Entering _make_request()
2025-07-21 21:20:07,848 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-07-21 21:20:07,848 - DEBUG - params={'range': '1d', 'interval': '15m', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,848 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,848 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,848 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,848 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:07,848 - DEBUG - reusing cookie
2025-07-21 21:20:07,848 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:07,848 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:07,848 - DEBUG - reusing crumb
2025-07-21 21:20:07,849 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:07,849 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,849 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,893 - DEBUG - response code=200
2025-07-21 21:20:07,894 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:07,894 - DEBUG -   Exiting get()
2025-07-21 21:20:07,896 - DEBUG - GOOGL: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:07,909 - DEBUG - GOOGL: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:07,916 - DEBUG - GOOGL: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:07,931 - DEBUG - GOOGL: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:45:00-04:00
2025-07-21 21:20:07,931 - DEBUG -  Exiting history()
2025-07-21 21:20:07,931 - DEBUG - Exiting history()
2025-07-21 21:20:07,931 - DEBUG - Entering history()
2025-07-21 21:20:07,932 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-07-21 21:20:07,932 - DEBUG -  Entering history()
2025-07-21 21:20:07,933 - DEBUG - GOOGL: Yahoo GET parameters: {'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,934 - DEBUG -   Entering get()
2025-07-21 21:20:07,934 - DEBUG -    Entering _make_request()
2025-07-21 21:20:07,934 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-07-21 21:20:07,934 - DEBUG - params={'range': '1d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:07,934 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:07,934 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:07,934 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,934 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:07,934 - DEBUG - reusing cookie
2025-07-21 21:20:07,937 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:07,937 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:07,937 - DEBUG - reusing crumb
2025-07-21 21:20:07,937 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:07,937 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:07,937 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:07,988 - DEBUG - response code=200
2025-07-21 21:20:07,989 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:07,989 - DEBUG -   Exiting get()
2025-07-21 21:20:07,991 - DEBUG - GOOGL: yfinance received OHLC data: 2025-07-21 13:30:00 -> 2025-07-21 20:00:00
2025-07-21 21:20:07,999 - DEBUG - GOOGL: OHLC after cleaning: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:08,002 - DEBUG - GOOGL: OHLC after combining events: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:08,010 - DEBUG - GOOGL: yfinance returning OHLC: 2025-07-21 09:30:00-04:00 -> 2025-07-21 15:30:00-04:00
2025-07-21 21:20:08,010 - DEBUG -  Exiting history()
2025-07-21 21:20:08,010 - DEBUG - Exiting history()
2025-07-21 21:20:08,011 - DEBUG - Entering history()
2025-07-21 21:20:08,011 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-07-21 21:20:08,011 - DEBUG -  Entering history()
2025-07-21 21:20:08,011 - DEBUG - GOOGL: Yahoo GET parameters: {'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:08,012 - DEBUG -   Entering get()
2025-07-21 21:20:08,012 - DEBUG -    Entering _make_request()
2025-07-21 21:20:08,012 - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-07-21 21:20:08,012 - DEBUG - params={'range': '3mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-07-21 21:20:08,012 - DEBUG -     Entering _get_cookie_and_crumb()
2025-07-21 21:20:08,012 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:08,012 - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,014 - DEBUG -       Entering _get_cookie_basic()
2025-07-21 21:20:08,014 - DEBUG - reusing cookie
2025-07-21 21:20:08,014 - DEBUG -       Exiting _get_cookie_basic()
2025-07-21 21:20:08,014 - DEBUG -       Entering _get_crumb_basic()
2025-07-21 21:20:08,014 - DEBUG - reusing crumb
2025-07-21 21:20:08,014 - DEBUG -       Exiting _get_crumb_basic()
2025-07-21 21:20:08,014 - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,014 - DEBUG -     Exiting _get_cookie_and_crumb()
2025-07-21 21:20:08,024 - DEBUG - response code=200
2025-07-21 21:20:08,024 - DEBUG -    Exiting _make_request()
2025-07-21 21:20:08,025 - DEBUG -   Exiting get()
2025-07-21 21:20:08,026 - DEBUG - GOOGL: yfinance received OHLC data: 2025-04-22 13:30:00 -> 2025-07-21 20:00:01
2025-07-21 21:20:08,030 - DEBUG - GOOGL: OHLC after cleaning: 2025-04-22 09:30:00-04:00 -> 2025-07-21 16:00:01-04:00
2025-07-21 21:20:08,037 - DEBUG - GOOGL: OHLC after combining events: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:08,055 - DEBUG - GOOGL: yfinance returning OHLC: 2025-04-22 00:00:00-04:00 -> 2025-07-21 00:00:00-04:00
2025-07-21 21:20:08,055 - DEBUG -  Exiting history()
2025-07-21 21:20:08,055 - DEBUG - Exiting history()
2025-07-21 21:20:08,055 - DEBUG - get_raw_json(): https://query2.finance.yahoo.com/v10/finance/quoteSummary/GOOGL
2025-07-21 21:20:08,056 - DEBUG - Entering get()
2025-07-21 21:20:08,056 - DEBUG -  Entering _make_request()
2025-07-21 21:20:08,056 - DEBUG - url=https://query2.finance.yahoo.com/v10/finance/quoteSummary/GOOGL
2025-07-21 21:20:08,057 - DEBUG - params={'modules': 'financialData,quoteType,defaultKeyStatistics,assetProfile,summaryDetail', 'corsDomain': 'finance.yahoo.com', 'formatted': 'false', 'symbol': 'GOOGL'}
2025-07-21 21:20:08,057 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:08,058 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:08,058 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,058 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:08,058 - DEBUG - reusing cookie
2025-07-21 21:20:08,058 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:08,058 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:08,058 - DEBUG - reusing crumb
2025-07-21 21:20:08,058 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:08,058 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,059 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:08,116 - DEBUG - response code=200
2025-07-21 21:20:08,116 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:08,116 - DEBUG - Exiting get()
2025-07-21 21:20:08,116 - DEBUG - get_raw_json(): https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:08,117 - DEBUG - Entering get()
2025-07-21 21:20:08,117 - DEBUG -  Entering _make_request()
2025-07-21 21:20:08,117 - DEBUG - url=https://query1.finance.yahoo.com/v7/finance/quote?
2025-07-21 21:20:08,117 - DEBUG - params={'symbols': 'GOOGL', 'formatted': 'false'}
2025-07-21 21:20:08,117 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:08,117 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:08,117 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,117 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:08,117 - DEBUG - reusing cookie
2025-07-21 21:20:08,118 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:08,118 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:08,118 - DEBUG - reusing crumb
2025-07-21 21:20:08,118 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:08,118 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,118 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:08,124 - DEBUG - response code=200
2025-07-21 21:20:08,124 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:08,124 - DEBUG - Exiting get()
2025-07-21 21:20:08,129 - DEBUG - Entering get()
2025-07-21 21:20:08,129 - DEBUG -  Entering _make_request()
2025-07-21 21:20:08,129 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/GOOGL
2025-07-21 21:20:08,129 - DEBUG - params=None
2025-07-21 21:20:08,129 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:08,129 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:08,129 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,129 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:08,130 - DEBUG - reusing cookie
2025-07-21 21:20:08,130 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:08,130 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:08,130 - DEBUG - reusing crumb
2025-07-21 21:20:08,130 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:08,130 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,130 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:08,138 - DEBUG - response code=200
2025-07-21 21:20:08,142 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:08,142 - DEBUG - Exiting get()
2025-07-21 21:20:08,143 - DEBUG - Entering get()
2025-07-21 21:20:08,143 - DEBUG -  Entering _make_request()
2025-07-21 21:20:08,143 - DEBUG - url=https://query2.finance.yahoo.com/v7/finance/options/GOOGL?date=1753401600
2025-07-21 21:20:08,144 - DEBUG - params=None
2025-07-21 21:20:08,144 - DEBUG -   Entering _get_cookie_and_crumb()
2025-07-21 21:20:08,144 - DEBUG - cookie_mode = 'basic'
2025-07-21 21:20:08,144 - DEBUG -    Entering _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,144 - DEBUG -     Entering _get_cookie_basic()
2025-07-21 21:20:08,144 - DEBUG - reusing cookie
2025-07-21 21:20:08,145 - DEBUG -     Exiting _get_cookie_basic()
2025-07-21 21:20:08,145 - DEBUG -     Entering _get_crumb_basic()
2025-07-21 21:20:08,145 - DEBUG - reusing crumb
2025-07-21 21:20:08,145 - DEBUG -     Exiting _get_crumb_basic()
2025-07-21 21:20:08,145 - DEBUG -    Exiting _get_cookie_and_crumb_basic()
2025-07-21 21:20:08,145 - DEBUG -   Exiting _get_cookie_and_crumb()
2025-07-21 21:20:08,152 - DEBUG - response code=200
2025-07-21 21:20:08,152 - DEBUG -  Exiting _make_request()
2025-07-21 21:20:08,153 - DEBUG - Exiting get()
2025-07-21 21:20:08,223 - DEBUG - Starting new HTTPS connection (1): www.alphavantage.co:443
2025-07-21 21:20:08,274 - DEBUG - https://www.alphavantage.co:443 "GET /query?function=EARNINGS_CALENDAR&horizon=3month&apikey=your_alpha_vantage_key_here HTTP/1.1" 200 72
2025-07-21 21:20:08,275 - DEBUG - Encoding detection: ascii is most likely the one.
2025-07-21 21:20:08,276 - INFO - ✅ Enhanced signals generated: 0
2025-07-21 21:20:08,280 - INFO - 
============================================================
2025-07-21 21:20:08,280 - INFO - SUMMARY
2025-07-21 21:20:08,280 - INFO - ============================================================
2025-07-21 21:20:08,281 - INFO - 📊 Total basic signals: 0
2025-07-21 21:20:08,281 - INFO - 📊 Total enhanced signals: 0
2025-07-21 21:20:08,281 - ERROR - ❌ NO SIGNALS GENERATED AT ALL
2025-07-21 21:20:08,281 - INFO - 
🔍 POSSIBLE REASONS:
2025-07-21 21:20:08,281 - INFO - 1. Market data quality issues
2025-07-21 21:20:08,282 - INFO - 2. Technical indicators not meeting thresholds
2025-07-21 21:20:08,282 - INFO - 3. Signal confidence thresholds too high
2025-07-21 21:20:08,282 - INFO - 4. Market conditions not suitable for signals
2025-07-21 21:20:08,282 - INFO - 5. Time-based filtering (market hours, optimal times)
