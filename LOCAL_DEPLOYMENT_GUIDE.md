# 🚀 AWOT Local Deployment Guide

## Quick Start (5 Minutes)

### 1. Environment Check
```bash
# Ensure you're in the AWOT directory
cd /home/<USER>/Documents/AWOT

# Activate virtual environment
source venv/bin/activate

# Check if everything is ready
python deploy_local.py status
```

### 2. Configure Credentials (Optional for Paper Trading)
```bash
# Edit your .env file with real credentials (optional for paper trading)
nano .env

# For paper trading, you can use the defaults
# For live trading, you MUST configure:
# - ROBINHOOD_USERNAME
# - ROBINHOOD_PASSWORD  
# - ROBINHOOD_TOTP_SECRET (for 2FA)
# - EMAIL settings for notifications
# - TELEGRAM_BOT_TOKEN (optional)
```

### 3. Deploy Platform
```bash
# Deploy in paper trading mode (safe, no real money)
python deploy_local.py deploy paper

# OR deploy in live trading mode (REAL MONEY - BE CAREFUL!)
python deploy_local.py deploy live
```

### 4. Access Your Platform
- **Dashboard**: http://localhost:8502
- **API**: http://localhost:8080
- **API Documentation**: http://localhost:8080/docs

---

## Detailed Setup Instructions

### Prerequisites Verification
```bash
# Check Python version (should be 3.8+)
python --version

# Check virtual environment
which python

# Verify all dependencies
pip list | grep -E "(streamlit|pandas|numpy|plotly|robin-stocks)"
```

### Configuration Options

#### Paper Trading (Recommended for Testing)
- **No real money involved**
- **Safe for testing strategies**
- **Full functionality simulation**
- **No broker credentials required**

#### Live Trading (Real Money)
- **Requires Robinhood account**
- **Needs 2FA setup**
- **Real money at risk**
- **Start with small amounts**

### Environment Variables (.env file)

#### Required for Live Trading:
```bash
# Robinhood Credentials
ROBINHOOD_USERNAME=your_username
ROBINHOOD_PASSWORD=your_password
ROBINHOOD_TOTP_SECRET=your_2fa_secret

# Trading Configuration
PAPER_TRADING=false  # Set to true for paper trading
INITIAL_CAPITAL=50000
RISK_PER_TRADE=0.10
MAX_POSITIONS=3
```

#### Optional (Notifications):
```bash
# Email Notifications
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_TO=<EMAIL>

# Telegram Notifications
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Market Data
ALPHA_VANTAGE_API_KEY=your_api_key
```

---

## Deployment Commands

### Basic Commands
```bash
# Deploy full platform
python deploy_local.py deploy [paper|live]

# Check service status
python deploy_local.py status

# Stop all services
python deploy_local.py stop

# View logs
python deploy_local.py logs [service_name]

# Run tests
python deploy_local.py test
```

### Service Management
```bash
# Check what's running
python deploy_local.py status

# View specific service logs
python deploy_local.py logs dashboard
python deploy_local.py logs api
python deploy_local.py logs trading
python deploy_local.py logs monitoring

# Stop everything
python deploy_local.py stop
```

---

## Platform Components

### 1. Dashboard (Port 8502)
- **Real-time portfolio monitoring**
- **Signal generation and analysis**
- **Risk management interface**
- **Trade execution controls**
- **Performance analytics**

### 2. API Server (Port 8080)
- **RESTful API for programmatic access**
- **Trading engine controls**
- **Portfolio data endpoints**
- **Notification management**
- **System health monitoring**

### 3. Trading Engine
- **Automated signal generation**
- **Order execution and management**
- **Risk monitoring and controls**
- **Position tracking**
- **Emergency stop capabilities**

### 4. Monitoring System
- **System resource monitoring**
- **Service health checks**
- **Performance metrics**
- **Log aggregation**
- **Alert generation**

---

## Safety Features

### Paper Trading Mode
- ✅ **No real money risk**
- ✅ **Realistic market simulation**
- ✅ **Full feature testing**
- ✅ **Performance validation**
- ✅ **Strategy optimization**

### Risk Controls
- ✅ **Position size limits**
- ✅ **Daily trade limits**
- ✅ **Stop-loss protection**
- ✅ **Emergency stop button**
- ✅ **Real-time risk monitoring**

### Security Features
- ✅ **Credential encryption**
- ✅ **2FA support**
- ✅ **Secure API endpoints**
- ✅ **Audit logging**
- ✅ **Input validation**

---

## Monitoring & Maintenance

### Daily Checks
```bash
# Check system status
python deploy_local.py status

# Review performance
curl http://localhost:8080/trading/status

# Check logs for errors
python deploy_local.py logs
```

### Performance Monitoring
- **CPU and memory usage**
- **Trading performance metrics**
- **Signal generation statistics**
- **Order execution rates**
- **Risk compliance tracking**

### Log Files
- `logs/dashboard.log` - Dashboard activity
- `logs/api.log` - API requests and responses
- `logs/trading.log` - Trading engine operations
- `logs/monitoring.log` - System metrics

---

## Troubleshooting

### Common Issues

#### Services Won't Start
```bash
# Check if ports are in use
netstat -tulpn | grep -E "(8502|8080)"

# Kill existing processes
pkill -f streamlit
pkill -f uvicorn

# Restart deployment
python deploy_local.py stop
python deploy_local.py deploy paper
```

#### Dashboard Not Loading
```bash
# Check dashboard logs
python deploy_local.py logs dashboard

# Restart just the dashboard
pkill -f streamlit
python run_dashboard.py
```

#### Trading Engine Issues
```bash
# Check trading logs
python deploy_local.py logs trading

# Verify credentials (for live trading)
grep -E "(USERNAME|PASSWORD)" .env

# Test paper trading mode
python deploy_local.py deploy paper
```

#### API Connection Issues
```bash
# Test API health
curl http://localhost:8080/health

# Check API logs
python deploy_local.py logs api

# Restart API server
pkill -f uvicorn
python -m uvicorn api.main:app --host 0.0.0.0 --port 8080
```

### Getting Help
1. **Check logs** for error messages
2. **Run tests** to validate components
3. **Restart services** if issues persist
4. **Use paper trading** for safe testing
5. **Review configuration** for missing values

---

## Next Steps After Deployment

### 1. Validate Paper Trading (Recommended)
- Test signal generation
- Verify order execution
- Monitor risk controls
- Review performance metrics
- Test emergency stops

### 2. Configure Notifications
- Set up email alerts
- Configure Telegram bot
- Test notification delivery
- Customize alert thresholds

### 3. Strategy Optimization
- Backtest strategies
- Adjust parameters
- Monitor performance
- Optimize risk settings

### 4. Live Trading Preparation
- Start with small positions
- Monitor closely
- Validate all systems
- Have emergency procedures ready

---

## Support & Resources

### Documentation
- **API Docs**: http://localhost:8080/docs
- **Dashboard Help**: Available in the interface
- **Configuration Guide**: This document

### Monitoring URLs
- **Dashboard**: http://localhost:8502
- **API Health**: http://localhost:8080/health
- **API Metrics**: http://localhost:8080/metrics
- **Trading Status**: http://localhost:8080/trading/status

### Emergency Procedures
1. **Stop All Trading**: `python deploy_local.py stop`
2. **Emergency Stop**: Use dashboard emergency button
3. **Close Positions**: Via dashboard or API
4. **Check Logs**: `python deploy_local.py logs`
5. **Contact Support**: Review logs and configuration

---

## 🎯 You're Ready to Trade!

Your AWOT platform is now deployed and ready for operation. Start with paper trading to validate everything works as expected, then gradually move to live trading with appropriate risk controls.

**Remember**: Always start small and monitor closely when transitioning to live trading!
