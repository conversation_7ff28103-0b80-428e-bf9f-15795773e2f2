# AWOT - Automated Weekly Options Trading Platform

## Phase 1 Implementation Complete ✅

An AI-powered algorithmic trading platform designed to identify and execute high-risk, high-reward weekly options trades automatically. This repository contains the complete Phase 1 implementation with core technical analysis, signal generation, and backtesting capabilities.

## 🚀 Features Implemented

### Core Technical Analysis Engine
- **Comprehensive Indicators**: RSI, MACD, Moving Averages, Bollinger Bands, ATR, ADX, and more
- **Options-Specific Metrics**: Volatility expansion, breakout detection, support/resistance levels
- **Pattern Recognition**: Double tops/bottoms, ascending triangles, and other chart patterns
- **Signal Strength Assessment**: Multi-factor scoring system for trade quality

### Advanced Signal Generation
- **Momentum Signals**: RSI oversold/overbought with MACD confirmation
- **Breakout Signals**: High-volume breakouts with trend confirmation
- **Volatility Signals**: Expansion/contraction patterns for straddle strategies
- **Reversal Signals**: Oversold bounces and overbought reversals
- **Event-Driven Signals**: Earnings and economic calendar integration

### Sophisticated Strategy Framework
- **Base Strategy Class**: Extensible framework for different trading approaches
- **Momentum Strategy**: Trend-following with moving average crossovers
- **Breakout Strategy**: Volume-confirmed price breakouts
- **Position Management**: Automated entry/exit with stop-loss and take-profit
- **Performance Tracking**: Real-time P&L and trade statistics

### Comprehensive Risk Management
- **Position Sizing**: Dynamic sizing based on signal quality and volatility
- **Portfolio Risk Controls**: Maximum positions, sector concentration limits
- **Real-Time Monitoring**: Drawdown tracking and time decay alerts
- **Risk Metrics**: Comprehensive scoring with actionable recommendations

### Professional Backtesting Engine
- **Historical Simulation**: Day-by-day trading simulation with realistic constraints
- **Performance Analytics**: Sharpe ratio, Sortino ratio, maximum drawdown
- **Strategy Comparison**: Side-by-side performance analysis
- **Visualization**: Equity curves, drawdown charts, trade distribution

### Enhanced Market Data Pipeline
- **Real-Time Data**: Current prices, volume, and market metrics
- **Options Flow Analysis**: Put/call ratios and unusual activity detection
- **Volume Analysis**: Breakout confirmation and unusual volume alerts
- **Market Sentiment**: VIX levels and sector performance tracking

## 📁 Project Structure

```
AWOT/
├── src/
│   ├── indicators/
│   │   └── technical_indicators.py    # Technical analysis engine
│   ├── signals/
│   │   └── signal_generator.py        # Signal generation logic
│   ├── strategies/
│   │   └── strategy.py                # Trading strategies framework
│   ├── backtesting/
│   │   └── backtesting.py             # Backtesting engine
│   ├── risk/
│   │   └── risk_manager.py            # Risk management system
│   └── data/
│       └── market_data.py             # Market data provider
├── tests/
│   ├── test_technical_indicators.py   # Technical indicators tests
│   ├── test_signal_generator.py       # Signal generation tests
│   └── test_risk_manager.py           # Risk management tests
├── config/
│   └── settings.py                    # Configuration settings
├── requirements.txt                   # Python dependencies
└── README.md                          # This file
```

## 🛠️ Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd AWOT
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Configure environment variables**:
Create a `.env` file in the root directory:
```env
ROBINHOOD_USERNAME=your_username
ROBINHOOD_PASSWORD=your_password
ALPHA_VANTAGE_API_KEY=your_api_key
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

## 📊 Usage Examples

### Basic Technical Analysis

```python
from src.indicators.technical_indicators import TechnicalIndicators
from src.data.market_data import MarketDataProvider

# Initialize components
indicators = TechnicalIndicators()
market_data = MarketDataProvider()

# Get stock data and calculate indicators
data = market_data.get_stock_data("AAPL", period="3mo")
analysis = indicators.calculate_all_indicators(data)

# Get signal strength
signal_strength = indicators.get_signal_strength(analysis)
print(f"Overall signal strength: {signal_strength['overall']:.2f}")

# Detect patterns
patterns = indicators.detect_patterns(analysis)
for pattern in patterns:
    print(f"Pattern: {pattern['pattern']} - Confidence: {pattern['confidence']:.2f}")
```

### Signal Generation

```python
from src.signals.signal_generator import SignalGenerator

# Initialize signal generator
signal_gen = SignalGenerator()

# Generate signals for a symbol
signals = signal_gen.generate_signals("AAPL")

for signal in signals:
    print(f"Signal: {signal.signal_type.value}")
    print(f"Confidence: {signal.confidence:.2f}")
    print(f"Entry: ${signal.entry_price:.2f}")
    print(f"Target: ${signal.target_price:.2f}")
    print(f"Stop Loss: ${signal.stop_loss_price:.2f}")
    print(f"Risk/Reward: {signal.risk_reward_ratio:.2f}")
    print("---")
```

### Strategy Backtesting

```python
from src.strategies.strategy import MomentumStrategy
from src.backtesting.backtesting import BacktestEngine

# Initialize strategy and backtesting engine
strategy = MomentumStrategy()
backtest_engine = BacktestEngine()

# Run backtest
symbols = ["AAPL", "TSLA", "MSFT", "NVDA", "GOOGL"]
result = backtest_engine.run_backtest(
    strategy=strategy,
    symbols=symbols,
    start_date="2023-01-01",
    end_date="2023-12-31",
    initial_capital=50000
)

# Print results
print(backtest_engine.generate_backtest_report(result))

# Plot results
backtest_engine.plot_backtest_results(result)
```

### Risk Management

```python
from src.risk.risk_manager import RiskManager
from src.strategies.strategy import Position, PositionType

# Initialize risk manager
risk_manager = RiskManager(initial_capital=100000)

# Calculate position size for a signal
position_info = risk_manager.calculate_position_size(
    signal=signal,
    current_price=5.0,
    portfolio_value=100000,
    existing_positions=[]
)

print(f"Recommended position size: {position_info['position_size']} contracts")
print(f"Risk amount: ${position_info['risk_amount']:.2f}")
print(f"Risk percentage: {position_info['risk_percentage']:.2%}")

# Assess portfolio risk
positions = [...]  # Your current positions
risk_summary = risk_manager.get_risk_summary(positions, 100000)

print(f"Overall risk score: {risk_summary['overall_risk_score']:.1f}/100")
print(f"Risk level: {risk_summary['overall_risk_level']}")

for alert in risk_summary['alerts']:
    print(f"⚠️ {alert['message']}")
```

### Market Data Analysis

```python
from src.data.market_data import MarketDataProvider

market_data = MarketDataProvider()

# Get comprehensive market data
data = market_data.get_comprehensive_market_data("AAPL")

# Analyze volume patterns
volume_analysis = market_data.analyze_volume_profile(historical_data)
if volume_analysis['unusual_volume']:
    print(f"Unusual volume detected: {volume_analysis['volume_ratio']:.1f}x normal")

# Get options flow
options_flow = market_data.get_options_flow_data("AAPL")
print(f"Put/Call Ratio: {options_flow['pc_ratio_volume']:.2f}")

# Scan for breakout candidates
candidates = market_data.scan_for_breakout_candidates(
    symbols=["AAPL", "TSLA", "MSFT"],
    min_volume_ratio=2.0
)

for candidate in candidates:
    print(f"{candidate['symbol']}: Score {candidate['breakout_score']:.1f}")
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test modules
python -m pytest tests/test_technical_indicators.py -v
python -m pytest tests/test_signal_generator.py -v
python -m pytest tests/test_risk_manager.py -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

## ⚙️ Configuration

Key configuration parameters in `config/settings.py`:

```python
# Trading Configuration
INITIAL_CAPITAL = 50000          # Starting capital
RISK_PER_TRADE = 0.10           # 10% risk per trade
MAX_POSITIONS = 3               # Maximum concurrent positions

# Technical Analysis
RSI_OVERSOLD = 30               # RSI oversold threshold
RSI_OVERBOUGHT = 70             # RSI overbought threshold
BREAKOUT_VOLUME_THRESHOLD = 2.0 # Volume breakout threshold

# Risk Management
PROFIT_TARGET = 1.0             # 100% profit target
STOP_LOSS = 0.5                 # 50% stop loss
```

## 📈 Performance Metrics

The backtesting engine provides comprehensive performance analysis:

- **Return Metrics**: Total return, annualized return, CAGR
- **Risk Metrics**: Maximum drawdown, volatility, VaR
- **Risk-Adjusted Returns**: Sharpe ratio, Sortino ratio, Calmar ratio
- **Trade Statistics**: Win rate, average win/loss, profit factor
- **Advanced Analytics**: Beta, alpha, information ratio

## 🔄 Next Steps (Phase 2+)

Phase 1 provides the foundation for:

1. **Live Trading Integration** (Phase 2)
   - Robinhood API integration
   - Real-time order execution
   - Position monitoring

2. **Advanced Strategies** (Phase 3)
   - Machine learning models
   - Multi-timeframe analysis
   - Options Greeks integration

3. **Risk & Portfolio Management** (Phase 4)
   - Dynamic hedging
   - Portfolio optimization
   - Real-time risk monitoring

4. **User Interface** (Phase 5)
   - Web dashboard
   - Mobile notifications
   - Performance reporting

## 📝 License

This project is for educational and research purposes. Please ensure compliance with all applicable trading regulations and broker terms of service.

## ⚠️ Disclaimer

This software is for educational purposes only. Trading options involves substantial risk and is not suitable for all investors. Past performance does not guarantee future results. Always conduct your own research and consider consulting with a financial advisor before making investment decisions.
